# Master Migration Script: Move to Centralized Docker Share
# This script orchestrates the complete migration from relative paths to centralized Docker share

param(
    [string]$ShareRoot = "C:\Docker_Share",
    [switch]$Force = $false,
    [switch]$SkipBackups = $false
)

Write-Host "N8N Builder: Centralized Docker Share Migration" -ForegroundColor Cyan
Write-Host "=" * 60
Write-Host "This script will migrate your N8N project to use a centralized Docker share" -ForegroundColor Yellow
Write-Host "Location: $ShareRoot" -ForegroundColor White

if (-not $Force) {
    $confirm = Read-Host "`nProceed with migration? (y/N)"
    if ($confirm -ne 'y' -and $confirm -ne 'Y') {
        Write-Host "Migration cancelled by user" -ForegroundColor Yellow
        exit 0
    }
}

# Step 1: Setup centralized directory structure
Write-Host "`nSTEP 1: Setting up centralized directory structure..." -ForegroundColor Magenta
try {
    $setupParams = @{
        ShareRoot = $ShareRoot
        Force = $Force
    }
    & ".\Scripts\Setup-Centralized-Docker-Share.ps1" @setupParams
    Write-Host "Step 1 Complete: Directory structure created" -ForegroundColor Green
} catch {
    Write-Host "Step 1 Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Update Docker Compose files
Write-Host "`nSTEP 2: Updating Docker Compose configuration..." -ForegroundColor Magenta
try {
    $composeParams = @{
        ShareRoot = $ShareRoot
        Backup = (-not $SkipBackups)
    }
    & ".\Scripts\Update-Docker-Compose-Paths.ps1" @composeParams
    Write-Host "Step 2 Complete: Docker Compose updated" -ForegroundColor Green
} catch {
    Write-Host "Step 2 Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 3: Update N8N workflow files
Write-Host "`nSTEP 3: Updating N8N workflow files..." -ForegroundColor Magenta
try {
    $workflowParams = @{
        Backup = (-not $SkipBackups)
        DryRun = $false
    }
    & ".\Scripts\Update-N8N-Workflow-Paths.ps1" @workflowParams
    Write-Host "Step 3 Complete: Workflow files updated" -ForegroundColor Green
} catch {
    Write-Host "Step 3 Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 4: Stop existing Docker containers
Write-Host "`nSTEP 4: Stopping existing Docker containers..." -ForegroundColor Magenta
try {
    Set-Location "n8n-docker"
    docker-compose down --remove-orphans
    Write-Host "Step 4 Complete: Containers stopped" -ForegroundColor Green
} catch {
    Write-Host "Step 4 Warning: Could not stop containers (they may not be running)" -ForegroundColor Yellow
} finally {
    Set-Location ".."
}

# Step 5: Start containers with new configuration
Write-Host "`nSTEP 5: Starting containers with new configuration..." -ForegroundColor Magenta
try {
    Set-Location "n8n-docker"
    docker-compose up -d
    Start-Sleep -Seconds 10
    Write-Host "Step 5 Complete: Containers started" -ForegroundColor Green
} catch {
    Write-Host "Step 5 Failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "You may need to start containers manually" -ForegroundColor Yellow
} finally {
    Set-Location ".."
}

# Step 6: Validate the migration
Write-Host "`nSTEP 6: Validating migration..." -ForegroundColor Magenta
try {
    & ".\Scripts\Validate-Docker-Share.ps1"
    Write-Host "Step 6 Complete: Validation finished" -ForegroundColor Green
} catch {
    Write-Host "Step 6 Warning: Validation script failed" -ForegroundColor Yellow
}

# Create migration summary
$summaryContent = @"
# N8N Builder Migration Summary
Migration completed: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

## Changes Made

### 1. Directory Structure
- Created: $ShareRoot
- Created: $ShareRoot\N8N (project-specific files)
- Created: $ShareRoot\Common (shared resources)
- Migrated DNS reports to centralized location

### 2. Docker Configuration
- Updated docker-compose.yml volume mounts
- Changed from: ../data:/home/<USER>/shared
- Changed to: $ShareRoot\N8N:/home/<USER>/shared
- Added: $ShareRoot\Common:/home/<USER>/common

### 3. Workflow Files
- Updated N8N workflow JSON files
- Fixed hardcoded paths to use container paths
- All file operations now use /home/<USER>/shared/ prefix

## Benefits Achieved

- **Portability**: Project can be moved without breaking file paths
- **Scalability**: Easy to add new projects with their own Docker shares
- **Organization**: Clear separation of project vs shared resources
- **Maintainability**: Centralized location for all Docker file operations

## File Locations

| Purpose | Host Path | Container Path |
|---------|-----------|----------------|
| DNS Reports | $ShareRoot\N8N\dns_reports | /home/<USER>/shared/dns_reports |
| N8N Workflows | $ShareRoot\N8N\workflows | /home/<USER>/shared/workflows |
| Logs | $ShareRoot\N8N\logs | /home/<USER>/shared/logs |
| Common Resources | $ShareRoot\Common | /home/<USER>/common |

## Next Steps

1. Test your DNS security workflow in N8N
2. Verify file operations work correctly
3. Update any documentation that references old paths
4. Consider adding other projects to the centralized structure

## Troubleshooting

If you encounter issues:
1. Check Docker container logs: docker-compose logs n8n
2. Verify directory permissions on $ShareRoot
3. Ensure containers restarted properly
4. Test file creation: docker-compose exec n8n touch /home/<USER>/shared/test.txt

## Rollback (if needed)

Backup files were created with timestamps. To rollback:
1. Restore docker-compose.yml from backup
2. Restore workflow files from backups
3. Restart containers
"@

$summaryPath = "data\migration_summary_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
$summaryContent | Out-File -FilePath $summaryPath -Encoding UTF8

Write-Host "`nMIGRATION COMPLETE!" -ForegroundColor Green
Write-Host "=" * 60
Write-Host "Summary report: $summaryPath" -ForegroundColor Cyan
Write-Host "N8N Interface: http://localhost:5678" -ForegroundColor Cyan
Write-Host "Docker Share: $ShareRoot" -ForegroundColor Cyan

Write-Host "`nTest Your Setup:" -ForegroundColor Yellow
Write-Host "1. Open N8N at http://localhost:5678" -ForegroundColor White
Write-Host "2. Run your DNS Security Monitor workflow" -ForegroundColor White
Write-Host "3. Check that files are created in $ShareRoot\N8N\dns_reports\" -ForegroundColor White

Write-Host "`nYour N8N project is now portable and scalable!" -ForegroundColor Green
