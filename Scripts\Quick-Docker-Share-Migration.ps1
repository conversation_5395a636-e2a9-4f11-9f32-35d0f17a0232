# Quick Docker Share Migration - Simplified Version
# This script performs the essential migration steps without the problematic workflow updates

param(
    [string]$ShareRoot = "C:\Docker_Share",
    [switch]$Force = $false
)

Write-Host "N8N Builder: Quick Docker Share Migration" -ForegroundColor Cyan
Write-Host "=" * 50
Write-Host "This script will migrate your N8N project to use a centralized Docker share" -ForegroundColor Yellow
Write-Host "Location: $ShareRoot" -ForegroundColor White

if (-not $Force) {
    $confirm = Read-Host "`nProceed with migration? (y/N)"
    if ($confirm -ne 'y' -and $confirm -ne 'Y') {
        Write-Host "Migration cancelled by user" -ForegroundColor Yellow
        exit 0
    }
}

# Step 1: Setup centralized directory structure
Write-Host "`nSTEP 1: Setting up centralized directory structure..." -ForegroundColor Magenta
try {
    & ".\Scripts\Setup-Centralized-Docker-Share.ps1" -ShareRoot $ShareRoot -Force:$Force
    Write-Host "Step 1 Complete: Directory structure created" -ForegroundColor Green
} catch {
    Write-Host "Step 1 Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Update Docker Compose files
Write-Host "`nSTEP 2: Updating Docker Compose configuration..." -ForegroundColor Magenta
try {
    & ".\Scripts\Update-Docker-Compose-Paths.ps1" -ShareRoot $ShareRoot -Backup
    Write-Host "Step 2 Complete: Docker Compose updated" -ForegroundColor Green
} catch {
    Write-Host "Step 2 Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 3: Stop existing Docker containers
Write-Host "`nSTEP 3: Stopping existing Docker containers..." -ForegroundColor Magenta
try {
    Set-Location "n8n-docker"
    docker-compose down --remove-orphans
    Write-Host "Step 3 Complete: Containers stopped" -ForegroundColor Green
} catch {
    Write-Host "Step 3 Warning: Could not stop containers (they may not be running)" -ForegroundColor Yellow
} finally {
    Set-Location ".."
}

# Step 4: Start containers with new configuration
Write-Host "`nSTEP 4: Starting containers with new configuration..." -ForegroundColor Magenta
try {
    Set-Location "n8n-docker"
    docker-compose up -d
    Start-Sleep -Seconds 10
    Write-Host "Step 4 Complete: Containers started" -ForegroundColor Green
} catch {
    Write-Host "Step 4 Failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "You may need to start containers manually" -ForegroundColor Yellow
} finally {
    Set-Location ".."
}

# Step 5: Test the setup
Write-Host "`nSTEP 5: Testing the setup..." -ForegroundColor Magenta
try {
    Set-Location "n8n-docker"
    
    # Test if container is running
    $containerStatus = docker-compose ps --services --filter "status=running"
    if ($containerStatus -match "n8n") {
        Write-Host "N8N container is running" -ForegroundColor Green
        
        # Test volume mount
        $testResult = docker-compose exec n8n test -d /home/<USER>/shared
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Volume mount is working" -ForegroundColor Green
        } else {
            Write-Host "Volume mount test failed" -ForegroundColor Red
        }
    } else {
        Write-Host "N8N container is not running" -ForegroundColor Red
    }
} catch {
    Write-Host "Step 5 Warning: Could not test setup: $($_.Exception.Message)" -ForegroundColor Yellow
} finally {
    Set-Location ".."
}

Write-Host "`nMIGRATION COMPLETE!" -ForegroundColor Green
Write-Host "=" * 50

Write-Host "`nWhat was accomplished:" -ForegroundColor Cyan
Write-Host "- Created centralized Docker share at: $ShareRoot" -ForegroundColor White
Write-Host "- Updated Docker Compose volume mounts" -ForegroundColor White
Write-Host "- Migrated existing DNS reports" -ForegroundColor White
Write-Host "- Restarted N8N containers with new configuration" -ForegroundColor White

Write-Host "`nNext Steps:" -ForegroundColor Yellow
Write-Host "1. Open N8N at http://localhost:5678" -ForegroundColor White
Write-Host "2. Test your DNS Security Monitor workflow" -ForegroundColor White
Write-Host "3. Check that files are created in $ShareRoot\N8N\dns_reports\" -ForegroundColor White
Write-Host "4. If workflows fail, manually update file paths in N8N to use /home/<USER>/shared/" -ForegroundColor White

Write-Host "`nTroubleshooting:" -ForegroundColor Yellow
Write-Host "- If N8N workflows can't find files, check the volume mount" -ForegroundColor White
Write-Host "- Container path: /home/<USER>/shared/ maps to $ShareRoot\N8N\" -ForegroundColor White
Write-Host "- Check container logs: docker-compose logs n8n" -ForegroundColor White

Write-Host "`nYour N8N project is now portable and scalable!" -ForegroundColor Green
