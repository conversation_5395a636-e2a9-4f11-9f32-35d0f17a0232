#!/usr/bin/env python3
"""
Comprehensive SQL Test File

This file contains a variety of Python imports, code statements, and SQL statements
in different formats to thoroughly test the SQL validator's detection capabilities.

Test Categories:
1. Python imports (should NOT be detected as SQL)
2. Regular code statements (should NOT be detected as SQL)
3. Single-line SQL statements (SHOULD be detected)
4. Multi-line SQL statements (SHOULD be detected)
5. SQL with blank lines (SHOULD be detected)
6. Edge cases and tricky scenarios
"""

# =============================================================================
# CATEGORY 1: Python Imports (Should NOT be detected as SQL)
# =============================================================================

from pathlib import Path
import sys
import os
from datetime import datetime
from typing import List, Dict, Optional
from core.healer_manager import SelfHealerManager
from n8n_builder.error_handler import ErrorDetail, ErrorCategory
import select, insert, update, delete  # These are Python modules, not SQL
from database import connection
import sqlite3

# =============================================================================
# CATEGORY 2: Regular Code Statements (Should NOT be detected as SQL)
# =============================================================================

def some_function():
    """Function with SQL keywords in comments and strings."""
    
    # Comments with SQL keywords should not be detected
    # SELECT your favorite option FROM the menu
    # INSERT your card INTO the slot
    # UPDATE your profile WHERE necessary
    
    # Log messages and print statements (not SQL)
    print("SELECT your option from the menu")
    log_message = "UPDATE operation completed successfully"
    error_msg = "INSERT failed due to constraint violation"
    status = "DELETE operation in progress"
    
    # Variable names that happen to contain SQL keywords
    select_option = "option1"
    insert_mode = True
    update_flag = False
    delete_permission = "admin"
    
    return "FROM here we go to the next step"

# =============================================================================
# CATEGORY 3: Single-Line SQL Statements (SHOULD be detected)
# =============================================================================

class DatabaseQueries:
    """Class containing various SQL queries."""
    
    # Simple SELECT statements
    get_all_entities = "SELECT * FROM REF_Entity"
    get_entity_by_name = "SELECT * FROM REF_Entity WHERE Name = 'test'"
    count_entities = "SELECT COUNT(*) FROM REF_Entity"
    
    # INSERT statements
    insert_entity = "INSERT INTO REF_Entity (Name, Value) VALUES (?, ?)"
    insert_fact = "INSERT INTO REF_Fact (EntityID, Name, Value) VALUES (1, 'test', 'value')"
    
    # UPDATE statements
    update_entity = "UPDATE REF_Entity SET Name = ? WHERE ID = ?"
    update_status = "UPDATE REF_Entity SET Status = 'active' WHERE Name = 'test'"
    
    # DELETE statements
    delete_entity = "DELETE FROM REF_Entity WHERE ID = ?"
    delete_old_records = "DELETE FROM REF_Entity WHERE CreatedDate < '2023-01-01'"

# =============================================================================
# CATEGORY 4: Multi-Line SQL Statements (SHOULD be detected)
# =============================================================================

def get_complex_queries():
    """Function with multi-line SQL statements."""
    
    # Multi-line SELECT with triple quotes
    complex_select = """
    SELECT e.ID, e.Name, f.Value
    FROM REF_Entity e
    JOIN REF_Fact f ON e.ID = f.EntityID
    WHERE e.Status = 'active'
    ORDER BY e.Name
    """
    
    # Multi-line INSERT
    bulk_insert = """
    INSERT INTO REF_Entity (Name, Description, Status, CreatedDate)
    VALUES 
        ('Entity1', 'First entity', 'active', GETDATE()),
        ('Entity2', 'Second entity', 'inactive', GETDATE()),
        ('Entity3', 'Third entity', 'active', GETDATE())
    """
    
    # Multi-line UPDATE with complex WHERE clause
    complex_update = """
    UPDATE REF_Entity 
    SET Status = 'archived',
        ModifiedDate = GETDATE()
    WHERE CreatedDate < DATEADD(year, -1, GETDATE())
        AND Status = 'inactive'
    """
    
    # Multi-line DELETE with subquery
    complex_delete = """
    DELETE FROM REF_Fact 
    WHERE EntityID IN (
        SELECT ID FROM REF_Entity 
        WHERE Status = 'deleted'
    )
    """
    
    return complex_select, bulk_insert, complex_update, complex_delete

# =============================================================================
# CATEGORY 5: SQL with Blank Lines (SHOULD be detected)
# =============================================================================

def get_formatted_queries():
    """SQL queries with blank lines for readability."""
    
    # SELECT with blank lines
    formatted_select = """
    SELECT 
        e.ID,
        e.Name,
        e.Description,
        
        f.Value,
        f.CreatedDate
        
    FROM REF_Entity e
    
    LEFT JOIN REF_Fact f 
        ON e.ID = f.EntityID
        
    WHERE e.Status = 'active'
        AND f.Value IS NOT NULL
        
    ORDER BY 
        e.Name ASC,
        f.CreatedDate DESC
    """
    
    # INSERT with formatting
    formatted_insert = """
    INSERT INTO REF_Entity 
    (
        Name,
        Description,
        Status,
        CreatedDate
    )
    VALUES 
    (
        ?,
        ?,
        'active',
        GETDATE()
    )
    """
    
    return formatted_select, formatted_insert

# =============================================================================
# CATEGORY 6: Edge Cases and Tricky Scenarios
# =============================================================================

class EdgeCaseTests:
    """Edge cases that might confuse the SQL detector."""
    
    # SQL keywords in different contexts
    not_sql_1 = "Please SELECT your favorite FROM our menu"  # Not SQL - missing proper structure
    not_sql_2 = "The UPDATE was successful"  # Not SQL - just a message
    not_sql_3 = "INSERT coin to continue"  # Not SQL - just text
    
    # Actual SQL that might be tricky
    simple_sql = "SELECT 1"  # Valid SQL - system query
    system_sql = "SELECT @@VERSION"  # Valid SQL - system function
    
    # SQL with string literals containing keywords
    tricky_sql_1 = "SELECT 'This contains SELECT keyword' FROM REF_Entity"
    tricky_sql_2 = "INSERT INTO REF_Entity (Name) VALUES ('UPDATE this later')"
    
    # SQL with comments (still valid SQL)
    commented_sql = """
    SELECT * FROM REF_Entity  -- This is a comment with SELECT
    WHERE Name = 'test'  /* Another comment with INSERT */
    """
    
    # Mixed quotes
    mixed_quotes_1 = 'SELECT * FROM REF_Entity WHERE Name = "test"'
    mixed_quotes_2 = "SELECT * FROM REF_Entity WHERE Name = 'test'"
    
    # SQL with escape characters
    escaped_sql = "SELECT * FROM REF_Entity WHERE Name = 'It\\'s a test'"
    
    # Very long SQL statement
    long_sql = """
    SELECT 
        e1.ID as Entity1_ID,
        e1.Name as Entity1_Name,
        e2.ID as Entity2_ID,
        e2.Name as Entity2_Name,
        f1.Value as Fact1_Value,
        f2.Value as Fact2_Value
    FROM REF_Entity e1
    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID
    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID
    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID
    WHERE e1.Status = 'active'
        AND f1.Type = 'primary'
        AND (e2.Status IS NULL OR e2.Status = 'active')
    ORDER BY e1.Name, e2.Name
    LIMIT 100
    """

# =============================================================================
# CATEGORY 7: Configuration-Style SQL
# =============================================================================

# Dictionary with SQL queries (common pattern)
DATABASE_QUERIES = {
    "get_user": "SELECT * FROM users WHERE id = ?",
    "create_user": "INSERT INTO users (name, email) VALUES (?, ?)",
    "update_user": "UPDATE users SET email = ? WHERE id = ?",
    "delete_user": "DELETE FROM users WHERE id = ?",
    
    # Nested dictionary
    "reports": {
        "daily": "SELECT COUNT(*) FROM transactions WHERE DATE(created_at) = CURDATE()",
        "monthly": "SELECT SUM(amount) FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())"
    }
}

# List of SQL statements
SQL_MIGRATIONS = [
    "CREATE TABLE IF NOT EXISTS users (id INTEGER PRIMARY KEY, name TEXT, email TEXT)",
    "ALTER TABLE users ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
    "CREATE INDEX idx_users_email ON users(email)",
    "DROP TABLE IF EXISTS temp_users"
]

# =============================================================================
# CATEGORY 8: Function Parameters and Return Values
# =============================================================================

def execute_query(sql_statement):
    """Function that takes SQL as parameter."""
    # This parameter name contains 'sql' but the function itself is not SQL
    if "SELECT" in sql_statement:
        return "This is a SELECT query"
    return "Unknown query type"

def build_dynamic_query():
    """Function that builds SQL dynamically."""
    base_query = "SELECT * FROM REF_Entity"
    where_clause = "WHERE Status = 'active'"
    order_clause = "ORDER BY Name"
    
    # Dynamic SQL construction (each part should be detected)
    full_query = f"{base_query} {where_clause} {order_clause}"
    return full_query

# =============================================================================
# CATEGORY 9: Error Cases and Incomplete SQL
# =============================================================================

class ErrorCases:
    """Cases that should not be detected as valid SQL."""
    
    # Incomplete SQL (missing required parts)
    incomplete_1 = "SELECT * FROM"  # Missing table name
    incomplete_2 = "INSERT INTO REF_Entity"  # Missing VALUES
    incomplete_3 = "UPDATE REF_Entity SET"  # Missing column assignment
    incomplete_4 = "DELETE FROM"  # Missing table name
    
    # SQL keywords without proper context
    just_keywords = "SELECT INSERT UPDATE DELETE FROM WHERE"
    
    # SQL-like but not SQL
    pseudo_sql = "PLEASE SELECT FROM THE FOLLOWING OPTIONS WHERE APPLICABLE"

# =============================================================================
# END OF TEST FILE
# =============================================================================

if __name__ == "__main__":
    print("This is a comprehensive SQL test file.")
    print("It should be used to test SQL detection and validation systems.")
    
    # Some final test cases in the main execution
    test_query = "SELECT COUNT(*) FROM REF_Entity WHERE Status = 'test'"
    print(f"Test query: {test_query}")
    
    # Multi-line in main
    final_test = """
    SELECT 
        'Test completed successfully' as message,
        COUNT(*) as total_entities
    FROM REF_Entity
    """
    
    print("Test file loaded successfully.")
