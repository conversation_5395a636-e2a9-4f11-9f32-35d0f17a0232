#!/usr/bin/env python3
"""
SQL Validator Configuration Manager

Handles loading, validating, and managing configuration settings for the
SQL validation and repair system.

AUTHOR: N8N_Builder Team
DATE: 2025-08-18
PURPOSE: Centralized configuration management for SQL validation system
"""

import os
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime

@dataclass
class DatabaseConfig:
    """Database connection configuration."""
    name: str
    location: str
    port: Optional[int] = None
    username: str = ""
    password: str = ""
    use_windows_auth: bool = True
    username_env_var: str = "DB_USERNAME"
    password_env_var: str = "DB_PASSWORD"

@dataclass
class FileSearchConfig:
    """File search configuration."""
    file_types: List[str] = field(default_factory=lambda: [".py", ".sql"])
    search_paths: List[str] = field(default_factory=lambda: ["Self_Healer", "Scripts", "tests"])
    recursive_search: bool = True
    max_depth: int = 0
    exclude_patterns: List[str] = field(default_factory=lambda: ["__pycache__", ".git", ".vscode"])

@dataclass
class SQLDetectionConfig:
    """SQL detection configuration."""
    required_keywords: List[str] = field(default_factory=lambda: ["SELECT", "INSERT", "UPDATE", "DELETE"])
    supporting_keywords: List[str] = field(default_factory=lambda: ["FROM", "WHERE", "JOIN", "INTO", "SET"])
    quote_types: List[str] = field(default_factory=lambda: ["single", "double", "triple_single", "triple_double"])
    min_statement_length: int = 10
    case_sensitive: bool = False

@dataclass
class RepairConfig:
    """Repair operation configuration."""
    table_name_fixes: Dict[str, str] = field(default_factory=dict)
    column_name_fixes: Dict[str, str] = field(default_factory=dict)
    create_backups: bool = True
    backup_directory: str = "data/repair_backups"
    backup_pattern: str = "{filename}.backup_{timestamp}"

@dataclass
class LoggingConfig:
    """Logging configuration."""
    enabled: bool = True
    log_directory: str = "data/database_repair_logs"
    detailed_log_pattern: str = "repair_session_{session_id}.json"
    summary_log_pattern: str = "repair_summary_{session_id}.csv"
    validation_log_pattern: str = "sql_validation_{session_id}.txt"
    log_level: str = "INFO"
    log_sql_content: bool = True
    max_log_size: int = 50

@dataclass
class ValidationConfig:
    """Validation configuration."""
    enabled: bool = True
    connection_timeout: int = 30
    query_timeout: int = 10
    max_retry_attempts: int = 3
    skip_validation_for: List[str] = field(default_factory=list)
    validation_failures_as_warnings: bool = False

@dataclass
class PerformanceConfig:
    """Performance configuration."""
    max_parallel_files: int = 4
    max_parallel_validations: int = 2
    show_progress: bool = True
    progress_interval: int = 10

@dataclass
class OutputConfig:
    """Output configuration."""
    verbosity: str = "NORMAL"
    show_detection_details: bool = True
    show_validation_details: bool = False
    generate_html_report: bool = True
    html_report_template: str = ""
    export_csv: bool = True
    export_json: bool = True

class SQLValidatorConfigManager:
    """Manages configuration for the SQL validation and repair system."""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or self._get_default_config_path()
        self.project_root = Path(__file__).parent.parent.parent
        
        # Configuration sections
        self.database: DatabaseConfig = DatabaseConfig("", "")
        self.file_search: FileSearchConfig = FileSearchConfig()
        self.sql_detection: SQLDetectionConfig = SQLDetectionConfig()
        self.repair: RepairConfig = RepairConfig()
        self.logging: LoggingConfig = LoggingConfig()
        self.validation: ValidationConfig = ValidationConfig()
        self.performance: PerformanceConfig = PerformanceConfig()
        self.output: OutputConfig = OutputConfig()
        
        # Load configuration
        self.load_config()
    
    def _get_default_config_path(self) -> str:
        """Get the default configuration file path."""
        return str(Path(__file__).parent / "sql_validator_config.yaml")
    
    def load_config(self) -> None:
        """Load configuration from YAML file."""
        try:
            config_path = Path(self.config_file)
            
            if not config_path.exists():
                print(f"⚠️  Configuration file not found: {config_path}")
                print(f"   Using default configuration settings.")
                self._apply_defaults()
                return
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            if not config_data:
                print(f"⚠️  Configuration file is empty: {config_path}")
                self._apply_defaults()
                return
            
            # Load each configuration section
            self._load_database_config(config_data.get('database', {}))
            self._load_file_search_config(config_data.get('file_search', {}))
            self._load_sql_detection_config(config_data.get('sql_detection', {}))
            self._load_repair_config(config_data.get('repair', {}))
            self._load_logging_config(config_data.get('logging', {}))
            self._load_validation_config(config_data.get('validation', {}))
            self._load_performance_config(config_data.get('performance', {}))
            self._load_output_config(config_data.get('output', {}))
            
            # Resolve environment variables and paths
            self._resolve_environment_variables()
            self._resolve_paths()
            
            print(f"✅ Configuration loaded from: {config_path}")
            
        except Exception as e:
            print(f"❌ Error loading configuration: {e}")
            print(f"   Using default configuration settings.")
            self._apply_defaults()
    
    def _load_database_config(self, config: Dict[str, Any]) -> None:
        """Load database configuration."""
        auth_config = config.get('authentication', {})
        
        self.database = DatabaseConfig(
            name=config.get('name', 'knowledgebase'),
            location=config.get('location', 'local'),
            port=config.get('port'),
            username=auth_config.get('username', ''),
            password=auth_config.get('password', ''),
            use_windows_auth=auth_config.get('use_windows_auth', True),
            username_env_var=auth_config.get('username_env_var', 'DB_USERNAME'),
            password_env_var=auth_config.get('password_env_var', 'DB_PASSWORD')
        )
    
    def _load_file_search_config(self, config: Dict[str, Any]) -> None:
        """Load file search configuration."""
        self.file_search = FileSearchConfig(
            file_types=config.get('file_types', ['.py', '.sql']),
            search_paths=config.get('search_paths', ['Self_Healer', 'Scripts', 'tests']),
            recursive_search=config.get('recursive_search', True),
            max_depth=config.get('max_depth', 0),
            exclude_patterns=config.get('exclude_patterns', ['__pycache__', '.git', '.vscode'])
        )
    
    def _load_sql_detection_config(self, config: Dict[str, Any]) -> None:
        """Load SQL detection configuration."""
        self.sql_detection = SQLDetectionConfig(
            required_keywords=config.get('required_keywords', ['SELECT', 'INSERT', 'UPDATE', 'DELETE']),
            supporting_keywords=config.get('supporting_keywords', ['FROM', 'WHERE', 'JOIN', 'INTO', 'SET']),
            quote_types=config.get('quote_types', ['single', 'double', 'triple_single', 'triple_double']),
            min_statement_length=config.get('min_statement_length', 10),
            case_sensitive=config.get('case_sensitive', False)
        )
    
    def _load_repair_config(self, config: Dict[str, Any]) -> None:
        """Load repair configuration."""
        # Handle None values from YAML (when key exists but has no value)
        table_fixes = config.get('table_name_fixes', {})
        if table_fixes is None:
            table_fixes = {}

        column_fixes = config.get('column_name_fixes', {})
        if column_fixes is None:
            column_fixes = {}

        self.repair = RepairConfig(
            table_name_fixes=table_fixes,
            column_name_fixes=column_fixes,
            create_backups=config.get('create_backups', True),
            backup_directory=config.get('backup_directory', 'data/repair_backups'),
            backup_pattern=config.get('backup_pattern', '{filename}.backup_{timestamp}')
        )
    
    def _load_logging_config(self, config: Dict[str, Any]) -> None:
        """Load logging configuration."""
        self.logging = LoggingConfig(
            enabled=config.get('enabled', True),
            log_directory=config.get('log_directory', 'data/database_repair_logs'),
            detailed_log_pattern=config.get('detailed_log_pattern', 'repair_session_{session_id}.json'),
            summary_log_pattern=config.get('summary_log_pattern', 'repair_summary_{session_id}.csv'),
            validation_log_pattern=config.get('validation_log_pattern', 'sql_validation_{session_id}.txt'),
            log_level=config.get('log_level', 'INFO'),
            log_sql_content=config.get('log_sql_content', True),
            max_log_size=config.get('max_log_size', 50)
        )
    
    def _load_validation_config(self, config: Dict[str, Any]) -> None:
        """Load validation configuration."""
        # Handle None values from YAML
        skip_validation = config.get('skip_validation_for', [])
        if skip_validation is None:
            skip_validation = []

        self.validation = ValidationConfig(
            enabled=config.get('enabled', True),
            connection_timeout=config.get('connection_timeout', 30),
            query_timeout=config.get('query_timeout', 10),
            max_retry_attempts=config.get('max_retry_attempts', 3),
            skip_validation_for=skip_validation,
            validation_failures_as_warnings=config.get('validation_failures_as_warnings', False)
        )
    
    def _load_performance_config(self, config: Dict[str, Any]) -> None:
        """Load performance configuration."""
        self.performance = PerformanceConfig(
            max_parallel_files=config.get('max_parallel_files', 4),
            max_parallel_validations=config.get('max_parallel_validations', 2),
            show_progress=config.get('show_progress', True),
            progress_interval=config.get('progress_interval', 10)
        )
    
    def _load_output_config(self, config: Dict[str, Any]) -> None:
        """Load output configuration."""
        self.output = OutputConfig(
            verbosity=config.get('verbosity', 'NORMAL'),
            show_detection_details=config.get('show_detection_details', True),
            show_validation_details=config.get('show_validation_details', False),
            generate_html_report=config.get('generate_html_report', True),
            html_report_template=config.get('html_report_template', ''),
            export_csv=config.get('export_csv', True),
            export_json=config.get('export_json', True)
        )
    
    def _apply_defaults(self) -> None:
        """Apply default configuration values."""
        self.database = DatabaseConfig("knowledgebase", "local")
        self.file_search = FileSearchConfig()
        self.sql_detection = SQLDetectionConfig()
        self.repair = RepairConfig()
        self.logging = LoggingConfig()
        self.validation = ValidationConfig()
        self.performance = PerformanceConfig()
        self.output = OutputConfig()
    
    def _resolve_environment_variables(self) -> None:
        """Resolve environment variables in configuration."""
        # Database credentials from environment variables
        if not self.database.username and self.database.username_env_var:
            self.database.username = os.getenv(self.database.username_env_var, "")
        
        if not self.database.password and self.database.password_env_var:
            self.database.password = os.getenv(self.database.password_env_var, "")
    
    def _resolve_paths(self) -> None:
        """Resolve relative paths to absolute paths."""
        # Convert relative search paths to absolute paths
        resolved_paths = []
        for path in self.file_search.search_paths:
            if Path(path).is_absolute():
                resolved_paths.append(path)
            else:
                resolved_paths.append(str(self.project_root / path))
        self.file_search.search_paths = resolved_paths
        
        # Resolve backup directory
        if not Path(self.repair.backup_directory).is_absolute():
            self.repair.backup_directory = str(self.project_root / self.repair.backup_directory)
        
        # Resolve log directory
        if not Path(self.logging.log_directory).is_absolute():
            self.logging.log_directory = str(self.project_root / self.logging.log_directory)
    
    def get_connection_string(self) -> str:
        """Generate database connection string."""
        if self.database.location.lower() in ['local', 'localhost']:
            server = 'localhost'
        else:
            server = self.database.location
        
        if self.database.port:
            server = f"{server},{self.database.port}"
        
        if self.database.use_windows_auth:
            return f"DRIVER={{ODBC Driver 18 for SQL Server}};SERVER={server};DATABASE={self.database.name};Trusted_Connection=yes;"
        else:
            username = self.database.username
            password = self.database.password
            return f"DRIVER={{ODBC Driver 18 for SQL Server}};SERVER={server};DATABASE={self.database.name};UID={username};PWD={password};"
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return list of issues."""
        issues = []
        
        # Validate database configuration
        if not self.database.name:
            issues.append("Database name is required")
        
        if not self.database.location:
            issues.append("Database location is required")
        
        # Validate file search configuration
        if not self.file_search.file_types:
            issues.append("At least one file type must be specified")
        
        if not self.file_search.search_paths:
            issues.append("At least one search path must be specified")
        
        # Check if search paths exist
        for path in self.file_search.search_paths:
            if not Path(path).exists():
                issues.append(f"Search path does not exist: {path}")
        
        # Validate SQL detection configuration
        if not self.sql_detection.required_keywords:
            issues.append("At least one required SQL keyword must be specified")
        
        return issues
    
    def print_config_summary(self) -> None:
        """Print a summary of the current configuration."""
        print(f"\n📋 SQL Validator Configuration Summary")
        print(f"{'=' * 50}")
        
        print(f"🗄️  Database:")
        print(f"   Name: {self.database.name}")
        print(f"   Location: {self.database.location}")
        print(f"   Authentication: {'Windows Auth' if self.database.use_windows_auth else 'SQL Auth'}")
        
        print(f"\n📁 File Search:")
        print(f"   File types: {', '.join(self.file_search.file_types)}")
        print(f"   Search paths: {len(self.file_search.search_paths)} paths")
        print(f"   Recursive: {self.file_search.recursive_search}")
        
        print(f"\n🔍 SQL Detection:")
        print(f"   Required keywords: {len(self.sql_detection.required_keywords)}")
        print(f"   Supporting keywords: {len(self.sql_detection.supporting_keywords)}")
        print(f"   Quote types: {', '.join(self.sql_detection.quote_types)}")
        
        print(f"\n🔧 Repair:")
        print(f"   Table fixes: {len(self.repair.table_name_fixes)}")
        print(f"   Create backups: {self.repair.create_backups}")
        
        print(f"\n📊 Logging:")
        print(f"   Enabled: {self.logging.enabled}")
        print(f"   Log directory: {Path(self.logging.log_directory).name}")
        
        print(f"\n✅ Validation:")
        print(f"   Enabled: {self.validation.enabled}")
        print(f"   Connection timeout: {self.validation.connection_timeout}s")

if __name__ == "__main__":
    # Test the configuration manager
    config_manager = SQLValidatorConfigManager()
    
    # Validate configuration
    issues = config_manager.validate_config()
    if issues:
        print("⚠️  Configuration issues found:")
        for issue in issues:
            print(f"   - {issue}")
    else:
        print("✅ Configuration is valid")
    
    # Print configuration summary
    config_manager.print_config_summary()
