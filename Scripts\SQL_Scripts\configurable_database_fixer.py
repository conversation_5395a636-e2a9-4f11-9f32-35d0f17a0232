#!/usr/bin/env python3
"""
Configurable Database Reference Fixer

A comprehensive database schema maintenance tool that uses configuration files
to control all aspects of SQL validation and repair operations.

AUTHOR: N8N_Builder Team
DATE: 2025-08-18
PURPOSE: Production-ready configurable database maintenance system
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Dict, Set
import fnmatch

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from Scripts.SQL_Scripts.config_manager import SQLValidatorConfigManager
from Scripts.database_repair_logger import DatabaseRepairLogger
from Scripts.sql_validator import SQLValidator
from Scripts.refined_sql_detector import RefinedSQLDetector

class ConfigurableDatabaseFixer:
    """Configurable database fixer with comprehensive settings management."""
    
    def __init__(self, config_file: str = None):
        # Load configuration
        self.config = SQLValidatorConfigManager(config_file)
        
        # Validate configuration
        config_issues = self.config.validate_config()
        if config_issues:
            print("⚠️  Configuration issues found:")
            for issue in config_issues:
                print(f"   - {issue}")
            print("   Continuing with available settings...\n")
        
        # Initialize components
        self.logger = DatabaseRepairLogger(self.config.logging.log_directory)
        self.sql_validator = SQLValidator(self.config.database.name)
        self.sql_validator.set_logger(self.logger)
        self.sql_detector = RefinedSQLDetector()
        
        # Configure SQL detector with config settings
        self._configure_sql_detector()
        
        # Statistics
        self.stats = {
            'files_processed': 0,
            'files_modified': 0,
            'sql_statements_found': 0,
            'sql_validations_passed': 0,
            'sql_validations_failed': 0,
            'repairs_made': 0
        }
    
    def _configure_sql_detector(self):
        """Configure the SQL detector with settings from config."""
        # Update SQL detector with configuration
        self.sql_detector.sql_keywords = set(self.config.sql_detection.required_keywords)
        self.sql_detector.supporting_keywords = set(self.config.sql_detection.supporting_keywords)
        
        # Configure quote patterns based on config
        quote_patterns = []
        if 'triple_double' in self.config.sql_detection.quote_types:
            quote_patterns.append((r'"""(.*?)"""', 'triple_double'))
        if 'triple_single' in self.config.sql_detection.quote_types:
            quote_patterns.append((r"'''(.*?)'''", 'triple_single'))
        if 'double' in self.config.sql_detection.quote_types:
            quote_patterns.append((r'"([^"\\]*(\\.[^"\\]*)*)"', 'double'))
        if 'single' in self.config.sql_detection.quote_types:
            quote_patterns.append((r"'([^'\\]*(\\.[^'\\]*)*)'", 'single'))
        
        self.sql_detector.quote_patterns = quote_patterns
    
    def _should_exclude_path(self, path: Path) -> bool:
        """Check if a path should be excluded based on configuration."""
        path_str = str(path)
        
        for pattern in self.config.file_search.exclude_patterns:
            if fnmatch.fnmatch(path.name, pattern) or fnmatch.fnmatch(path_str, pattern):
                return True
        
        return False
    
    def _get_files_to_process(self) -> List[Path]:
        """Get list of files to process based on configuration."""
        files_to_process = []
        
        for search_path in self.config.file_search.search_paths:
            search_dir = Path(search_path)
            
            if not search_dir.exists():
                if self.config.output.verbosity in ['VERBOSE', 'DEBUG']:
                    print(f"⚠️  Search path does not exist: {search_path}")
                continue
            
            if self.config.output.verbosity in ['VERBOSE', 'DEBUG']:
                print(f"📁 Searching: {search_path}")
            
            # Get files based on recursive setting
            if self.config.file_search.recursive_search:
                pattern = "**/*"
                if self.config.file_search.max_depth > 0:
                    # Limit recursion depth (simplified approach)
                    pattern = "/".join(["*"] * self.config.file_search.max_depth) + "/*"
                
                file_iterator = search_dir.rglob("*")
            else:
                file_iterator = search_dir.iterdir()
            
            for file_path in file_iterator:
                if not file_path.is_file():
                    continue
                
                # Check file extension
                if file_path.suffix not in self.config.file_search.file_types:
                    continue
                
                # Check exclusion patterns
                if self._should_exclude_path(file_path):
                    continue
                
                files_to_process.append(file_path)
        
        return files_to_process
    
    async def process_file(self, file_path: Path, dry_run: bool = True) -> Dict:
        """Process a single file with repairs and validation."""
        file_results = {
            'file_path': str(file_path),
            'repairs_made': 0,
            'sql_statements_found': 0,
            'sql_validations_passed': 0,
            'sql_validations_failed': 0,
            'errors': []
        }
        
        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                original_content = f.read()
            
            modified_content = original_content
            repairs_made = 0
            
            # Apply table name repairs
            for incorrect, correct in self.config.repair.table_name_fixes.items():
                if incorrect in modified_content:
                    count = modified_content.count(incorrect)
                    if count > 0:
                        modified_content = modified_content.replace(incorrect, correct)
                        repairs_made += count
                        
                        # Log repair
                        self.logger.log_repair(
                            str(file_path), 0,
                            f"Contains {count} instances of '{incorrect}'",
                            f"Replaced with '{correct}'",
                            f"Table name fix: {incorrect} → {correct}"
                        )
                        
                        if self.config.output.verbosity in ['NORMAL', 'VERBOSE', 'DEBUG']:
                            print(f"   📝 {file_path.name}: {incorrect} → {correct} ({count} times)")
            
            # Apply column name repairs if configured
            for incorrect, correct in self.config.repair.column_name_fixes.items():
                if incorrect in modified_content:
                    count = modified_content.count(incorrect)
                    if count > 0:
                        modified_content = modified_content.replace(incorrect, correct)
                        repairs_made += count
                        
                        # Log repair
                        self.logger.log_repair(
                            str(file_path), 0,
                            f"Contains {count} instances of '{incorrect}'",
                            f"Replaced with '{correct}'",
                            f"Column name fix: {incorrect} → {correct}"
                        )
            
            file_results['repairs_made'] = repairs_made
            
            # Write modified file if changes were made and not dry run
            if repairs_made > 0 and not dry_run:
                if self.config.repair.create_backups:
                    self._create_backup(file_path)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                
                if self.config.output.verbosity in ['VERBOSE', 'DEBUG']:
                    print(f"      ✅ File repaired and backed up")
            
            # SQL validation if enabled
            if self.config.validation.enabled:
                sql_statements = self.sql_detector.detect_sql_in_content(modified_content)
                file_results['sql_statements_found'] = len(sql_statements)
                
                for stmt in sql_statements:
                    # Skip validation for certain SQL types if configured
                    if stmt.sql_type in self.config.validation.skip_validation_for:
                        continue
                    
                    # Skip very short statements
                    if len(stmt.statement.strip()) < self.config.sql_detection.min_statement_length:
                        continue
                    
                    try:
                        validation_result = await self.sql_validator.validate_sql(
                            stmt.statement, str(file_path), stmt.line_number
                        )
                        
                        if validation_result.success:
                            file_results['sql_validations_passed'] += 1
                        else:
                            file_results['sql_validations_failed'] += 1
                            
                            if not self.config.validation.validation_failures_as_warnings:
                                file_results['errors'].append({
                                    'line': stmt.line_number,
                                    'sql': stmt.statement,
                                    'error': validation_result.error_message
                                })
                    
                    except Exception as e:
                        file_results['sql_validations_failed'] += 1
                        file_results['errors'].append({
                            'line': stmt.line_number,
                            'sql': stmt.statement,
                            'error': f"Validation exception: {str(e)}"
                        })
        
        except Exception as e:
            error_msg = f"Error processing file: {str(e)}"
            file_results['errors'].append({'error': error_msg})
            self.logger.log_validation(str(file_path), 0, "", "file_processing_error", "failed", error_msg)
        
        return file_results
    
    def _create_backup(self, file_path: Path):
        """Create a backup of the file."""
        from datetime import datetime
        import shutil
        
        backup_dir = Path(self.config.repair.backup_directory)
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = self.config.repair.backup_pattern.format(
            filename=file_path.name,
            timestamp=timestamp
        )
        
        backup_path = backup_dir / backup_name
        shutil.copy2(file_path, backup_path)
        
        if self.config.output.verbosity in ['VERBOSE', 'DEBUG']:
            print(f"      💾 Backup created: {backup_name}")
    
    async def run_comprehensive_repair(self, dry_run: bool = True) -> Dict:
        """Run comprehensive database repair process."""
        if self.config.output.verbosity != 'QUIET':
            print(f"🔧 Configurable Database Reference Fixer")
            print(f"{'=' * 60}")
            print(f"Mode: {'DRY RUN' if dry_run else 'LIVE REPAIR'}")
            print(f"Database: {self.config.database.name} @ {self.config.database.location}")
            print(f"Session ID: {self.logger.session_id}")
        
        # Print configuration summary if verbose
        if self.config.output.verbosity in ['VERBOSE', 'DEBUG']:
            self.config.print_config_summary()
        
        # Get files to process
        files_to_process = self._get_files_to_process()
        
        if self.config.output.verbosity != 'QUIET':
            print(f"\n📊 Scan Results:")
            print(f"   Files to process: {len(files_to_process)}")
            print(f"   File types: {', '.join(self.config.file_search.file_types)}")
            print(f"   Search paths: {len(self.config.file_search.search_paths)}")
        
        # Process files
        for i, file_path in enumerate(files_to_process, 1):
            # Show progress if enabled
            if (self.config.performance.show_progress and 
                i % self.config.performance.progress_interval == 0):
                print(f"   Progress: {i}/{len(files_to_process)} files processed...")
            
            file_results = await self.process_file(file_path, dry_run)
            
            # Update statistics
            self.stats['files_processed'] += 1
            if file_results['repairs_made'] > 0:
                self.stats['files_modified'] += 1
            self.stats['repairs_made'] += file_results['repairs_made']
            self.stats['sql_statements_found'] += file_results['sql_statements_found']
            self.stats['sql_validations_passed'] += file_results['sql_validations_passed']
            self.stats['sql_validations_failed'] += file_results['sql_validations_failed']
            
            # Show file results if configured
            if (self.config.output.show_detection_details and 
                (file_results['repairs_made'] > 0 or file_results['sql_validations_failed'] > 0)):
                
                relative_path = file_path.relative_to(Path.cwd()) if file_path.is_relative_to(Path.cwd()) else file_path
                print(f"   📄 {relative_path}")
                
                if file_results['repairs_made'] > 0:
                    print(f"      🔧 Repairs: {file_results['repairs_made']}")
                
                if self.config.output.show_validation_details:
                    if file_results['sql_validations_passed'] > 0:
                        print(f"      ✅ SQL Tests Passed: {file_results['sql_validations_passed']}")
                    if file_results['sql_validations_failed'] > 0:
                        print(f"      ❌ SQL Tests Failed: {file_results['sql_validations_failed']}")
        
        return self.stats
    
    def print_final_summary(self, dry_run: bool):
        """Print comprehensive final summary."""
        if self.config.output.verbosity == 'QUIET':
            return
        
        print(f"\n📊 FINAL SUMMARY")
        print(f"{'=' * 60}")
        print(f"Files processed: {self.stats['files_processed']:,}")
        print(f"Files modified: {self.stats['files_modified']:,}")
        print(f"Total repairs: {self.stats['repairs_made']:,}")
        
        if self.config.validation.enabled:
            print(f"SQL statements found: {self.stats['sql_statements_found']:,}")
            print(f"SQL validations passed: {self.stats['sql_validations_passed']:,}")
            print(f"SQL validations failed: {self.stats['sql_validations_failed']:,}")
            
            total_validations = self.stats['sql_validations_passed'] + self.stats['sql_validations_failed']
            if total_validations > 0:
                success_rate = (self.stats['sql_validations_passed'] / total_validations) * 100
                print(f"SQL validation success rate: {success_rate:.1f}%")
            elif self.stats['sql_statements_found'] > 0:
                print(f"SQL validation success rate: No validations performed (0 tests run)")
        
        if dry_run:
            print(f"\n🔍 DRY RUN COMPLETE - No files were modified")
            print(f"   Run with --live to apply repairs")
        else:
            print(f"\n✅ REPAIRS APPLIED")
            if self.stats['files_modified'] > 0 and self.config.repair.create_backups:
                print(f"   Backups created in: {self.config.repair.backup_directory}")
        
        # Show logger summary
        if self.config.logging.enabled:
            self.logger.print_summary()

async def main():
    """Main execution function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Configurable database reference fixer')
    parser.add_argument('--config', '-c', 
                       help='Configuration file path')
    parser.add_argument('--live', action='store_true',
                       help='Apply fixes (default is dry-run)')
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='Quiet mode - minimal output')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose mode - detailed output')
    parser.add_argument('--debug', '-d', action='store_true',
                       help='Debug mode - maximum output')
    
    args = parser.parse_args()
    
    try:
        # Initialize fixer with configuration
        fixer = ConfigurableDatabaseFixer(args.config)
        
        # Override verbosity if specified
        if args.quiet:
            fixer.config.output.verbosity = 'QUIET'
        elif args.verbose:
            fixer.config.output.verbosity = 'VERBOSE'
        elif args.debug:
            fixer.config.output.verbosity = 'DEBUG'
        
        # Run the repair process
        stats = await fixer.run_comprehensive_repair(dry_run=not args.live)
        
        # Print final summary
        fixer.print_final_summary(dry_run=not args.live)
        
        # Close logging session
        fixer.logger.close_session()
        
        # Return appropriate exit code
        return 0 if stats['sql_validations_failed'] == 0 else 1
        
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
