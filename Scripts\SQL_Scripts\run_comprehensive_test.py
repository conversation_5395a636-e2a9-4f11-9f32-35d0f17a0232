#!/usr/bin/env python3
"""Run comprehensive test on the full test file"""

import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from Scripts.refined_sql_detector import RefinedSQLDetector

def main():
    print("🧪 Comprehensive SQL Detection Test")
    print("=" * 50)
    
    detector = RefinedSQLDetector()
    test_file = Path(__file__).parent / "comprehensive_sql_test_file.py"
    
    try:
        # Read the comprehensive test file
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📄 Test file: {test_file.name}")
        print(f"   File size: {len(content):,} characters")
        print(f"   Lines: {len(content.split(chr(10))):,}")
        
        # Detect SQL statements
        sql_statements = detector.detect_sql_in_content(content)
        
        print(f"\n🔍 Detection Results:")
        print(f"   SQL statements found: {len(sql_statements)}")
        
        # Analyze by SQL type
        by_type = {}
        for stmt in sql_statements:
            sql_type = stmt.sql_type
            by_type[sql_type] = by_type.get(sql_type, 0) + 1
        
        print(f"\n📊 By SQL Type:")
        for sql_type, count in sorted(by_type.items()):
            print(f"   {sql_type}: {count}")
        
        # Show sample detections
        print(f"\n📋 Sample Detections:")
        for i, stmt in enumerate(sql_statements[:8], 1):
            print(f"   {i}. Line {stmt.line_number:3d} ({stmt.sql_type:6s}): {stmt.statement[:45]}...")
        
        if len(sql_statements) > 8:
            print(f"   ... and {len(sql_statements) - 8} more")
        
        # Quick analysis
        lines = content.split('\n')
        
        # Count Python imports
        import_lines = [line for line in lines 
                       if line.strip().startswith('from ') or line.strip().startswith('import ')]
        
        # Count comment lines
        comment_lines = [line for line in lines 
                        if line.strip().startswith('#')]
        
        # Count lines with SQL keywords (old method would catch these)
        keyword_lines = [line for line in lines 
                        if any(kw in line.upper() for kw in ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'FROM'])]
        
        print(f"\n🎯 Analysis:")
        print(f"   Python imports: {len(import_lines)}")
        print(f"   Comment lines: {len(comment_lines)}")
        print(f"   Lines with SQL keywords: {len(keyword_lines)}")
        print(f"   Actual SQL detected: {len(sql_statements)}")
        print(f"   False positives avoided: {len(keyword_lines) - len(sql_statements)}")
        
        if len(keyword_lines) > 0:
            accuracy = (1 - (len(keyword_lines) - len(sql_statements)) / len(keyword_lines)) * 100
            print(f"   Accuracy improvement: {(len(keyword_lines) - len(sql_statements)) / len(keyword_lines) * 100:.1f}%")
        
        # Validate some specific expectations
        print(f"\n✅ Validation Checks:")
        
        # Should find SELECT statements
        select_count = by_type.get('SELECT', 0)
        print(f"   SELECT statements: {select_count} {'✅' if select_count > 5 else '⚠️'}")
        
        # Should find INSERT statements  
        insert_count = by_type.get('INSERT', 0)
        print(f"   INSERT statements: {insert_count} {'✅' if insert_count > 3 else '⚠️'}")
        
        # Should find UPDATE statements
        update_count = by_type.get('UPDATE', 0)
        print(f"   UPDATE statements: {update_count} {'✅' if update_count > 2 else '⚠️'}")
        
        # Should find DELETE statements
        delete_count = by_type.get('DELETE', 0)
        print(f"   DELETE statements: {delete_count} {'✅' if delete_count > 2 else '⚠️'}")
        
        # Should not detect too many (avoiding false positives)
        reasonable_count = len(sql_statements) < len(keyword_lines) * 0.7  # Less than 70% of keyword lines
        print(f"   Reasonable detection count: {'✅' if reasonable_count else '⚠️'}")
        
        print(f"\n🎉 Comprehensive test completed!")
        
        if len(sql_statements) > 10 and select_count > 0 and reasonable_count:
            print(f"   🚀 SQL detection system is working excellently!")
            return True
        else:
            print(f"   ⚠️ SQL detection system may need review.")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
