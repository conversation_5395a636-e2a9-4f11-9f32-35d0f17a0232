#!/usr/bin/env python3
"""Simple test of SQL detection system"""

import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from Scripts.refined_sql_detector import RefinedSQLDetector

def main():
    print("🧪 Simple SQL Detection Test")
    print("=" * 40)
    
    detector = RefinedSQLDetector()
    
    # Test cases from our comprehensive file
    test_cases = [
        # Should detect as SQL
        ('get_all = "SELECT * FROM REF_Entity"', True),
        ('insert_sql = "INSERT INTO REF_Entity VALUES (?, ?)"', True),
        ('update_query = "UPDATE REF_Entity SET Name = ? WHERE ID = ?"', True),
        
        # Should NOT detect as SQL
        ('from pathlib import Path', False),
        ('import sys', False),
        ('print("SELECT your option")', False),
        ('log = "UPDATE completed successfully"', False),
        
        # Multi-line SQL (should detect)
        ('''query = """
SELECT * FROM REF_Entity 
WHERE Status = 'active'
"""''', True),
    ]
    
    correct = 0
    total = len(test_cases)
    
    for i, (test_content, should_detect) in enumerate(test_cases, 1):
        sql_found = detector.detect_sql_in_content(test_content)
        detected = len(sql_found) > 0
        
        status = "✅" if detected == should_detect else "❌"
        print(f"{status} Test {i}: {test_content[:30]}...")
        print(f"   Expected: {should_detect}, Got: {detected}")
        
        if sql_found:
            for stmt in sql_found:
                print(f"   SQL: {stmt.sql_type} - {stmt.statement[:25]}...")
        
        if detected == should_detect:
            correct += 1
        
        print()
    
    accuracy = (correct / total) * 100
    print(f"📊 Results: {correct}/{total} correct ({accuracy:.1f}%)")
    
    if accuracy >= 90:
        print("🎉 SQL detection system is working excellently!")
    elif accuracy >= 70:
        print("✅ SQL detection system is working well!")
    else:
        print("⚠️ SQL detection system needs improvement.")
    
    return accuracy >= 70

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
