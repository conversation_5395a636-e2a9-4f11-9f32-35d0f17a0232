#!/usr/bin/env python3
"""
SQL Parser Test Suite

Comprehensive test cases for the segmented SQL parser covering all SQL statement
variations from simplest to most complex, organized by statement type.

AUTHOR: N8N_Builder Team
DATE: 2025-08-18
PURPOSE: Validate segmented SQL parsing across all SQL variations
"""

import sys
from pathlib import Path
from typing import Dict, List, Any
from dataclasses import dataclass

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from Scripts.sql_validator import SQLValidator

@dataclass
class TestCase:
    """Represents a single SQL test case."""
    name: str
    sql: str
    expected_tables: List[str]
    expected_columns: List[str]
    description: str
    complexity: str  # 'simple', 'intermediate', 'complex'

class SQLParserTestSuite:
    """
    Comprehensive test suite for SQL parser validation.
    Tests are organized by statement type and complexity level.
    """
    
    def __init__(self):
        self.validator = SQLValidator()
        self.test_cases = self._build_test_cases()
        self.results = []
    
    def _build_test_cases(self) -> Dict[str, List[TestCase]]:
        """Build comprehensive test cases organized by SQL statement type."""
        return {
            'SELECT_SIMPLE': self._build_select_simple_tests(),
            'SELECT_JOINS': self._build_select_join_tests(),
            'SELECT_COMPLEX': self._build_select_complex_tests(),
            'INSERT_TESTS': self._build_insert_tests(),
            'UPDATE_TESTS': self._build_update_tests(),
            'DELETE_TESTS': self._build_delete_tests(),
            'EDGE_CASES': self._build_edge_case_tests()
        }
    
    def _build_select_simple_tests(self) -> List[TestCase]:
        """Build simple SELECT test cases."""
        return [
            # Basic SELECT statements
            TestCase(
                name="SELECT_BASIC_NO_ALIAS",
                sql="SELECT Name, ID FROM Users",
                expected_tables=["Users"],
                expected_columns=["Name", "ID"],
                description="Basic SELECT with no table alias",
                complexity="simple"
            ),
            
            TestCase(
                name="SELECT_BASIC_WITH_ALIAS",
                sql="SELECT u.Name, u.ID FROM Users u",
                expected_tables=["Users"],
                expected_columns=["Name", "ID"],
                description="Basic SELECT with table alias",
                complexity="simple"
            ),
            
            TestCase(
                name="SELECT_BASIC_WITH_AS",
                sql="SELECT u.Name, u.ID FROM Users AS u",
                expected_tables=["Users"],
                expected_columns=["Name", "ID"],
                description="Basic SELECT with AS keyword",
                complexity="simple"
            ),
            
            TestCase(
                name="SELECT_STAR",
                sql="SELECT * FROM Products",
                expected_tables=["Products"],
                expected_columns=["*"],
                description="SELECT all columns",
                complexity="simple"
            ),

            TestCase(
                name="SELECT_STAR_WHERE",
                sql="SELECT * FROM Products WHERE Price > 100",
                expected_tables=["Products"],
                expected_columns=["*", "Price"],
                description="SELECT all columns with WHERE clause",
                complexity="simple"
            ),

            TestCase(
                name="SELECT_STAR_ALIAS",
                sql="SELECT * FROM Products p WHERE p.Active = 1",
                expected_tables=["Products"],
                expected_columns=["*", "Active"],
                description="SELECT all columns with table alias",
                complexity="simple"
            ),
            
            TestCase(
                name="SELECT_WITH_WHERE",
                sql="SELECT Name, Email FROM Users WHERE Active = 1",
                expected_tables=["Users"],
                expected_columns=["Name", "Email", "Active"],
                description="SELECT with WHERE clause",
                complexity="simple"
            ),
            
            TestCase(
                name="SELECT_WITH_ALIAS_WHERE",
                sql="SELECT u.Name, u.Email FROM Users u WHERE u.Active = 1",
                expected_tables=["Users"],
                expected_columns=["Name", "Email", "Active"],
                description="SELECT with alias and WHERE clause",
                complexity="simple"
            ),
            
            # Schema-qualified tables
            TestCase(
                name="SELECT_SCHEMA_QUALIFIED",
                sql="SELECT u.Name FROM dbo.Users u WHERE u.Status = 'Active'",
                expected_tables=["Users"],
                expected_columns=["Name", "Status"],
                description="SELECT with schema-qualified table",
                complexity="simple"
            ),
        ]
    
    def _build_select_join_tests(self) -> List[TestCase]:
        """Build SELECT with JOIN test cases."""
        return [
            # INNER JOIN variations
            TestCase(
                name="SELECT_INNER_JOIN",
                sql="SELECT u.Name, p.Title FROM Users u INNER JOIN Posts p ON u.ID = p.UserID",
                expected_tables=["Users", "Posts"],
                expected_columns=["Name", "Title", "ID", "UserID"],  # Updated to include JOIN ON columns
                description="Basic INNER JOIN",
                complexity="intermediate"
            ),

            TestCase(
                name="SELECT_JOIN_NO_INNER",
                sql="SELECT u.Name, p.Title FROM Users u JOIN Posts p ON u.ID = p.UserID",
                expected_tables=["Users", "Posts"],
                expected_columns=["Name", "Title", "ID", "UserID"],  # Updated to include JOIN ON columns
                description="JOIN without INNER keyword",
                complexity="intermediate"
            ),
            
            # LEFT JOIN variations
            TestCase(
                name="SELECT_LEFT_JOIN",
                sql="SELECT u.Name, p.Title FROM Users u LEFT JOIN Posts p ON u.ID = p.UserID",
                expected_tables=["Users", "Posts"],
                expected_columns=["Name", "Title", "ID", "UserID"],  # Updated to include JOIN ON columns
                description="LEFT JOIN",
                complexity="intermediate"
            ),

            TestCase(
                name="SELECT_LEFT_OUTER_JOIN",
                sql="SELECT u.Name, p.Title FROM Users u LEFT OUTER JOIN Posts p ON u.ID = p.UserID",
                expected_tables=["Users", "Posts"],
                expected_columns=["Name", "Title", "ID", "UserID"],  # Updated to include JOIN ON columns
                description="LEFT OUTER JOIN",
                complexity="intermediate"
            ),

            # RIGHT JOIN variations
            TestCase(
                name="SELECT_RIGHT_JOIN",
                sql="SELECT u.Name, p.Title FROM Users u RIGHT JOIN Posts p ON u.ID = p.UserID",
                expected_tables=["Users", "Posts"],
                expected_columns=["Name", "Title", "ID", "UserID"],  # Updated to include JOIN ON columns
                description="RIGHT JOIN",
                complexity="intermediate"
            ),

            # FULL JOIN variations
            TestCase(
                name="SELECT_FULL_JOIN",
                sql="SELECT u.Name, p.Title FROM Users u FULL JOIN Posts p ON u.ID = p.UserID",
                expected_tables=["Users", "Posts"],
                expected_columns=["Name", "Title", "ID", "UserID"],  # Updated to include JOIN ON columns
                description="FULL JOIN",
                complexity="intermediate"
            ),
            
            # Multiple JOINs
            TestCase(
                name="SELECT_MULTIPLE_JOINS",
                sql="SELECT u.Name, p.Title, c.Content FROM Users u JOIN Posts p ON u.ID = p.UserID JOIN Comments c ON p.ID = c.PostID",
                expected_tables=["Users", "Posts", "Comments"],
                expected_columns=["Name", "Title", "Content", "ID", "UserID", "PostID"],  # Updated to include all JOIN ON columns
                description="Multiple JOINs",
                complexity="intermediate"
            ),

            # Mixed JOIN types
            TestCase(
                name="SELECT_MIXED_JOINS",
                sql="SELECT u.Name, p.Title, c.Content FROM Users u LEFT JOIN Posts p ON u.ID = p.UserID INNER JOIN Comments c ON p.ID = c.PostID",
                expected_tables=["Users", "Posts", "Comments"],
                expected_columns=["Name", "Title", "Content", "ID", "UserID", "PostID"],  # Updated to include all JOIN ON columns
                description="Mixed JOIN types",
                complexity="intermediate"
            ),
            
            # Comma-separated tables (legacy syntax)
            TestCase(
                name="SELECT_COMMA_SEPARATED",
                sql="SELECT u.Name, p.Title FROM Users u, Posts p WHERE u.ID = p.UserID",
                expected_tables=["Users", "Posts"],
                expected_columns=["Name", "Title", "ID", "UserID"],
                description="Comma-separated tables (legacy)",
                complexity="intermediate"
            ),
            
            TestCase(
                name="SELECT_COMMA_SEPARATED_COMPLEX",
                sql="SELECT t1.Name, t2.Title, t3.Content FROM Table1 t1, Table2 t2, Table3 t3 WHERE t1.ID = t2.T1_ID AND t2.ID = t3.T2_ID",
                expected_tables=["Table1", "Table2", "Table3"],
                expected_columns=["Name", "Title", "Content", "ID", "T1_ID", "T2_ID"],
                description="Multiple comma-separated tables",
                complexity="intermediate"
            ),
        ]
    
    def _build_select_complex_tests(self) -> List[TestCase]:
        """Build complex SELECT test cases with GROUP BY, HAVING, ORDER BY."""
        return [
            # GROUP BY clauses
            TestCase(
                name="SELECT_GROUP_BY",
                sql="SELECT Department, COUNT(*) as EmployeeCount FROM Employees GROUP BY Department",
                expected_tables=["Employees"],
                expected_columns=["Department"],
                description="SELECT with GROUP BY",
                complexity="complex"
            ),
            
            TestCase(
                name="SELECT_GROUP_BY_MULTIPLE",
                sql="SELECT Department, JobTitle, COUNT(*) as Count FROM Employees GROUP BY Department, JobTitle",
                expected_tables=["Employees"],
                expected_columns=["Department", "JobTitle"],
                description="SELECT with multiple GROUP BY columns",
                complexity="complex"
            ),
            
            # HAVING clauses
            TestCase(
                name="SELECT_GROUP_BY_HAVING",
                sql="SELECT Department, COUNT(*) as EmployeeCount FROM Employees GROUP BY Department HAVING COUNT(*) > 5",
                expected_tables=["Employees"],
                expected_columns=["Department"],
                description="SELECT with GROUP BY and HAVING",
                complexity="complex"
            ),
            
            # ORDER BY clauses
            TestCase(
                name="SELECT_ORDER_BY",
                sql="SELECT Name, Salary FROM Employees ORDER BY Salary DESC",
                expected_tables=["Employees"],
                expected_columns=["Name", "Salary"],
                description="SELECT with ORDER BY",
                complexity="complex"
            ),
            
            TestCase(
                name="SELECT_ORDER_BY_MULTIPLE",
                sql="SELECT Name, Department, Salary FROM Employees ORDER BY Department ASC, Salary DESC",
                expected_tables=["Employees"],
                expected_columns=["Name", "Department", "Salary"],
                description="SELECT with multiple ORDER BY columns",
                complexity="complex"
            ),
            
            # Complex combinations
            TestCase(
                name="SELECT_FULL_COMPLEX",
                sql="SELECT e.Department, COUNT(e.ID) as EmployeeCount, AVG(e.Salary) as AvgSalary FROM Employees e WHERE e.Active = 1 GROUP BY e.Department HAVING COUNT(e.ID) > 3 ORDER BY AvgSalary DESC",
                expected_tables=["Employees"],
                expected_columns=["Department", "ID", "Salary", "Active"],
                description="Complex SELECT with all clauses",
                complexity="complex"
            ),
            
            # JOINs with GROUP BY
            TestCase(
                name="SELECT_JOIN_GROUP_BY",
                sql="SELECT d.Name, COUNT(e.ID) as EmployeeCount FROM Departments d LEFT JOIN Employees e ON d.ID = e.DepartmentID GROUP BY d.Name",
                expected_tables=["Departments", "Employees"],
                expected_columns=["Name", "ID", "DepartmentID"],
                description="JOIN with GROUP BY",
                complexity="complex"
            ),
            
            # Functions and expressions
            TestCase(
                name="SELECT_WITH_FUNCTIONS",
                sql="SELECT f.ID, f.Name, f.ValidityRating, COUNT(e.ID) as EvidenceCount, AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage FROM REF_Fact f LEFT JOIN REF_Evidence e ON f.ID = e.FactID WHERE f.DataSource LIKE '%Self-Healer%' GROUP BY f.ID, f.Name, f.ValidityRating",
                expected_tables=["REF_Fact", "REF_Evidence"],
                expected_columns=["ID", "Name", "ValidityRating", "FactID", "DataSource"],
                description="Complex query with functions and window functions",
                complexity="complex"
            ),
            
            # LIMIT clause
            TestCase(
                name="SELECT_WITH_LIMIT",
                sql="SELECT Name, Salary FROM Employees ORDER BY Salary DESC LIMIT 10",
                expected_tables=["Employees"],
                expected_columns=["Name", "Salary"],
                description="SELECT with LIMIT",
                complexity="complex"
            ),
        ]
    
    def _build_insert_tests(self) -> List[TestCase]:
        """Build INSERT test cases."""
        return [
            # Basic INSERT
            TestCase(
                name="INSERT_BASIC",
                sql="INSERT INTO Users (Name, Email) VALUES ('John Doe', '<EMAIL>')",
                expected_tables=["Users"],
                expected_columns=["Name", "Email"],
                description="Basic INSERT with column list",
                complexity="simple"
            ),
            
            TestCase(
                name="INSERT_NO_COLUMNS",
                sql="INSERT INTO Users VALUES ('John Doe', '<EMAIL>', 1)",
                expected_tables=["Users"],
                expected_columns=[],
                description="INSERT without column list",
                complexity="simple"
            ),
            
            # INSERT with SELECT
            TestCase(
                name="INSERT_SELECT",
                sql="INSERT INTO ActiveUsers (Name, Email) SELECT Name, Email FROM Users WHERE Active = 1",
                expected_tables=["ActiveUsers", "Users"],
                expected_columns=["Name", "Email", "Active"],
                description="INSERT with SELECT",
                complexity="intermediate"
            ),
            
            # INSERT with schema
            TestCase(
                name="INSERT_SCHEMA_QUALIFIED",
                sql="INSERT INTO dbo.Users (Name, Email, Status) VALUES ('Jane Doe', '<EMAIL>', 'Active')",
                expected_tables=["Users"],
                expected_columns=["Name", "Email", "Status"],
                description="INSERT with schema-qualified table",
                complexity="simple"
            ),
        ]
    
    def _build_update_tests(self) -> List[TestCase]:
        """Build UPDATE test cases."""
        return [
            # Basic UPDATE
            TestCase(
                name="UPDATE_BASIC",
                sql="UPDATE Users SET Name = 'Jane Doe' WHERE ID = 1",
                expected_tables=["Users"],
                expected_columns=["Name", "ID"],
                description="Basic UPDATE",
                complexity="simple"
            ),
            
            TestCase(
                name="UPDATE_MULTIPLE_COLUMNS",
                sql="UPDATE Users SET Name = 'Jane Doe', Email = '<EMAIL>', Status = 'Active' WHERE ID = 1",
                expected_tables=["Users"],
                expected_columns=["Name", "Email", "Status", "ID"],
                description="UPDATE multiple columns",
                complexity="simple"
            ),
            
            # UPDATE with alias
            TestCase(
                name="UPDATE_WITH_ALIAS",
                sql="UPDATE Users u SET u.Name = 'Jane Doe', u.Status = 'Active' WHERE u.ID = 1",
                expected_tables=["Users"],
                expected_columns=["Name", "Status", "ID"],
                description="UPDATE with table alias",
                complexity="intermediate"
            ),
            
            # UPDATE with JOIN
            TestCase(
                name="UPDATE_WITH_JOIN",
                sql="UPDATE Users u SET u.Status = 'Inactive' FROM Users u JOIN Departments d ON u.DepartmentID = d.ID WHERE d.Name = 'Closed'",
                expected_tables=["Users", "Departments"],
                expected_columns=["Status", "DepartmentID", "ID", "Name"],
                description="UPDATE with JOIN",
                complexity="complex"
            ),
            
            # UPDATE with schema
            TestCase(
                name="UPDATE_SCHEMA_QUALIFIED",
                sql="UPDATE dbo.Users SET Status = 'Inactive' WHERE LastLogin < '2023-01-01'",
                expected_tables=["Users"],
                expected_columns=["Status", "LastLogin"],
                description="UPDATE with schema-qualified table",
                complexity="simple"
            ),
        ]
    
    def _build_delete_tests(self) -> List[TestCase]:
        """Build DELETE test cases."""
        return [
            # Basic DELETE
            TestCase(
                name="DELETE_BASIC",
                sql="DELETE FROM Users WHERE ID = 1",
                expected_tables=["Users"],
                expected_columns=["ID"],
                description="Basic DELETE",
                complexity="simple"
            ),
            
            TestCase(
                name="DELETE_COMPLEX_WHERE",
                sql="DELETE FROM Users WHERE Status = 'Inactive' AND LastLogin < '2023-01-01'",
                expected_tables=["Users"],
                expected_columns=["Status", "LastLogin"],
                description="DELETE with complex WHERE",
                complexity="simple"
            ),
            
            # DELETE with alias
            TestCase(
                name="DELETE_WITH_ALIAS",
                sql="DELETE FROM Users u WHERE u.Status = 'Inactive'",
                expected_tables=["Users"],
                expected_columns=["Status"],
                description="DELETE with table alias",
                complexity="intermediate"
            ),
            
            # DELETE with schema
            TestCase(
                name="DELETE_SCHEMA_QUALIFIED",
                sql="DELETE FROM dbo.Users WHERE Status = 'Deleted'",
                expected_tables=["Users"],
                expected_columns=["Status"],
                description="DELETE with schema-qualified table",
                complexity="simple"
            ),
        ]
    
    def _build_edge_case_tests(self) -> List[TestCase]:
        """Build edge case test cases."""
        return [
            # Whitespace variations
            TestCase(
                name="EDGE_EXTRA_WHITESPACE",
                sql="SELECT    u.Name   ,   u.Email   FROM    Users   u   WHERE   u.Active   =   1",
                expected_tables=["Users"],
                expected_columns=["Name", "Email", "Active"],
                description="Extra whitespace handling",
                complexity="simple"
            ),
            
            # Case variations
            TestCase(
                name="EDGE_MIXED_CASE",
                sql="select u.Name, u.Email from Users u where u.Active = 1",
                expected_tables=["Users"],
                expected_columns=["Name", "Email", "Active"],
                description="Mixed case SQL keywords",
                complexity="simple"
            ),
            
            # Complex table names
            TestCase(
                name="EDGE_UNDERSCORE_TABLES",
                sql="SELECT rf.ID, rf.Name FROM REF_Fact rf JOIN XRF_Fact_Evidence xfe ON rf.ID = xfe.FactID",
                expected_tables=["REF_Fact", "XRF_Fact_Evidence"],
                expected_columns=["ID", "Name", "FactID"],
                description="Tables with underscores",
                complexity="intermediate"
            ),
            
            # Numbers in names
            TestCase(
                name="EDGE_NUMBERS_IN_NAMES",
                sql="SELECT t1.Field1, t2.Field2 FROM Table1 t1 JOIN Table2 t2 ON t1.ID = t2.Table1ID",
                expected_tables=["Table1", "Table2"],
                expected_columns=["Field1", "Field2", "ID", "Table1ID"],
                description="Numbers in table and column names",
                complexity="intermediate"
            ),
            
            # Long aliases
            TestCase(
                name="EDGE_LONG_ALIASES",
                sql="SELECT employee.Name, department.Title FROM Employees employee JOIN Departments department ON employee.DeptID = department.ID",
                expected_tables=["Employees", "Departments"],
                expected_columns=["Name", "Title", "DeptID", "ID"],
                description="Long descriptive aliases",
                complexity="intermediate"
            ),
        ]
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all test cases and return results."""
        print("🧪 Running SQL Parser Test Suite")
        print("=" * 80)
        
        total_tests = 0
        passed_tests = 0
        failed_tests = []
        
        for category, test_cases in self.test_cases.items():
            print(f"\n📂 {category} ({len(test_cases)} tests)")
            print("-" * 60)
            
            category_passed = 0
            category_total = len(test_cases)
            
            for test_case in test_cases:
                total_tests += 1
                result = self._run_single_test(test_case)
                
                if result['passed']:
                    passed_tests += 1
                    category_passed += 1
                    print(f"   ✅ {test_case.name}")
                else:
                    failed_tests.append(result)
                    print(f"   ❌ {test_case.name}")
                    print(f"      Expected Tables: {test_case.expected_tables}")
                    print(f"      Actual Tables: {result['actual_tables']}")
                    print(f"      Expected Columns: {test_case.expected_columns}")
                    print(f"      Actual Columns: {result['actual_columns']}")
                    if result['error']:
                        print(f"      Error: {result['error']}")
            
            print(f"   📊 Category Result: {category_passed}/{category_total} passed")
        
        # Summary
        print(f"\n🎯 FINAL RESULTS")
        print("=" * 80)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {len(failed_tests)}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests:
            print(f"\n❌ FAILED TESTS:")
            for failure in failed_tests:
                print(f"   - {failure['test_name']}: {failure['description']}")
        else:
            print(f"\n🎉 ALL TESTS PASSED!")
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'success_rate': (passed_tests/total_tests)*100,
            'all_passed': len(failed_tests) == 0
        }
    
    def _run_single_test(self, test_case: TestCase) -> Dict[str, Any]:
        """Run a single test case."""
        try:
            result = self.validator.extract_sql_components(test_case.sql)
            
            actual_tables = sorted(result['tables'])
            expected_tables = sorted(test_case.expected_tables)
            
            actual_columns = sorted(result['columns'])
            expected_columns = sorted(test_case.expected_columns)
            
            tables_match = set(actual_tables) == set(expected_tables)
            columns_match = set(actual_columns) == set(expected_columns)
            
            passed = tables_match and columns_match
            
            return {
                'test_name': test_case.name,
                'description': test_case.description,
                'passed': passed,
                'actual_tables': actual_tables,
                'expected_tables': expected_tables,
                'actual_columns': actual_columns,
                'expected_columns': expected_columns,
                'tables_match': tables_match,
                'columns_match': columns_match,
                'error': None
            }
            
        except Exception as e:
            return {
                'test_name': test_case.name,
                'description': test_case.description,
                'passed': False,
                'actual_tables': [],
                'expected_tables': test_case.expected_tables,
                'actual_columns': [],
                'expected_columns': test_case.expected_columns,
                'tables_match': False,
                'columns_match': False,
                'error': str(e)
            }
    
    def run_category_tests(self, category: str) -> Dict[str, Any]:
        """Run tests for a specific category."""
        if category not in self.test_cases:
            print(f"❌ Category '{category}' not found")
            print(f"Available categories: {list(self.test_cases.keys())}")
            return {'error': f'Category {category} not found'}
        
        print(f"🧪 Running {category} Tests")
        print("=" * 60)
        
        test_cases = self.test_cases[category]
        passed = 0
        failed = []
        
        for test_case in test_cases:
            result = self._run_single_test(test_case)
            
            if result['passed']:
                passed += 1
                print(f"   ✅ {test_case.name} - {test_case.description}")
            else:
                failed.append(result)
                print(f"   ❌ {test_case.name} - {test_case.description}")
                print(f"      Expected: T={test_case.expected_tables}, C={test_case.expected_columns}")
                print(f"      Actual: T={result['actual_tables']}, C={result['actual_columns']}")
        
        print(f"\n📊 {category} Results: {passed}/{len(test_cases)} passed")
        
        return {
            'category': category,
            'total_tests': len(test_cases),
            'passed_tests': passed,
            'failed_tests': failed,
            'success_rate': (passed/len(test_cases))*100
        }

def main():
    """Main test runner."""
    test_suite = SQLParserTestSuite()
    
    if len(sys.argv) > 1:
        # Run specific category
        category = sys.argv[1].upper()
        test_suite.run_category_tests(category)
    else:
        # Run all tests
        test_suite.run_all_tests()

if __name__ == "__main__":
    main()
