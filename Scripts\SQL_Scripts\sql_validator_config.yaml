# SQL Validator and Repair System Configuration
# ==============================================
# This configuration file controls all aspects of the SQL validation and repair process.
# Modify these settings to match your environment and requirements.

# Database Connection Settings
# ============================
database:
  # Database name to connect to
  name: "knowledgebase"
  
  # Database server location
  # Options: "local", "localhost", or IP address (e.g., "*************")
  location: "local"
  
  # Database server port (optional, uses default if not specified)
  # port: 1433  # SQL Server default
  # port: 5432  # PostgreSQL default
  # port: 3306  # MySQL default
  
  # Authentication credentials
  authentication:
    # Login username
    username: ""
    
    # Password (leave empty to use Windows Authentication or environment variable)
    password: ""
    
    # Use Windows Authentication (SQL Server only)
    use_windows_auth: true
    
    # Environment variable names for credentials (optional)
    username_env_var: "DB_USERNAME"
    password_env_var: "DB_PASSWORD"

# File Search Settings
# ====================
file_search:
  # File types to search for SQL statements
  # Add or remove extensions as needed
  file_types:
    - ".py"      # Python files
    - ".sql"     # SQL script files
    - ".js"      # JavaScript files
    - ".ts"      # TypeScript files
    - ".cs"      # C# files
    - ".java"    # Java files
    - ".php"     # PHP files
    - ".rb"      # Ruby files
    - ".go"      # Go files
    - ".rs"      # Rust files
  
  # Directory paths to search
  # Can be absolute paths or relative to the project root
  search_paths:
    - "Self_Healer"
    - "Scripts"
    - "tests"
    - "n8n_builder"
    # - "C:/absolute/path/to/search"  # Example absolute path
  
  # Recursive search through subdirectories
  recursive_search: true
  
  # Maximum directory depth for recursive search (0 = unlimited)
  max_depth: 0
  
  # Exclude patterns (files/folders to skip)
  exclude_patterns:
    - "__pycache__"
    - ".git"
    - ".vscode"
    - "node_modules"
    - "*.pyc"
    - "*.log"
    - ".env"
    - "venv"
    - ".venv"

# SQL Detection Settings
# ======================
sql_detection:
  # SQL keywords that must be present for detection
  required_keywords:
    - "SELECT"
    - "INSERT"
    - "UPDATE"
    - "DELETE"
  
  # Supporting keywords that strengthen detection confidence
  supporting_keywords:
    - "FROM"
    - "WHERE"
    - "JOIN"
    - "INTO"
    - "SET"
    - "VALUES"
    - "ORDER BY"
    - "GROUP BY"
    - "HAVING"
    - "LIMIT"
    - "TOP"
  
  # Quote types to recognize for SQL statements
  quote_types:
    - "single"        # 'SQL statement'
    - "double"        # "SQL statement"
    - "triple_single" # '''SQL statement'''
    - "triple_double" # """SQL statement"""
  
  # Minimum SQL statement length to consider
  min_statement_length: 10
  
  # Case sensitivity for keyword detection
  case_sensitive: false

# Repair Settings
# ===============
repair:
  # Database table name corrections
  # Format: "incorrect_name": "correct_name"
  table_name_fixes:
    "REF_Entities": "REF_Entity"
    "REF_Attributes": "REF_Attribute"
    "REF_EntityValues": "REF_EntityValue"
    "REF_Facts": "REF_Fact"
    "REF_Opinions": "REF_Opinion"
    "REF_Sources": "REF_Source"
    "REF_Categories": "REF_Category"
    "XRF_Entity_Attribute_Value": "XRF_EntityAttributeValue"
    "XRF_Entity_AttributeValue": "XRF_EntityAttributeValue"
  
  # Column name corrections (if needed)
  column_name_fixes: {}
    # Example: "old_column_name": "new_column_name"
  
  # Create backups before making changes
  create_backups: true
  
  # Backup directory (relative to project root)
  backup_directory: "data/repair_backups"
  
  # Backup file naming pattern
  backup_pattern: "{filename}.backup_{timestamp}"

# Logging Settings
# ================
logging:
  # Enable comprehensive logging
  enabled: true
  
  # Log directory (relative to project root)
  log_directory: "data/database_repair_logs"
  
  # Log file naming patterns
  detailed_log_pattern: "repair_session_{session_id}.json"
  summary_log_pattern: "repair_summary_{session_id}.csv"
  validation_log_pattern: "sql_validation_{session_id}.txt"
  
  # Log levels: DEBUG, INFO, WARNING, ERROR
  log_level: "INFO"
  
  # Include SQL statement content in logs
  log_sql_content: true
  
  # Maximum log file size (MB)
  max_log_size: 50

# Validation Settings
# ===================
validation:
  # Enable SQL validation testing
  enabled: true
  
  # Connection timeout for database queries (seconds)
  connection_timeout: 30
  
  # Query timeout for validation tests (seconds)
  query_timeout: 10
  
  # Maximum number of validation attempts per SQL statement
  max_retry_attempts: 3
  
  # Skip validation for certain SQL types
  skip_validation_for: []
    # Example: - "CREATE"
    # Example: - "DROP"
    # Example: - "ALTER"
  
  # Treat validation failures as warnings instead of errors
  validation_failures_as_warnings: false

# Performance Settings
# ====================
performance:
  # Maximum number of files to process in parallel
  max_parallel_files: 4
  
  # Maximum number of SQL statements to validate in parallel
  max_parallel_validations: 2
  
  # Enable progress reporting
  show_progress: true
  
  # Progress update interval (number of files)
  progress_interval: 10

# Output Settings
# ===============
output:
  # Verbosity level: QUIET, NORMAL, VERBOSE, DEBUG
  verbosity: "NORMAL"
  
  # Show detailed SQL detection results
  show_detection_details: true
  
  # Show validation results for each SQL statement
  show_validation_details: false
  
  # Generate HTML report
  generate_html_report: true
  
  # HTML report template (optional)
  html_report_template: ""
  
  # Export results to CSV
  export_csv: true
  
  # Export results to JSON
  export_json: true

# Advanced Settings
# =================
advanced:
  # Enable experimental features
  experimental_features: false
  
  # Custom SQL detection patterns (regex)
  custom_patterns: []
  
  # Plugin directory for custom extensions
  plugin_directory: "Scripts/SQL_Scripts/plugins"
  
  # Enable plugin system
  enable_plugins: false
