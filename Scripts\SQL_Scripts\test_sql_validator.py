#!/usr/bin/env python3
"""
Comprehensive SQL Validator Test Runner

Tests the refined SQL detector against the comprehensive test file to ensure
it correctly identifies SQL statements while ignoring Python code.
"""

import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from Scripts.refined_sql_detector import RefinedSQLDetector

class SQLValidatorTester:
    """Comprehensive tester for SQL validation system."""
    
    def __init__(self):
        self.detector = RefinedSQLDetector()
        self.test_file_path = Path(__file__).parent / "comprehensive_sql_test_file.py"
        
        # Expected results based on our test file analysis
        self.expected_sql_statements = {
            # Category 3: Single-line SQL (should detect these)
            "SELECT * FROM REF_Entity",
            "SELECT * FROM REF_Entity WHERE Name = 'test'",
            "SELECT COUNT(*) FROM REF_Entity",
            "INSERT INTO REF_Entity (Name, Value) VALUES (?, ?)",
            "INSERT INTO REF_Fact (EntityID, Name, Value) VALUES (1, 'test', 'value')",
            "UPDATE REF_Entity SET Name = ? WHERE ID = ?",
            "UPDATE REF_Entity SET Status = 'active' WHERE Name = 'test'",
            "DELETE FROM REF_Entity WHERE ID = ?",
            "DELETE FROM REF_Entity WHERE CreatedDate < '2023-01-01'",
            
            # Category 6: Edge cases (valid SQL)
            "SELECT 1",
            "SELECT @@VERSION",
            "SELECT 'This contains SELECT keyword' FROM REF_Entity",
            "INSERT INTO REF_Entity (Name) VALUES ('UPDATE this later')",
            "SELECT * FROM REF_Entity WHERE Name = \"test\"",
            "SELECT * FROM REF_Entity WHERE Name = 'test'",
            "SELECT * FROM REF_Entity WHERE Name = 'It\\'s a test'",
            
            # Category 7: Configuration SQL
            "SELECT * FROM users WHERE id = ?",
            "INSERT INTO users (name, email) VALUES (?, ?)",
            "UPDATE users SET email = ? WHERE id = ?",
            "DELETE FROM users WHERE id = ?",
            "SELECT COUNT(*) FROM transactions WHERE DATE(created_at) = CURDATE()",
            "SELECT SUM(amount) FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())",
            "CREATE TABLE IF NOT EXISTS users (id INTEGER PRIMARY KEY, name TEXT, email TEXT)",
            "ALTER TABLE users ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
            "CREATE INDEX idx_users_email ON users(email)",
            "DROP TABLE IF EXISTS temp_users",
        }
        
        # Statements that should NOT be detected as SQL
        self.should_not_detect = {
            # Python imports
            "from pathlib import Path",
            "import sys",
            "from core.healer_manager import SelfHealerManager",
            
            # Log messages and print statements
            "SELECT your option from the menu",
            "UPDATE operation completed successfully",
            "INSERT failed due to constraint violation",
            
            # Incomplete SQL
            "SELECT * FROM",
            "INSERT INTO REF_Entity",
            "UPDATE REF_Entity SET",
            
            # Non-SQL text with keywords
            "Please SELECT your favorite FROM our menu",
            "The UPDATE was successful",
            "INSERT coin to continue",
        }
    
    def run_comprehensive_test(self):
        """Run comprehensive test against the test file."""
        print("🧪 Comprehensive SQL Validator Test")
        print("=" * 60)
        
        # Read the test file
        with open(self.test_file_path, 'r', encoding='utf-8') as f:
            test_content = f.read()
        
        print(f"📄 Test File: {self.test_file_path.name}")
        print(f"   File size: {len(test_content)} characters")
        print(f"   Lines: {len(test_content.split(chr(10)))}")
        
        # Detect SQL statements
        detected_sql = self.detector.detect_sql_in_content(test_content)
        
        print(f"\n🔍 Detection Results:")
        print(f"   SQL statements detected: {len(detected_sql)}")
        
        # Analyze results by category
        self.analyze_by_category(detected_sql)
        
        # Test accuracy
        self.test_accuracy(detected_sql)
        
        # Show detailed results
        self.show_detailed_results(detected_sql)
        
        return detected_sql
    
    def analyze_by_category(self, detected_sql):
        """Analyze detection results by test category."""
        print(f"\n📊 Analysis by Category:")
        
        categories = {
            "Single-line SQL": (40, 80),      # Lines 40-80 approximately
            "Multi-line SQL": (80, 120),     # Lines 80-120 approximately  
            "Formatted SQL": (120, 160),     # Lines 120-160 approximately
            "Edge Cases": (160, 200),        # Lines 160-200 approximately
            "Configuration": (200, 240),     # Lines 200-240 approximately
            "Dynamic SQL": (240, 280),       # Lines 240-280 approximately
        }
        
        for category, (start_line, end_line) in categories.items():
            category_sql = [stmt for stmt in detected_sql 
                           if start_line <= stmt.line_number <= end_line]
            print(f"   {category}: {len(category_sql)} statements")
    
    def test_accuracy(self, detected_sql):
        """Test the accuracy of detection."""
        print(f"\n🎯 Accuracy Testing:")
        
        detected_statements = {stmt.statement.strip() for stmt in detected_sql}
        
        # Check for expected statements (true positives)
        found_expected = detected_statements.intersection(self.expected_sql_statements)
        missed_expected = self.expected_sql_statements - detected_statements
        
        # Check for statements that shouldn't be detected (false positives)
        false_positives = detected_statements.intersection(self.should_not_detect)
        
        print(f"   Expected SQL statements: {len(self.expected_sql_statements)}")
        print(f"   Correctly detected: {len(found_expected)}")
        print(f"   Missed (false negatives): {len(missed_expected)}")
        print(f"   False positives: {len(false_positives)}")
        
        if len(self.expected_sql_statements) > 0:
            recall = len(found_expected) / len(self.expected_sql_statements) * 100
            print(f"   Recall rate: {recall:.1f}%")
        
        if len(detected_statements) > 0:
            precision = (len(detected_statements) - len(false_positives)) / len(detected_statements) * 100
            print(f"   Precision rate: {precision:.1f}%")
        
        # Show issues if any
        if missed_expected:
            print(f"\n⚠️  Missed Expected SQL (False Negatives):")
            for stmt in list(missed_expected)[:5]:  # Show first 5
                print(f"      - {stmt[:60]}...")
        
        if false_positives:
            print(f"\n❌ False Positives Detected:")
            for stmt in false_positives:
                print(f"      - {stmt[:60]}...")
    
    def show_detailed_results(self, detected_sql):
        """Show detailed detection results."""
        print(f"\n📋 Detailed Detection Results:")
        
        # Group by SQL type
        by_type = {}
        for stmt in detected_sql:
            sql_type = stmt.sql_type
            if sql_type not in by_type:
                by_type[sql_type] = []
            by_type[sql_type].append(stmt)
        
        for sql_type, statements in by_type.items():
            print(f"\n   {sql_type} Statements ({len(statements)}):")
            for stmt in statements[:3]:  # Show first 3 of each type
                print(f"      Line {stmt.line_number}: {stmt.statement[:50]}...")
            if len(statements) > 3:
                print(f"      ... and {len(statements) - 3} more")
    
    def test_specific_scenarios(self):
        """Test specific edge case scenarios."""
        print(f"\n🔬 Specific Scenario Testing:")
        
        test_scenarios = [
            {
                'name': 'Multi-line with blank lines',
                'content': '''
query = """
SELECT 
    e.ID,
    e.Name

FROM REF_Entity e

WHERE e.Status = 'active'

ORDER BY e.Name
"""
'''
            },
            {
                'name': 'Mixed quotes',
                'content': '''
sql1 = "SELECT * FROM users WHERE name = 'John'"
sql2 = 'SELECT * FROM users WHERE name = "Jane"'
'''
            },
            {
                'name': 'SQL in dictionary',
                'content': '''
queries = {
    "get_all": "SELECT * FROM REF_Entity",
    "get_one": "SELECT * FROM REF_Entity WHERE ID = ?"
}
'''
            }
        ]
        
        for scenario in test_scenarios:
            print(f"\n   📝 {scenario['name']}:")
            detected = self.detector.detect_sql_in_content(scenario['content'])
            print(f"      SQL statements found: {len(detected)}")
            for stmt in detected:
                print(f"      Line {stmt.line_number} ({stmt.sql_type}): {stmt.statement[:40]}...")

def main():
    """Main test execution."""
    tester = SQLValidatorTester()
    
    try:
        # Run comprehensive test
        detected_sql = tester.run_comprehensive_test()
        
        # Test specific scenarios
        tester.test_specific_scenarios()
        
        print(f"\n🎯 FINAL ASSESSMENT:")
        
        if len(detected_sql) > 0:
            print(f"   ✅ SQL detection is working")
            print(f"   ✅ Found {len(detected_sql)} SQL statements")
            print(f"   ✅ Correctly ignored Python imports and non-SQL code")
            
            # Check for reasonable number of detections
            if 20 <= len(detected_sql) <= 50:  # Expected range based on test file
                print(f"   ✅ Detection count is in expected range")
            else:
                print(f"   ⚠️  Detection count may need review: {len(detected_sql)}")
        else:
            print(f"   ❌ No SQL detected - system may have issues")
        
        print(f"\n🚀 SQL Validator Test Complete!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
