# Setup Centralized Docker Share Structure
# This script creates a centralized Docker share folder that can be used by all projects

param(
    [string]$ShareRoot = "C:\Docker_Share",
    [switch]$Force = $false
)

Write-Host "Setting up Centralized Docker Share Structure" -ForegroundColor Cyan
Write-Host "=" * 60

# Function to create directory with proper permissions
function New-DockerShareDirectory {
    param([string]$Path, [string]$Description)
    
    if (-not (Test-Path $Path)) {
        New-Item -ItemType Directory -Path $Path -Force | Out-Null
        Write-Host "Created: $Path - $Description" -ForegroundColor Green
    } else {
        Write-Host "Exists: $Path - $Description" -ForegroundColor Yellow
    }
}

# Create main Docker share structure
Write-Host "`nCreating Docker Share Directory Structure..." -ForegroundColor Yellow

New-DockerShareDirectory -Path $ShareRoot -Description "Main Docker Share Root"
New-DockerShareDirectory -Path "$ShareRoot\N8N" -Description "N8N Project Files"
New-DockerShareDirectory -Path "$ShareRoot\N8N\dns_reports" -Description "DNS Security Reports"
New-DockerShareDirectory -Path "$ShareRoot\N8N\workflows" -Description "N8N Workflow Exports"
New-DockerShareDirectory -Path "$ShareRoot\N8N\logs" -Description "N8N Application Logs"
New-DockerShareDirectory -Path "$ShareRoot\N8N\temp" -Description "Temporary Processing Files"

# Create structure for future projects
New-DockerShareDirectory -Path "$ShareRoot\Common" -Description "Shared Resources for All Projects"
New-DockerShareDirectory -Path "$ShareRoot\Common\scripts" -Description "Reusable Scripts"
New-DockerShareDirectory -Path "$ShareRoot\Common\templates" -Description "File Templates"
New-DockerShareDirectory -Path "$ShareRoot\Common\logs" -Description "System-wide Logs"

# Set proper permissions (allow Docker to access)
Write-Host "`nSetting Directory Permissions..." -ForegroundColor Yellow
try {
    # Give full control to current user and system
    icacls $ShareRoot /grant "${env:USERNAME}:(OI)(CI)F" /T | Out-Null
    icacls $ShareRoot /grant "SYSTEM:(OI)(CI)F" /T | Out-Null
    icacls $ShareRoot /grant "Administrators:(OI)(CI)F" /T | Out-Null
    Write-Host "Permissions set successfully" -ForegroundColor Green
} catch {
    Write-Host "Warning: Could not set permissions: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Migrate existing DNS reports if they exist
$currentDataPath = "..\data\dns_reports"
if (Test-Path $currentDataPath) {
    Write-Host "`nMigrating existing DNS reports..." -ForegroundColor Yellow
    
    if ($Force -or (Read-Host "Migrate existing DNS reports to centralized location? (y/N)") -eq 'y') {
        try {
            Copy-Item -Path "$currentDataPath\*" -Destination "$ShareRoot\N8N\dns_reports\" -Recurse -Force
            Write-Host "DNS reports migrated successfully" -ForegroundColor Green

            # Create backup of original
            $backupPath = "..\data\dns_reports_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            Rename-Item -Path $currentDataPath -NewName $backupPath
            Write-Host "Original data backed up to: $backupPath" -ForegroundColor Green
        } catch {
            Write-Host "Error migrating DNS reports: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Create configuration file for projects to reference
$configContent = @"
# Docker Share Configuration
# This file contains the centralized Docker share paths for all projects

DOCKER_SHARE_ROOT=C:\Docker_Share
N8N_SHARE_PATH=C:\Docker_Share\N8N
COMMON_SHARE_PATH=C:\Docker_Share\Common

# Container mount paths (use these in docker-compose.yml)
# - C:\Docker_Share\N8N:/home/<USER>/shared
# - C:\Docker_Share\Common:/home/<USER>/common

# Project-specific paths
DNS_REPORTS_PATH=C:\Docker_Share\N8N\dns_reports
N8N_WORKFLOWS_PATH=C:\Docker_Share\N8N\workflows
N8N_LOGS_PATH=C:\Docker_Share\N8N\logs
N8N_TEMP_PATH=C:\Docker_Share\N8N\temp
"@

$configPath = "$ShareRoot\docker-share-config.env"
$configContent | Out-File -FilePath $configPath -Encoding UTF8
Write-Host "Configuration saved to: $configPath" -ForegroundColor Green

# Create README for the Docker share
$readmeContent = @"
# Centralized Docker Share

This directory provides a centralized location for Docker volume mounts across all projects.

## 📁 Directory Structure

- **N8N/**: Files specific to N8N Builder project
  - dns_reports/: DNS security monitoring reports
  - workflows/: Exported N8N workflows
  - logs/: Application logs
  - temp/: Temporary processing files

- **Common/**: Shared resources for all projects
  - scripts/: Reusable utility scripts
  - templates/: File templates
  - logs/: System-wide logs

## 🔧 Usage in Docker Compose

```yaml
volumes:
  - C:\Docker_Share\N8N:/home/<USER>/shared
  - C:\Docker_Share\Common:/home/<USER>/common
```

## 🚀 Benefits

1. **Portable**: Projects can be moved without breaking file paths
2. **Scalable**: Easy to add new projects with their own folders
3. **Organized**: Clear separation between project-specific and shared resources
4. **Maintainable**: Centralized location for all Docker file operations

## 📝 Adding New Projects

1. Create folder: C:\Docker_Share\[PROJECT_NAME]
2. Add volume mount in docker-compose.yml
3. Update docker-share-config.env if needed
"@

$readmePath = "$ShareRoot\README.md"
$readmeContent | Out-File -FilePath $readmePath -Encoding UTF8
Write-Host "Documentation created: $readmePath" -ForegroundColor Green

Write-Host "`nCentralized Docker Share Setup Complete!" -ForegroundColor Green
Write-Host "Location: $ShareRoot" -ForegroundColor Cyan
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "   1. Update docker-compose.yml to use: C:\Docker_Share\N8N:/home/<USER>/shared" -ForegroundColor White
Write-Host "   2. Run the Docker compose update script" -ForegroundColor White
Write-Host "   3. Test the N8N workflow with new paths" -ForegroundColor White
