# Update Docker Compose to use Centralized Docker Share
# This script updates docker-compose.yml files to use the new centralized paths

param(
    [string]$ShareRoot = "C:\Docker_Share",
    [switch]$Backup
)

Write-Host "Updating Docker Compose for Centralized Share" -ForegroundColor Cyan
Write-Host "=" * 50

$dockerComposeFiles = @(
    "n8n-docker\docker-compose.yml",
    "n8n-docker\docker-compose.dev.yml"
)

foreach ($composeFile in $dockerComposeFiles) {
    if (Test-Path $composeFile) {
        Write-Host "`nProcessing: $composeFile" -ForegroundColor Yellow

        # Create backup if requested
        if ($Backup) {
            $backupFile = "$composeFile.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            Copy-Item -Path $composeFile -Destination $backupFile
            Write-Host "Backup created: $backupFile" -ForegroundColor Green
        }
        
        # Read current content
        $content = Get-Content $composeFile -Raw
        
        # Replace relative data mount with centralized path
        $oldPattern = '\.\./data:/home/<USER>/shared'
        $newPattern = "$ShareRoot\N8N:/home/<USER>/shared"
        
        if ($content -match [regex]::Escape($oldPattern)) {
            $content = $content -replace [regex]::Escape($oldPattern), $newPattern
            Write-Host "✅ Updated data volume mount" -ForegroundColor Green
        } else {
            Write-Host "ℹ️  No data volume mount found to update" -ForegroundColor Blue
        }
        
        # Add common share mount if not present
        $commonMount = "$ShareRoot\Common:/home/<USER>/common"
        if ($content -notmatch [regex]::Escape($commonMount)) {
            # Find the volumes section and add the common mount
            $volumesPattern = '(\s+volumes:\s*\n)((?:\s+- [^\n]+\n)*)'
            if ($content -match $volumesPattern) {
                $replacement = "`$1`$2      # Mount common shared resources`n      - $commonMount`n"
                $content = $content -replace $volumesPattern, $replacement
                Write-Host "✅ Added common volume mount" -ForegroundColor Green
            }
        }
        
        # Write updated content
        $content | Out-File -FilePath $composeFile -Encoding UTF8 -NoNewline
        Write-Host "✅ Updated: $composeFile" -ForegroundColor Green
        
    } else {
        Write-Host "⚠️  File not found: $composeFile" -ForegroundColor Yellow
    }
}

# Create a validation script
$validationScript = @"
# Validate Docker Share Setup
Write-Host "🧪 Validating Docker Share Setup..." -ForegroundColor Cyan

# Check if centralized directories exist
`$paths = @(
    "$ShareRoot",
    "$ShareRoot\N8N",
    "$ShareRoot\N8N\dns_reports",
    "$ShareRoot\Common"
)

foreach (`$path in `$paths) {
    if (Test-Path `$path) {
        Write-Host "✅ `$path" -ForegroundColor Green
    } else {
        Write-Host "❌ `$path" -ForegroundColor Red
    }
}

# Test Docker compose configuration
Write-Host "`n🐳 Testing Docker Compose Configuration..." -ForegroundColor Yellow
Set-Location "n8n-docker"
try {
    docker-compose config --quiet
    Write-Host "✅ Docker Compose configuration is valid" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose configuration error: `$(`$_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Run: docker-compose down" -ForegroundColor White
Write-Host "2. Run: docker-compose up -d" -ForegroundColor White
Write-Host "3. Test N8N workflow file operations" -ForegroundColor White
"@

$validationPath = "Scripts\Validate-Docker-Share.ps1"
$validationScript | Out-File -FilePath $validationPath -Encoding UTF8
Write-Host "✅ Validation script created: $validationPath" -ForegroundColor Green

Write-Host "`n🎉 Docker Compose Update Complete!" -ForegroundColor Green
Write-Host "📋 Summary of Changes:" -ForegroundColor Cyan
Write-Host "   • Updated volume mounts to use: $ShareRoot\N8N:/home/<USER>/shared" -ForegroundColor White
Write-Host "   • Added common resources mount: $ShareRoot\Common:/home/<USER>/common" -ForegroundColor White
Write-Host "   • Created backups of original files" -ForegroundColor White
Write-Host "   • Created validation script" -ForegroundColor White

Write-Host "`n⚠️  Important: Restart Docker containers to apply changes" -ForegroundColor Yellow
Write-Host "Run: cd n8n-docker && docker-compose down && docker-compose up -d" -ForegroundColor White
