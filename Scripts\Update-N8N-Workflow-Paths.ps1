# Update N8N Workflow Files to use Centralized Paths
# This script updates workflow JSON files to use the new centralized Docker share paths

param(
    [switch]$Backup,
    [switch]$DryRun
)

Write-Host "Updating N8N Workflow Paths" -ForegroundColor Cyan
Write-Host "=" * 40

# Find all workflow JSON files
$workflowFiles = @()
$searchPaths = @(
    "data\dns_reports\setup\*.json",
    "n8n-docker\data\*.json",
    "projects\*\*.json"
)

foreach ($searchPath in $searchPaths) {
    $files = Get-ChildItem -Path $searchPath -ErrorAction SilentlyContinue
    $workflowFiles += $files
}

if ($workflowFiles.Count -eq 0) {
    Write-Host "ℹ️  No workflow files found to update" -ForegroundColor Blue
    exit 0
}

Write-Host "📁 Found $($workflowFiles.Count) workflow files to check" -ForegroundColor Yellow

# Path mappings for updates
$pathMappings = @{
    '/home/<USER>/shared/dns_reports/' = '/home/<USER>/shared/dns_reports/'  # This stays the same
    '../data/dns_reports/' = '/home/<USER>/shared/dns_reports/'
    './data/dns_reports/' = '/home/<USER>/shared/dns_reports/'
    'data/dns_reports/' = '/home/<USER>/shared/dns_reports/'
}

foreach ($file in $workflowFiles) {
    Write-Host "`n📝 Processing: $($file.Name)" -ForegroundColor Yellow
    
    try {
        # Read and parse JSON
        $content = Get-Content $file.FullName -Raw -Encoding UTF8
        $originalContent = $content
        $updated = $false
        
        # Check if this looks like an N8N workflow
        if ($content -match '"nodes":\s*\[' -or $content -match '"name":\s*".*"') {
            
            # Apply path mappings
            foreach ($oldPath in $pathMappings.Keys) {
                $newPath = $pathMappings[$oldPath]
                if ($content -match [regex]::Escape($oldPath)) {
                    $content = $content -replace [regex]::Escape($oldPath), $newPath
                    Write-Host "  ✅ Updated path: $oldPath → $newPath" -ForegroundColor Green
                    $updated = $true
                }
            }
            
            # Look for any remaining problematic paths
            $problematicPatterns = @(
                '\.\.\/data\/',
                '\.\/data\/',
                'C:\\.*\\data\\',
                '/home/<USER>/shared/\.\./data/'
            )
            
            foreach ($pattern in $problematicPatterns) {
                if ($content -match $pattern) {
                    Write-Host "  ⚠️  Found potentially problematic path pattern: $pattern" -ForegroundColor Yellow
                }
            }
            
            # Save updated content if changes were made
            if ($updated -and -not $DryRun) {
                # Create backup
                if ($Backup) {
                    $backupPath = "$($file.FullName).backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
                    Copy-Item -Path $file.FullName -Destination $backupPath
                    Write-Host "  💾 Backup: $backupPath" -ForegroundColor Blue
                }
                
                # Write updated content
                $content | Out-File -FilePath $file.FullName -Encoding UTF8 -NoNewline
                Write-Host "  ✅ Updated: $($file.Name)" -ForegroundColor Green
            } elseif ($updated -and $DryRun) {
                Write-Host "  🔍 [DRY RUN] Would update: $($file.Name)" -ForegroundColor Cyan
            } else {
                Write-Host "  ℹ️  No changes needed: $($file.Name)" -ForegroundColor Blue
            }
            
        } else {
            Write-Host "  ⏭️  Skipped (not an N8N workflow): $($file.Name)" -ForegroundColor Gray
        }
        
    } catch {
        Write-Host "  ❌ Error processing $($file.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Create a summary report
$reportContent = @"
# N8N Workflow Path Update Report
Generated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

## Path Mappings Applied
$(foreach ($mapping in $pathMappings.GetEnumerator()) { "- $($mapping.Key) → $($mapping.Value)" })

## Files Processed
$($workflowFiles | ForEach-Object { "- $($_.FullName)" })

## Recommendations

1. **Test Workflows**: After updating Docker containers, test all N8N workflows
2. **Verify Paths**: Ensure all file operations use `/home/<USER>/shared/` prefix
3. **Update Documentation**: Update any documentation that references old paths
4. **Monitor Logs**: Check N8N logs for any path-related errors

## Container Path Structure
- `/home/<USER>/shared/` → Maps to `C:\Docker_Share\N8N\`
- `/home/<USER>/common/` → Maps to `C:\Docker_Share\Common\`

## Troubleshooting
If workflows still fail:
1. Check Docker volume mounts in docker-compose.yml
2. Verify centralized directories exist and have proper permissions
3. Restart N8N containers completely
4. Check N8N logs: docker-compose logs n8n
"@

$reportPath = "data\workflow_path_update_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
$reportContent | Out-File -FilePath $reportPath -Encoding UTF8
Write-Host "`n📊 Report saved: $reportPath" -ForegroundColor Green

if ($DryRun) {
    Write-Host "`n🔍 DRY RUN COMPLETE - No files were modified" -ForegroundColor Cyan
    Write-Host "Run without -DryRun to apply changes" -ForegroundColor Yellow
} else {
    Write-Host "`n🎉 Workflow Path Update Complete!" -ForegroundColor Green
}

Write-Host "`n📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Run the centralized Docker share setup script" -ForegroundColor White
Write-Host "2. Update Docker Compose configuration" -ForegroundColor White
Write-Host "3. Restart N8N containers" -ForegroundColor White
Write-Host "4. Test DNS security workflow" -ForegroundColor White
