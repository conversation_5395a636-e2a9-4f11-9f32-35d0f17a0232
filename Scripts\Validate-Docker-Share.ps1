﻿# Validate Docker Share Setup
Write-Host "Validating Docker Share Setup..." -ForegroundColor Cyan

# Check if centralized directories exist
$paths = @(
    "C:\Docker_Share",
    "C:\Docker_Share\N8N",
    "C:\Docker_Share\N8N\dns_reports",
    "C:\Docker_Share\Common"
)

foreach ($path in $paths) {
    if (Test-Path $path) {
        Write-Host "OK: $path" -ForegroundColor Green
    } else {
        Write-Host "MISSING: $path" -ForegroundColor Red
    }
}

# Test Docker compose configuration
Write-Host "
Testing Docker Compose Configuration..." -ForegroundColor Yellow
Set-Location "n8n-docker"
try {
    docker-compose config --quiet
    Write-Host "Docker Compose configuration is valid" -ForegroundColor Green
} catch {
    Write-Host "Docker Compose configuration error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "
ðŸ“‹ Next Steps:" -ForegroundColor Yellow
Write-Host "1. Run: docker-compose down" -ForegroundColor White
Write-Host "2. Run: docker-compose up -d" -ForegroundColor White
Write-Host "3. Test N8N workflow file operations" -ForegroundColor White
