#!/usr/bin/env python3
"""
Automated Database Reference Repair Tool

This script automatically fixes database table and column name issues found by
the validation script. It provides safe, reliable repairs with backup and
rollback capabilities.

AUTHOR: N8N_Builder Team  
DATE: 2025-08-18
PURPOSE: Automated repair of database schema reference errors
USAGE: python Scripts/auto_fix_database_references.py [--dry-run] [--backup]
"""

import re
import sys
import shutil
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime
import json

@dataclass
class RepairRule:
    """Defines a repair rule for database references."""
    pattern: str  # Regex pattern to find the issue
    replacement: str  # What to replace it with
    description: str  # Human-readable description
    confidence: str  # "high", "medium", "low"
    category: str  # "table_name", "column_name", "syntax"

@dataclass
class RepairResult:
    """Result of applying a repair to a file."""
    file_path: str
    line_number: int
    original_content: str
    fixed_content: str
    rule_applied: str
    success: bool
    error_message: Optional[str] = None

class DatabaseReferenceRepairer:
    """Automated repair tool for database reference issues."""
    
    def __init__(self):
        self.repair_rules = self._initialize_repair_rules()
        self.backup_dir = Path("data/repair_backups")
        self.results: List[RepairResult] = []
        
    def _initialize_repair_rules(self) -> List[RepairRule]:
        """Initialize the repair rules based on common issues."""
        rules = []
        
        # Table name fixes (high confidence - these are definite errors)
        table_fixes = {
            'REF_Entity': 'REF_Entity',
            'REF_Facts': 'REF_Fact',
            'REF_Opinions': 'REF_Opinion',
            'REF_Sources': 'REF_Source',
            'REF_Categories': 'REF_Category',
            'REF_Attribute': 'REF_Attribute',  # Assuming singular is correct
            'REF_EntityValue': 'REF_EntityValue',
            'REF_Evidence': 'REF_Evidence',  # This might be correct as-is
        }
        
        for incorrect, correct in table_fixes.items():
            # Pattern to match table references in various SQL contexts
            patterns = [
                # FROM clause
                (rf'\bFROM\s+{re.escape(incorrect)}\b', f'FROM {correct}'),
                # JOIN clause  
                (rf'\bJOIN\s+{re.escape(incorrect)}\b', f'JOIN {correct}'),
                # INSERT INTO
                (rf'\bINSERT\s+INTO\s+{re.escape(incorrect)}\b', f'INSERT INTO {correct}'),
                # UPDATE
                (rf'\bUPDATE\s+{re.escape(incorrect)}\b', f'UPDATE {correct}'),
                # DELETE FROM
                (rf'\bDELETE\s+FROM\s+{re.escape(incorrect)}\b', f'DELETE FROM {correct}'),
                # SELECT ... FROM (with potential columns)
                (rf'(\bSELECT\s+[^F]+\s+FROM\s+){re.escape(incorrect)}\b', rf'\1{correct}'),
                # Table name in quotes or brackets
                (rf'["\']?{re.escape(incorrect)}["\']?', f'{correct}'),
            ]
            
            for pattern, replacement in patterns:
                confidence = "high" if incorrect in [REF_Entity] else "medium"
                rules.append(RepairRule(
                    pattern=pattern,
                    replacement=replacement,
                    description=f"Fix table name: {incorrect} → {correct}",
                    confidence=confidence,
                    category="table_name"
                ))
        
        # Cross-reference table naming consistency
        xrf_fixes = {
            'XRF_EntityAttributeValue': 'XRF_EntityAttributeValue',
            'XRF_EntityAttributeValue': 'XRF_EntityAttributeValue',  # Remove underscores
        }
        
        for incorrect, correct in xrf_fixes.items():
            rules.append(RepairRule(
                pattern=rf'\b{re.escape(incorrect)}\b',
                replacement=correct,
                description=f"Standardize XRF table name: {incorrect} → {correct}",
                confidence="medium",
                category="table_name"
            ))
        
        # Common SQL syntax fixes
        syntax_fixes = [
            # Fix common SQL formatting issues
            RepairRule(
                pattern=r'\bSELECT\s+\*\s+FROM\s+([A-Za-z_][A-Za-z0-9_]*)\s+ORDER\s+BY',
                replacement=r'SELECT * FROM \1 ORDER BY',
                description="Fix SELECT * FROM spacing",
                confidence="high",
                category="syntax"
            ),
            # Fix table alias spacing
            RepairRule(
                pattern=r'\bFROM\s+([A-Za-z_][A-Za-z0-9_]*)\s+([a-z])\b',
                replacement=r'FROM \1 \2',
                description="Fix table alias spacing",
                confidence="medium", 
                category="syntax"
            ),
        ]
        
        rules.extend(syntax_fixes)
        return rules
    
    def create_backup(self, file_path: Path) -> Path:
        """Create a backup of the file before modification."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{file_path.name}.backup_{timestamp}"
        backup_path = self.backup_dir / file_path.parent.name / backup_name
        
        # Ensure backup directory exists
        backup_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Copy the file
        shutil.copy2(file_path, backup_path)
        return backup_path
    
    def apply_repairs_to_file(self, file_path: Path, dry_run: bool = False) -> List[RepairResult]:
        """Apply all applicable repairs to a single file."""
        results = []
        
        try:
            # Read the file
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            modified_lines = lines.copy()
            file_modified = False
            
            # Apply repairs line by line
            for line_num, original_line in enumerate(lines):
                current_line = original_line
                
                for rule in self.repair_rules:
                    # Skip low confidence rules unless explicitly enabled
                    if rule.confidence == "low":
                        continue
                        
                    # Apply the repair rule
                    if re.search(rule.pattern, current_line, re.IGNORECASE):
                        fixed_line = re.sub(rule.pattern, rule.replacement, current_line, flags=re.IGNORECASE)
                        
                        if fixed_line != current_line:
                            result = RepairResult(
                                file_path=str(file_path),
                                line_number=line_num + 1,
                                original_content=current_line.strip(),
                                fixed_content=fixed_line.strip(),
                                rule_applied=rule.description,
                                success=True
                            )
                            results.append(result)
                            
                            current_line = fixed_line
                            file_modified = True
                
                modified_lines[line_num] = current_line
            
            # Write the modified file if not dry run
            if file_modified and not dry_run:
                # Create backup first
                backup_path = self.create_backup(file_path)
                print(f"   📁 Backup created: {backup_path}")
                
                # Write the modified content
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.writelines(modified_lines)
                    
        except Exception as e:
            error_result = RepairResult(
                file_path=str(file_path),
                line_number=0,
                original_content="",
                fixed_content="",
                rule_applied="File processing error",
                success=False,
                error_message=str(e)
            )
            results.append(error_result)
            
        return results
    
    def repair_codebase(self, target_directories: List[str] = None, dry_run: bool = False, 
                       file_extensions: Set[str] = None) -> Dict:
        """Repair database references across the codebase."""
        if target_directories is None:
            target_directories = ['n8n_builder', 'Self_Healer', 'Scripts', 'tests']
            
        if file_extensions is None:
            file_extensions = {'.py', '.sql', '.ps1'}
            
        print(f"🔧 Database Reference Repair Tool")
        print(f"{'=' * 50}")
        print(f"Mode: {'DRY RUN' if dry_run else 'LIVE REPAIR'}")
        print(f"Target directories: {', '.join(target_directories)}")
        print(f"File extensions: {', '.join(file_extensions)}")
        print(f"Repair rules loaded: {len(self.repair_rules)}")
        
        project_root = Path(__file__).parent.parent
        all_results = []
        files_processed = 0
        files_modified = 0
        
        for directory in target_directories:
            dir_path = project_root / directory
            if not dir_path.exists():
                continue
                
            print(f"\n📁 Processing directory: {directory}")
            
            for file_path in dir_path.rglob('*'):
                if file_path.is_file() and file_path.suffix in file_extensions:
                    files_processed += 1
                    
                    file_results = self.apply_repairs_to_file(file_path, dry_run)
                    
                    if file_results and any(r.success for r in file_results):
                        files_modified += 1
                        successful_repairs = [r for r in file_results if r.success]
                        
                        print(f"   📄 {file_path.relative_to(project_root)}: {len(successful_repairs)} repairs")
                        
                        for result in successful_repairs:
                            print(f"      Line {result.line_number}: {result.rule_applied}")
                            if not dry_run:
                                print(f"         Before: {result.original_content}")
                                print(f"         After:  {result.fixed_content}")
                    
                    all_results.extend(file_results)
        
        # Generate summary
        successful_repairs = [r for r in all_results if r.success]
        failed_repairs = [r for r in all_results if not r.success]
        
        summary = {
            'files_processed': files_processed,
            'files_modified': files_modified,
            'total_repairs': len(successful_repairs),
            'failed_repairs': len(failed_repairs),
            'dry_run': dry_run,
            'repairs_by_category': {},
            'repairs_by_confidence': {}
        }
        
        # Categorize repairs
        for result in successful_repairs:
            # Find which rule was applied
            for rule in self.repair_rules:
                if rule.description == result.rule_applied:
                    summary['repairs_by_category'][rule.category] = summary['repairs_by_category'].get(rule.category, 0) + 1
                    summary['repairs_by_confidence'][rule.confidence] = summary['repairs_by_confidence'].get(rule.confidence, 0) + 1
                    break
        
        self.results = all_results
        return summary
    
    def generate_repair_report(self, summary: Dict, output_file: str = None) -> str:
        """Generate a detailed repair report."""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"data/database_repair_report_{timestamp}.html"
            
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        successful_repairs = [r for r in self.results if r.success]
        failed_repairs = [r for r in self.results if not r.success]
        
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Database Reference Repair Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f8ff; padding: 20px; border-radius: 5px; }}
        .summary {{ display: flex; gap: 20px; margin: 20px 0; }}
        .metric {{ background-color: #e8f4f8; padding: 15px; border-radius: 5px; text-align: center; }}
        .metric.success {{ background-color: #e8f8e8; }}
        .metric.warning {{ background-color: #fff8e8; }}
        .metric.error {{ background-color: #ffe8e8; }}
        .repair {{ background-color: #f9f9f9; padding: 10px; margin: 5px 0; border-left: 4px solid #4CAF50; }}
        .repair.failed {{ border-left-color: #f44336; }}
        .file-path {{ font-weight: bold; color: #0066cc; }}
        .line-number {{ color: #666; font-size: 0.9em; }}
        .code {{ font-family: monospace; background-color: #f0f0f0; padding: 5px; margin: 5px 0; }}
        .before {{ color: #d32f2f; }}
        .after {{ color: #388e3c; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Database Reference Repair Report</h1>
        <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>Mode: {'DRY RUN' if summary['dry_run'] else 'LIVE REPAIR'}</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>{summary['files_processed']}</h3>
            <p>Files Processed</p>
        </div>
        <div class="metric success">
            <h3>{summary['files_modified']}</h3>
            <p>Files Modified</p>
        </div>
        <div class="metric success">
            <h3>{summary['total_repairs']}</h3>
            <p>Successful Repairs</p>
        </div>
        <div class="metric {'error' if summary['failed_repairs'] > 0 else 'success'}">
            <h3>{summary['failed_repairs']}</h3>
            <p>Failed Repairs</p>
        </div>
    </div>
    
    <h2>Repair Categories</h2>
    <ul>
"""
        
        for category, count in summary['repairs_by_category'].items():
            html_content += f"<li><strong>{category}</strong>: {count} repairs</li>"
            
        html_content += """
    </ul>
    
    <h2>✅ Successful Repairs</h2>
"""
        
        for result in successful_repairs:
            html_content += f"""
    <div class="repair">
        <div class="file-path">{result.file_path}</div>
        <div class="line-number">Line {result.line_number}</div>
        <div><strong>Rule:</strong> {result.rule_applied}</div>
        <div class="code before">Before: {result.original_content}</div>
        <div class="code after">After:  {result.fixed_content}</div>
    </div>
"""
        
        if failed_repairs:
            html_content += "<h2>❌ Failed Repairs</h2>"
            for result in failed_repairs:
                html_content += f"""
    <div class="repair failed">
        <div class="file-path">{result.file_path}</div>
        <div><strong>Error:</strong> {result.error_message}</div>
    </div>
"""
        
        html_content += """
</body>
</html>
"""
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
            
        return str(output_path)

def main():
    """Main execution function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Automatically repair database reference issues')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be changed without making changes')
    parser.add_argument('--backup', action='store_true', default=True,
                       help='Create backups before modifying files (default: True)')
    parser.add_argument('--directories', nargs='+', 
                       default=['n8n_builder', 'Self_Healer', 'Scripts', 'tests'],
                       help='Directories to process')
    parser.add_argument('--extensions', nargs='+',
                       default=['.py', '.sql', '.ps1'],
                       help='File extensions to process')
    parser.add_argument('--output',
                       help='Output file for repair report')
    
    args = parser.parse_args()
    
    repairer = DatabaseReferenceRepairer()
    
    # Run the repairs
    summary = repairer.repair_codebase(
        target_directories=args.directories,
        dry_run=args.dry_run,
        file_extensions=set(args.extensions)
    )
    
    # Generate report
    report_path = repairer.generate_repair_report(summary, args.output)
    
    # Print final summary
    print(f"\n📊 REPAIR SUMMARY:")
    print(f"   Files processed: {summary['files_processed']}")
    print(f"   Files modified: {summary['files_modified']}")
    print(f"   Total repairs: {summary['total_repairs']}")
    print(f"   Failed repairs: {summary['failed_repairs']}")
    print(f"\n📄 Detailed report: {report_path}")
    
    if args.dry_run:
        print(f"\n🔍 DRY RUN COMPLETE - No files were modified")
        print(f"   Run without --dry-run to apply the repairs")
    else:
        print(f"\n✅ REPAIRS APPLIED")
        if summary['total_repairs'] > 0:
            print(f"   Backups created in: data/repair_backups/")
    
    return 0 if summary['failed_repairs'] == 0 else 1

if __name__ == "__main__":
    sys.exit(main())
