#!/usr/bin/env python3
"""
Batch Table Name Fix Script

This script processes multiple table name mappings from a configuration file
and applies them all to the codebase using the repair summary CSV.

Usage:
    python batch_fix_table_names.py <csv_file> <mapping_file>
    
Mapping file format (JSON):
{
    "XRF_EntityAttributeValue": "XRF_Entity_AttributeValue",
    "REF_OldTable": "REF_New_Table",
    "XRF_AnotherTable": "XRF_Another_Table"
}

Author: N8N_Builder SQL Validation System
Date: 2025-08-23
"""

import sys
import json
import os
from pathlib import Path
import argparse
from datetime import datetime
from fix_table_names import TableNameFixer

class BatchTableNameFixer:
    def __init__(self, csv_file: str, mapping_file: str):
        self.csv_file = csv_file
        self.mapping_file = mapping_file
        self.mappings = {}
        self.results = []
        
    def load_mappings(self) -> bool:
        """Load table name mappings from JSON file."""
        try:
            with open(self.mapping_file, 'r', encoding='utf-8') as f:
                self.mappings = json.load(f)
            
            print(f"📊 Loaded {len(self.mappings)} table name mappings:")
            for old_name, new_name in self.mappings.items():
                print(f"   {old_name} → {new_name}")
            
            return True
            
        except FileNotFoundError:
            print(f"❌ Error: Mapping file '{self.mapping_file}' not found")
            return False
        except json.JSONDecodeError as e:
            print(f"❌ Error: Invalid JSON in mapping file: {e}")
            return False
        except Exception as e:
            print(f"❌ Error loading mappings: {e}")
            return False
    
    def process_all_mappings(self) -> None:
        """Process all table name mappings."""
        print(f"\n🔧 Processing {len(self.mappings)} table name mappings")
        print("=" * 80)
        
        for i, (old_name, new_name) in enumerate(self.mappings.items(), 1):
            print(f"\n[{i}/{len(self.mappings)}] Processing: {old_name} → {new_name}")
            print("-" * 60)
            
            # Create a fixer for this mapping
            fixer = TableNameFixer(self.csv_file, old_name, new_name)
            
            # Load and process entries
            entries = fixer.load_repair_summary()
            if entries:
                fixer.process_entries(entries)
            
            # Store results
            self.results.append({
                'old_name': old_name,
                'new_name': new_name,
                'fixes_applied': len(fixer.fixes_applied),
                'errors': len(fixer.errors),
                'fixer': fixer
            })
    
    def generate_batch_report(self) -> None:
        """Generate a comprehensive batch report."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"data/database_repair_logs/batch_table_fixes_{timestamp}.txt"
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        total_fixes = sum(r['fixes_applied'] for r in self.results)
        total_errors = sum(r['errors'] for r in self.results)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"Batch Table Name Fix Report\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Source CSV: {self.csv_file}\n")
            f.write(f"Mapping File: {self.mapping_file}\n")
            f.write("=" * 80 + "\n\n")
            
            f.write(f"OVERALL SUMMARY:\n")
            f.write(f"  Mappings Processed: {len(self.results)}\n")
            f.write(f"  Total Fixes Applied: {total_fixes}\n")
            f.write(f"  Total Errors: {total_errors}\n\n")
            
            f.write("MAPPING RESULTS:\n")
            f.write("-" * 80 + "\n")
            for result in self.results:
                f.write(f"Mapping: {result['old_name']} → {result['new_name']}\n")
                f.write(f"  Fixes: {result['fixes_applied']}\n")
                f.write(f"  Errors: {result['errors']}\n")
                
                if result['fixes_applied'] > 0:
                    f.write(f"  Files Modified:\n")
                    for fix in result['fixer'].fixes_applied:
                        f.write(f"    {fix['file']}:{fix['line']}\n")
                
                if result['errors'] > 0:
                    f.write(f"  Errors:\n")
                    for error in result['fixer'].errors:
                        f.write(f"    {error}\n")
                
                f.write("\n")
        
        print(f"\n📄 Batch report saved to: {report_file}")
    
    def run(self) -> bool:
        """Main execution method."""
        print(f"🔧 Batch Table Name Fixer")
        print(f"📁 CSV File: {self.csv_file}")
        print(f"🗂️  Mapping File: {self.mapping_file}")
        print("=" * 80)
        
        # Load mappings
        if not self.load_mappings():
            return False
        
        # Process all mappings
        self.process_all_mappings()
        
        # Generate batch report
        self.generate_batch_report()
        
        # Print summary
        total_fixes = sum(r['fixes_applied'] for r in self.results)
        total_errors = sum(r['errors'] for r in self.results)
        
        print("\n" + "=" * 80)
        print(f"🎯 BATCH SUMMARY:")
        print(f"   📊 Mappings Processed: {len(self.results)}")
        print(f"   ✅ Total Fixes Applied: {total_fixes}")
        print(f"   ❌ Total Errors: {total_errors}")
        
        # Show per-mapping results
        print(f"\n📋 PER-MAPPING RESULTS:")
        for result in self.results:
            status = "✅" if result['fixes_applied'] > 0 else "⚠️" if result['errors'] == 0 else "❌"
            print(f"   {status} {result['old_name']} → {result['new_name']}: "
                  f"{result['fixes_applied']} fixes, {result['errors']} errors")
        
        return total_fixes > 0

def create_sample_mapping_file(filename: str) -> None:
    """Create a sample mapping file for reference."""
    sample_mappings = {
        "XRF_EntityAttributeValue": "XRF_Entity_AttributeValue",
        "REF_OldTable": "REF_New_Table",
        "XRF_AnotherTable": "XRF_Another_Table"
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(sample_mappings, f, indent=2)
    
    print(f"📝 Sample mapping file created: {filename}")

def main():
    parser = argparse.ArgumentParser(
        description="Batch fix table name references using mapping file",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python batch_fix_table_names.py repair_summary.csv table_mappings.json
  python batch_fix_table_names.py --create-sample mappings.json
        """
    )
    
    parser.add_argument('csv_file', nargs='?', help='Path to the repair summary CSV file')
    parser.add_argument('mapping_file', nargs='?', help='Path to the JSON mapping file')
    parser.add_argument('--create-sample', metavar='FILE', help='Create a sample mapping file')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be changed without making changes')
    
    args = parser.parse_args()
    
    # Handle sample file creation
    if args.create_sample:
        create_sample_mapping_file(args.create_sample)
        return 0
    
    # Validate required arguments
    if not args.csv_file or not args.mapping_file:
        parser.print_help()
        return 1
    
    # Validate files exist
    if not os.path.exists(args.csv_file):
        print(f"❌ Error: CSV file '{args.csv_file}' not found")
        return 1
    
    if not os.path.exists(args.mapping_file):
        print(f"❌ Error: Mapping file '{args.mapping_file}' not found")
        return 1
    
    # Create and run the batch fixer
    batch_fixer = BatchTableNameFixer(args.csv_file, args.mapping_file)
    
    if args.dry_run:
        print("🔍 DRY RUN MODE - No changes will be made")
        # TODO: Implement dry run functionality
        return 0
    
    success = batch_fixer.run()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
