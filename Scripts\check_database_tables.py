#!/usr/bin/env python3
"""
Check KnowledgeBase database table names to confirm REF_Entity vs REF_Entity
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from n8n_builder.mcp_database_tool import MCPDatabaseTool

async def check_database_tables():
    """Check the actual table names in the KnowledgeBase database."""
    try:
        print("🔍 Checking KnowledgeBase Database Table Names...")
        print("=" * 60)
        
        # Initialize database connection
        db_tool = MCPDatabaseTool('knowledgebase')
        
        # Test connection first
        print("1. Testing database connection...")
        conn_result = await db_tool.test_connection()
        
        if not conn_result['connected']:
            print(f"❌ Connection failed: {conn_result['error']}")
            return False
            
        print(f"✅ Connected to database: {conn_result['database_name']}")
        print(f"   Server: {conn_result['server_version'][:50]}...")
        
        # Check for all REF_ tables
        print("\n2. Checking REF_ table names...")
        ref_tables_query = """
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
        """
        
        result = await db_tool.execute_query(ref_tables_query)
        
        if result.get('rows'):
            print(f"   Found {len(result['rows'])} REF_ tables:")
            
            entity_tables = []
            for row in result['rows']:
                table_name = row['TABLE_NAME']
                print(f"   - {table_name}")
                
                # Check specifically for Entity-related tables
                if 'entit' in table_name.lower():
                    entity_tables.append(table_name)
                    print(f"     ⭐ ENTITY-RELATED TABLE: {table_name}")
            
            # Specific check for the problematic table
            print(f"\n3. Entity table analysis:")
            if entity_tables:
                for table in entity_tables:
                    print(f"   ✅ Found entity table: {table}")
                    
                    # Check if this is the table being referenced incorrectly
                    if table == 'REF_Entity':
                        print(f"   🎯 CORRECT TABLE NAME: REF_Entity (singular)")
                    elif table == REF_Entity:
                        print(f"   ⚠️  OLD TABLE NAME: REF_Entity (plural)")
            else:
                print("   ❌ No entity tables found")
                
            # Check for the specific error we saw
            print(f"\n4. Testing problematic query...")
            try:
                test_query = "SELECT TOP 1 * FROM REF_Entity"
                test_result = await db_tool.execute_query(test_query)
                print("   ❌ REF_Entity table exists (unexpected)")
            except Exception as e:
                if "Invalid object name REF_Entity" in str(e):
                    print("   ✅ REF_Entity does NOT exist (as expected)")
                else:
                    print(f"   ⚠️  Unexpected error: {e}")
            
            try:
                test_query = "SELECT TOP 1 * FROM REF_Entity"
                test_result = await db_tool.execute_query(test_query)
                print("   ✅ REF_Entity table exists (correct)")
            except Exception as e:
                print(f"   ❌ REF_Entity error: {e}")
                
        else:
            print("   ❌ No REF_ tables found")
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking database tables: {e}")
        return False

async def main():
    """Main execution function."""
    success = await check_database_tables()
    
    if success:
        print(f"\n🎯 CONCLUSION:")
        print(f"   The table name should be 'REF_Entity' (singular)")
        print(f"   Scripts referencing REF_Entity need to be updated")
    else:
        print(f"\n❌ Could not complete table name verification")

if __name__ == "__main__":
    asyncio.run(main())
