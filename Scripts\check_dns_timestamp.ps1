# Check DNS Cache File Timestamp
$unixTime = 1756505533
$dateTime = (Get-Date '1970-01-01').AddSeconds($unixTime)
$currentTime = Get-Date
$ageMinutes = [math]::Floor(($currentTime - $dateTime).TotalMinutes)

Write-Host "DNS Cache File Timestamp Analysis" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host "Unix timestamp: $unixTime" -ForegroundColor White
Write-Host "File timestamp: $dateTime" -ForegroundColor White
Write-Host "Current time:   $currentTime" -ForegroundColor White
Write-Host "File age:       $ageMinutes minutes" -ForegroundColor White

if ($ageMinutes -le 65) {
    Write-Host "✅ File is FRESH (≤65 minutes)" -ForegroundColor Green
    Write-Host "   The N8N workflow should accept this file" -ForegroundColor Green
} else {
    Write-Host "❌ File is STALE (>65 minutes)" -ForegroundColor Red
    Write-Host "   The N8N workflow will reject this file" -ForegroundColor Red
}

# Also check current Unix time for comparison
$currentUnixTime = [int64]((Get-Date).ToUniversalTime() - (Get-Date '1970-01-01')).TotalSeconds
Write-Host "`nCurrent Unix time: $currentUnixTime" -ForegroundColor Gray
Write-Host "Time difference: $($currentUnixTime - $unixTime) seconds" -ForegroundColor Gray
