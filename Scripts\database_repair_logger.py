#!/usr/bin/env python3
"""
Database Repair Logger

Provides comprehensive logging for database schema validation and repair operations.
Creates detailed audit trails with timestamps and validation results.

AUTHOR: N8N_Builder Team
DATE: 2025-08-18
PURPOSE: Audit logging for database schema maintenance operations
"""

import json
import csv
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

@dataclass
class RepairLogEntry:
    """Single repair operation log entry."""
    timestamp: str
    operation_type: str  # "validation", "repair", "sql_test"
    file_path: str
    line_number: int
    original_content: str
    modified_content: str
    rule_applied: str
    status: str  # "success", "failed", "warning"
    error_message: Optional[str] = None
    sql_validation_result: Optional[str] = None

class DatabaseRepairLogger:
    """Comprehensive logging system for database repair operations."""
    
    def __init__(self, log_dir: str = "data/database_repair_logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate session ID for this run
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Log file paths
        self.detailed_log = self.log_dir / f"repair_session_{self.session_id}.json"
        self.summary_log = self.log_dir / f"repair_summary_{self.session_id}.csv"
        self.validation_log = self.log_dir / f"sql_validation_{self.session_id}.txt"
        
        # In-memory storage
        self.entries: List[RepairLogEntry] = []
        
        # Initialize log files
        self._initialize_logs()
    
    def _initialize_logs(self):
        """Initialize log files with headers."""
        # Create detailed JSON log
        initial_data = {
            "session_id": self.session_id,
            "start_time": datetime.now().isoformat(),
            "entries": []
        }
        
        with open(self.detailed_log, 'w', encoding='utf-8') as f:
            json.dump(initial_data, f, indent=2)
        
        # Create CSV summary log
        with open(self.summary_log, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([
                'Timestamp', 'Operation', 'File', 'Line', 'Status', 
                'Rule Applied', 'SQL Validation', 'Error'
            ])
        
        # Create validation log
        with open(self.validation_log, 'w', encoding='utf-8') as f:
            f.write(f"SQL Validation Log - Session {self.session_id}\n")
            f.write(f"Started: {datetime.now().isoformat()}\n")
            f.write("=" * 80 + "\n\n")
    
    def log_repair(self, file_path: str, line_number: int, original: str, 
                   modified: str, rule: str, status: str = "success", 
                   error: str = None, sql_result: str = None):
        """Log a repair operation."""
        entry = RepairLogEntry(
            timestamp=datetime.now().isoformat(),
            operation_type="repair",
            file_path=file_path,
            line_number=line_number,
            original_content=original.strip(),
            modified_content=modified.strip(),
            rule_applied=rule,
            status=status,
            error_message=error,
            sql_validation_result=sql_result
        )
        
        self._add_entry(entry)
    
    def log_validation(self, file_path: str, line_number: int, content: str,
                      issue_type: str, status: str = "warning", 
                      error: str = None):
        """Log a validation finding."""
        entry = RepairLogEntry(
            timestamp=datetime.now().isoformat(),
            operation_type="validation",
            file_path=file_path,
            line_number=line_number,
            original_content=content.strip(),
            modified_content="",
            rule_applied=issue_type,
            status=status,
            error_message=error
        )
        
        self._add_entry(entry)
    
    def log_sql_test(self, file_path: str, line_number: int, original_sql: str,
                     test_sql: str, result: str, status: str = "success"):
        """Log SQL validation test result."""
        entry = RepairLogEntry(
            timestamp=datetime.now().isoformat(),
            operation_type="sql_test",
            file_path=file_path,
            line_number=line_number,
            original_content=original_sql.strip(),
            modified_content=test_sql.strip(),
            rule_applied="SQL Validation Test",
            status=status,
            sql_validation_result=result
        )
        
        self._add_entry(entry)
        
        # Also write to validation log file
        with open(self.validation_log, 'a', encoding='utf-8') as f:
            f.write(f"[{entry.timestamp}] {file_path}:{line_number}\n")
            f.write(f"Status: {status.upper()}\n")
            f.write(f"Original SQL: {original_sql.strip()}\n")
            f.write(f"Test SQL: {test_sql.strip()}\n")
            f.write(f"Result: {result}\n")
            f.write("-" * 40 + "\n\n")
    
    def _add_entry(self, entry: RepairLogEntry):
        """Add entry to in-memory storage and update log files."""
        self.entries.append(entry)
        
        # Update detailed JSON log
        with open(self.detailed_log, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        data['entries'].append(asdict(entry))
        data['last_updated'] = datetime.now().isoformat()
        
        with open(self.detailed_log, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)
        
        # Update CSV summary
        with open(self.summary_log, 'a', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([
                entry.timestamp,
                entry.operation_type,
                entry.file_path,
                entry.line_number,
                entry.status,
                entry.rule_applied,
                entry.sql_validation_result or "",
                entry.error_message or ""
            ])
    
    def generate_summary_report(self) -> Dict[str, Any]:
        """Generate summary statistics."""
        total_entries = len(self.entries)
        
        # Count by operation type
        operations = {}
        statuses = {}
        
        for entry in self.entries:
            operations[entry.operation_type] = operations.get(entry.operation_type, 0) + 1
            statuses[entry.status] = statuses.get(entry.status, 0) + 1
        
        # SQL validation results
        sql_tests = [e for e in self.entries if e.operation_type == "sql_test"]
        sql_passed = len([e for e in sql_tests if e.status == "success"])
        sql_failed = len([e for e in sql_tests if e.status == "failed"])
        
        summary = {
            "session_id": self.session_id,
            "total_entries": total_entries,
            "operations": operations,
            "statuses": statuses,
            "sql_validation": {
                "total_tests": len(sql_tests),
                "passed": sql_passed,
                "failed": sql_failed,
                "success_rate": (sql_passed / len(sql_tests) * 100) if sql_tests else 0
            },
            "log_files": {
                "detailed": str(self.detailed_log),
                "summary": str(self.summary_log),
                "validation": str(self.validation_log)
            }
        }
        
        return summary
    
    def print_summary(self):
        """Print a formatted summary to console."""
        summary = self.generate_summary_report()
        
        print(f"\n📊 DATABASE REPAIR SESSION SUMMARY")
        print(f"{'=' * 50}")
        print(f"Session ID: {summary['session_id']}")
        print(f"Total Operations: {summary['total_entries']}")
        
        print(f"\n📋 Operations Breakdown:")
        for op_type, count in summary['operations'].items():
            print(f"   {op_type.title()}: {count}")
        
        print(f"\n📈 Status Breakdown:")
        for status, count in summary['statuses'].items():
            print(f"   {status.title()}: {count}")
        
        if summary['sql_validation']['total_tests'] > 0:
            sql = summary['sql_validation']
            print(f"\n🔍 SQL Validation Results:")
            print(f"   Total Tests: {sql['total_tests']}")
            print(f"   Passed: {sql['passed']}")
            print(f"   Failed: {sql['failed']}")
            print(f"   Success Rate: {sql['success_rate']:.1f}%")
        
        print(f"\n📄 Log Files Created:")
        for log_type, path in summary['log_files'].items():
            print(f"   {log_type.title()}: {path}")
    
    def close_session(self):
        """Close the logging session and finalize logs."""
        # Update detailed log with session end time
        with open(self.detailed_log, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        data['end_time'] = datetime.now().isoformat()
        data['summary'] = self.generate_summary_report()
        
        with open(self.detailed_log, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)
        
        # Close validation log
        with open(self.validation_log, 'a', encoding='utf-8') as f:
            f.write(f"\nSession ended: {datetime.now().isoformat()}\n")
            f.write("=" * 80 + "\n")

if __name__ == "__main__":
    # Test the logger
    logger = DatabaseRepairLogger()
    
    # Test logging different types of entries
    logger.log_repair(
        "test_file.py", 42, 
        "FROM REF_Entities", "FROM REF_Entity",
        "Fix table name: REF_Entities → REF_Entity"
    )
    
    logger.log_sql_test(
        "test_file.py", 42,
        "SELECT * FROM REF_Entity WHERE Name = 'test'",
        "SELECT 1 FROM REF_Entity WHERE Name = 'test'",
        "Query executed successfully - table and columns exist"
    )
    
    logger.print_summary()
    logger.close_session()
    
    print(f"\n✅ Test logging completed successfully!")
