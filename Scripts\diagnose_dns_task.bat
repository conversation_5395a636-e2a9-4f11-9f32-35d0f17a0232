@echo off
echo DNS Task Scheduler Quick Diagnostic
echo ===================================

echo.
echo 1. Checking if task exists...
schtasks /query /tn "N8N_DNS_Cache_Collector" >nul 2>&1
if %errorlevel%==0 (
    echo ✓ Task exists
    echo.
    echo Task Details:
    schtasks /query /tn "N8N_DNS_Cache_Collector" /fo list /v
) else (
    echo ✗ Task not found
    goto :end
)

echo.
echo 2. Checking DNS cache file...
if exist "data\dns_reports\setup\dns_cache_output.txt" (
    echo ✓ DNS cache file exists
    dir "data\dns_reports\setup\dns_cache_output.txt"
) else (
    echo ✗ DNS cache file missing
)

echo.
echo 3. Checking script file...
if exist "data\dns_reports\setup\get_dns_cache.ps1" (
    echo ✓ Script file exists
    dir "data\dns_reports\setup\get_dns_cache.ps1"
) else (
    echo ✗ Script file missing
)

echo.
echo 4. Recent Task Scheduler events (if available)...
wevtutil qe Microsoft-Windows-TaskScheduler/Operational /c:10 /rd:true /f:text /q:"*[System[TimeCreated[timediff(@SystemTime) <= 604800000]] and EventData[Data='N8N_DNS_Cache_Collector']]" 2>nul

:end
echo.
echo To enable detailed task history, run:
echo   wevtutil set-log Microsoft-Windows-TaskScheduler/Operational /enabled:true
echo.
echo To manually test the task:
echo   schtasks /run /tn "N8N_DNS_Cache_Collector"
pause
