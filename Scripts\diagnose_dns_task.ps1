# DNS Task Scheduler Diagnostic Script
# Investigates why the N8N_DNS_Cache_Collector task stopped working

param(
    [switch]$Detailed,
    [switch]$EnableHistory,
    [switch]$TestRun
)

$TaskName = "N8N_DNS_Cache_Collector"

Write-Host "DNS Task Scheduler Diagnostic Tool" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan

if ($EnableHistory) {
    Write-Host "`nEnabling Task Scheduler History..." -ForegroundColor Yellow
    try {
        # Enable Task Scheduler history
        wevtutil set-log Microsoft-Windows-TaskScheduler/Operational /enabled:true
        Write-Host "✅ Task Scheduler history enabled" -ForegroundColor Green
        Write-Host "   You can now view detailed task execution logs in Event Viewer" -ForegroundColor White
        Write-Host "   Path: Event Viewer > Applications and Services Logs > Microsoft > Windows > TaskScheduler > Operational" -ForegroundColor White
    } catch {
        Write-Host "❌ Failed to enable Task Scheduler history: $($_.Exception.Message)" -ForegroundColor Red
    }
    return
}

# Check if task exists
Write-Host "`n1. Checking Task Existence..." -ForegroundColor Yellow
try {
    $task = Get-ScheduledTask -TaskName $TaskName -ErrorAction Stop
    Write-Host "✅ Task found: $TaskName" -ForegroundColor Green
    Write-Host "   State: $($task.State)" -ForegroundColor White
    Write-Host "   Author: $($task.Author)" -ForegroundColor White
    Write-Host "   Description: $($task.Description)" -ForegroundColor White
} catch {
    Write-Host "❌ Task not found: $TaskName" -ForegroundColor Red
    Write-Host "   Run setup_dns_automation.ps1 to recreate the task" -ForegroundColor Yellow
    exit 1
}

# Get detailed task information
Write-Host "`n2. Task Configuration Details..." -ForegroundColor Yellow
$taskInfo = Get-ScheduledTaskInfo -TaskName $TaskName
$taskAction = (Get-ScheduledTask -TaskName $TaskName).Actions[0]
$taskTrigger = (Get-ScheduledTask -TaskName $TaskName).Triggers[0]
$taskPrincipal = (Get-ScheduledTask -TaskName $TaskName).Principal

Write-Host "   Last Run Time: $($taskInfo.LastRunTime)" -ForegroundColor White
Write-Host "   Next Run Time: $($taskInfo.NextRunTime)" -ForegroundColor White
Write-Host "   Last Task Result: $($taskInfo.LastTaskResult)" -ForegroundColor White
Write-Host "   Number of Missed Runs: $($taskInfo.NumberOfMissedRuns)" -ForegroundColor White

# Decode the last task result
$resultMessage = switch ($taskInfo.LastTaskResult) {
    0 { "Success (0x0)" }
    1 { "Incorrect function (0x1)" }
    2 { "File not found (0x2)" }
    267009 { "Task is currently running (0x41301)" }
    267010 { "Task is ready to run (0x41302)" }
    267011 { "Task is disabled (0x41303)" }
    267012 { "Task has not yet run (0x41304)" }
    267013 { "There are no more runs scheduled (0x41305)" }
    267014 { "Properties are not available (0x41306)" }
    267015 { "The last run was terminated by the user (0x41307)" }
    267016 { "Either the task has no triggers or existing triggers are disabled (0x41308)" }
    -2147024894 { "File not found (0x80070002)" }
    -2147024891 { "Path not found (0x80070003)" }
    -2147024809 { "Insufficient system resources (0x800700C1)" }
    default { "Unknown result code: $($taskInfo.LastTaskResult)" }
}
Write-Host "   Result Meaning: $resultMessage" -ForegroundColor White

# Check action details
Write-Host "`n3. Task Action Configuration..." -ForegroundColor Yellow
Write-Host "   Execute: $($taskAction.Execute)" -ForegroundColor White
Write-Host "   Arguments: $($taskAction.Arguments)" -ForegroundColor White
Write-Host "   Working Directory: $($taskAction.WorkingDirectory)" -ForegroundColor White

# Check trigger details
Write-Host "`n4. Task Trigger Configuration..." -ForegroundColor Yellow
Write-Host "   Trigger Type: $($taskTrigger.CimClass.CimClassName)" -ForegroundColor White
Write-Host "   Start Boundary: $($taskTrigger.StartBoundary)" -ForegroundColor White
Write-Host "   Enabled: $($taskTrigger.Enabled)" -ForegroundColor White
if ($taskTrigger.Repetition) {
    Write-Host "   Repetition Interval: $($taskTrigger.Repetition.Interval)" -ForegroundColor White
    Write-Host "   Repetition Duration: $($taskTrigger.Repetition.Duration)" -ForegroundColor White
}

# Check principal (security context)
Write-Host "`n5. Task Security Context..." -ForegroundColor Yellow
Write-Host "   User ID: $($taskPrincipal.UserId)" -ForegroundColor White
Write-Host "   Logon Type: $($taskPrincipal.LogonType)" -ForegroundColor White
Write-Host "   Run Level: $($taskPrincipal.RunLevel)" -ForegroundColor White

# Check file paths and permissions
Write-Host "`n6. File System Checks..." -ForegroundColor Yellow
$scriptPath = Join-Path (Split-Path $PSScriptRoot -Parent) "data\dns_reports\setup\get_dns_cache.ps1"
$outputPath = Join-Path (Split-Path $PSScriptRoot -Parent) "data\dns_reports\setup\dns_cache_output.txt"

Write-Host "   Script Path: $scriptPath" -ForegroundColor White
if (Test-Path $scriptPath) {
    Write-Host "   ✅ Script file exists" -ForegroundColor Green
    $scriptFile = Get-Item $scriptPath
    Write-Host "      Size: $($scriptFile.Length) bytes" -ForegroundColor White
    Write-Host "      Modified: $($scriptFile.LastWriteTime)" -ForegroundColor White
} else {
    Write-Host "   ❌ Script file missing!" -ForegroundColor Red
}

Write-Host "   Output Path: $outputPath" -ForegroundColor White
if (Test-Path $outputPath) {
    $outputFile = Get-Item $outputPath
    Write-Host "   ✅ Output file exists" -ForegroundColor Green
    Write-Host "      Size: $($outputFile.Length) bytes" -ForegroundColor White
    Write-Host "      Modified: $($outputFile.LastWriteTime)" -ForegroundColor White
    
    # Calculate age in minutes
    $ageMinutes = [math]::Round(((Get-Date) - $outputFile.LastWriteTime).TotalMinutes, 0)
    Write-Host "      Age: $ageMinutes minutes" -ForegroundColor White
    
    if ($ageMinutes -gt 65) {
        Write-Host "      ⚠️  File is too old (max 65 minutes)" -ForegroundColor Yellow
    } else {
        Write-Host "      ✅ File is current" -ForegroundColor Green
    }
} else {
    Write-Host "   ❌ Output file missing!" -ForegroundColor Red
}

# Check recent Windows Event Log entries for Task Scheduler
Write-Host "`n7. Recent Task Scheduler Events..." -ForegroundColor Yellow
try {
    $events = Get-WinEvent -FilterHashtable @{LogName='Microsoft-Windows-TaskScheduler/Operational'; StartTime=(Get-Date).AddDays(-7)} -MaxEvents 50 -ErrorAction SilentlyContinue | 
              Where-Object { $_.Message -like "*$TaskName*" } | 
              Sort-Object TimeCreated -Descending | 
              Select-Object -First 10

    if ($events) {
        Write-Host "   Found $($events.Count) recent events for this task:" -ForegroundColor White
        foreach ($event in $events) {
            $level = switch ($event.LevelDisplayName) {
                "Information" { "ℹ️" }
                "Warning" { "⚠️" }
                "Error" { "❌" }
                default { "📝" }
            }
            Write-Host "   $level $($event.TimeCreated.ToString('MM/dd HH:mm')) - ID:$($event.Id) - $($event.LevelDisplayName)" -ForegroundColor White
            if ($Detailed) {
                Write-Host "      $($event.Message.Split("`n")[0])" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "   ⚠️  No recent events found for this task" -ForegroundColor Yellow
        Write-Host "      This could indicate the task hasn't run recently" -ForegroundColor Gray
    }
} catch {
    Write-Host "   ⚠️  Could not access Task Scheduler event log" -ForegroundColor Yellow
    Write-Host "      Error: $($_.Exception.Message)" -ForegroundColor Gray
}

if ($TestRun) {
    Write-Host "`n8. Test Run..." -ForegroundColor Yellow
    Write-Host "   Starting task manually..." -ForegroundColor White
    try {
        Start-ScheduledTask -TaskName $TaskName
        Write-Host "   ✅ Task started successfully" -ForegroundColor Green
        
        # Wait a moment and check status
        Start-Sleep -Seconds 5
        $newTaskInfo = Get-ScheduledTaskInfo -TaskName $TaskName
        Write-Host "   Current State: $((Get-ScheduledTask -TaskName $TaskName).State)" -ForegroundColor White
        Write-Host "   Last Result: $($newTaskInfo.LastTaskResult)" -ForegroundColor White
        
    } catch {
        Write-Host "   ❌ Failed to start task: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n📋 Summary & Recommendations:" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

if ($taskInfo.LastTaskResult -eq 0) {
    Write-Host "✅ Task appears to be configured correctly" -ForegroundColor Green
} else {
    Write-Host "⚠️  Task has error code: $resultMessage" -ForegroundColor Yellow
}

if ($taskInfo.NumberOfMissedRuns -gt 0) {
    Write-Host "⚠️  Task has $($taskInfo.NumberOfMissedRuns) missed runs" -ForegroundColor Yellow
}

Write-Host "`nNext Steps:" -ForegroundColor White
Write-Host "1. Enable history: .\Scripts\diagnose_dns_task.ps1 -EnableHistory" -ForegroundColor Gray
Write-Host "2. Run detailed check: .\Scripts\diagnose_dns_task.ps1 -Detailed" -ForegroundColor Gray
Write-Host "3. Test manual run: .\Scripts\diagnose_dns_task.ps1 -TestRun" -ForegroundColor Gray
Write-Host "4. Check Event Viewer for detailed logs" -ForegroundColor Gray
