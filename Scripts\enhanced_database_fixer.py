#!/usr/bin/env python3
"""
Enhanced Database Reference Fixer

Combines validation, repair, and SQL testing with comprehensive logging.
Provides complete audit trail of all database schema maintenance operations.

AUTHOR: N8N_Builder Team
DATE: 2025-08-18
PURPOSE: Production-ready database schema maintenance with full logging
"""

import re
import sys
import shutil
import asyncio
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Set, Tuple, Optional

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from Scripts.database_repair_logger import DatabaseRepairLogger
from Scripts.sql_validator import SQLValidator
from Scripts.refined_sql_detector import RefinedSQLDetector

class EnhancedDatabaseFixer:
    """Enhanced database fixer with logging and validation."""
    
    def __init__(self, database_name: str = 'knowledgebase'):
        self.database_name = database_name
        self.logger = DatabaseRepairLogger()
        self.sql_validator = SQLValidator(database_name)
        self.sql_validator.set_logger(self.logger)
        self.sql_detector = RefinedSQLDetector()
        
        # Define repair rules
        self.repair_rules = {
            # High confidence fixes (definite errors)
            'REF_Entities': 'REF_Entity',
            'REF_Attributes': 'REF_Attribute',
            'REF_EntityValues': 'REF_EntityValue',
            'XRF_Entity_Attribute_Value': 'XRF_EntityAttributeValue',
            'XRF_Entity_AttributeValue': 'XRF_EntityAttributeValue',
            
            # Medium confidence fixes (likely errors)
            'REF_Facts': 'REF_Fact',
            'REF_Opinions': 'REF_Opinion',
            'REF_Sources': 'REF_Source',
            'REF_Categories': 'REF_Category',
        }
        
        # File processing settings
        self.scan_directories = ['Self_Healer', 'Scripts', 'tests']
        self.file_extensions = {'.py', '.sql'}
        
    def create_backup(self, file_path: Path) -> Path:
        """Create a backup of the file before modification."""
        backup_dir = Path("data/repair_backups")
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{file_path.name}.backup_{timestamp}"
        backup_path = backup_dir / backup_name
        
        shutil.copy2(file_path, backup_path)
        return backup_path
    
    def find_sql_statements(self, content: str) -> List[Tuple[int, str]]:
        """Find SQL statements in file content using refined detection."""
        sql_statements = []

        # Use the refined SQL detector to find actual SQL statements
        detected_sql = self.sql_detector.detect_sql_in_content(content)

        for sql_stmt in detected_sql:
            sql_statements.append((sql_stmt.line_number, sql_stmt.statement))

        return sql_statements
    
    async def process_file(self, file_path: Path, dry_run: bool = True) -> Dict:
        """Process a single file with validation, repair, and SQL testing."""
        results = {
            'file_path': str(file_path),
            'repairs_made': 0,
            'sql_tests_passed': 0,
            'sql_tests_failed': 0,
            'errors': []
        }
        
        try:
            # Read the file
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                original_content = f.read()
            
            modified_content = original_content
            repairs_made = 0
            
            # Apply repairs
            for incorrect, correct in self.repair_rules.items():
                if incorrect in modified_content:
                    count = modified_content.count(incorrect)
                    if count > 0:
                        modified_content = modified_content.replace(incorrect, correct)
                        repairs_made += count
                        
                        # Log each repair
                        self.logger.log_repair(
                            str(file_path), 0,  # Line number would need more complex tracking
                            f"Contains {count} instances of '{incorrect}'",
                            f"Replaced with '{correct}'",
                            f"Fix table name: {incorrect} → {correct}"
                        )
                        
                        print(f"   📝 {file_path.name}: {incorrect} → {correct} ({count} times)")
            
            # Write the modified file if not dry run and changes were made
            if repairs_made > 0 and not dry_run:
                # Create backup
                backup_path = self.create_backup(file_path)
                
                # Write modified content
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                
                print(f"      ✅ Repaired and backed up to: {backup_path.name}")
            
            results['repairs_made'] = repairs_made
            
            # Validate SQL statements in the (possibly modified) content
            sql_statements = self.find_sql_statements(modified_content)
            
            for line_num, sql_statement in sql_statements:
                # Skip very short statements that are likely not complete SQL
                if len(sql_statement.strip()) < 10:
                    continue
                
                validation_result = await self.sql_validator.validate_sql(
                    sql_statement, str(file_path), line_num
                )
                
                if validation_result.success:
                    results['sql_tests_passed'] += 1
                else:
                    results['sql_tests_failed'] += 1
                    results['errors'].append({
                        'line': line_num,
                        'sql': sql_statement,
                        'error': validation_result.error_message
                    })
            
        except Exception as e:
            error_msg = f"Error processing file: {str(e)}"
            results['errors'].append({'error': error_msg})
            self.logger.log_validation(str(file_path), 0, "", "file_processing_error", "failed", error_msg)
        
        return results
    
    async def process_codebase(self, dry_run: bool = True) -> Dict:
        """Process the entire codebase with comprehensive logging."""
        print(f"🔧 Enhanced Database Reference Fixer")
        print(f"{'=' * 50}")
        print(f"Mode: {'DRY RUN' if dry_run else 'LIVE REPAIR'}")
        print(f"Database: {self.database_name}")
        print(f"Session ID: {self.logger.session_id}")
        
        project_root = Path(__file__).parent.parent
        summary = {
            'files_processed': 0,
            'files_modified': 0,
            'total_repairs': 0,
            'total_sql_tests': 0,
            'sql_tests_passed': 0,
            'sql_tests_failed': 0,
            'errors': []
        }
        
        for directory in self.scan_directories:
            dir_path = project_root / directory
            if not dir_path.exists():
                continue
            
            print(f"\n📁 Processing directory: {directory}")
            
            for file_path in dir_path.rglob('*'):
                if file_path.is_file() and file_path.suffix in self.file_extensions:
                    summary['files_processed'] += 1
                    
                    file_results = await self.process_file(file_path, dry_run)
                    
                    if file_results['repairs_made'] > 0:
                        summary['files_modified'] += 1
                        summary['total_repairs'] += file_results['repairs_made']
                    
                    summary['total_sql_tests'] += (file_results['sql_tests_passed'] + file_results['sql_tests_failed'])
                    summary['sql_tests_passed'] += file_results['sql_tests_passed']
                    summary['sql_tests_failed'] += file_results['sql_tests_failed']
                    summary['errors'].extend(file_results['errors'])
                    
                    # Print file summary if there was activity
                    if file_results['repairs_made'] > 0 or file_results['sql_tests_failed'] > 0:
                        relative_path = file_path.relative_to(project_root)
                        print(f"   📄 {relative_path}")
                        if file_results['repairs_made'] > 0:
                            print(f"      🔧 Repairs: {file_results['repairs_made']}")
                        if file_results['sql_tests_passed'] > 0:
                            print(f"      ✅ SQL Tests Passed: {file_results['sql_tests_passed']}")
                        if file_results['sql_tests_failed'] > 0:
                            print(f"      ❌ SQL Tests Failed: {file_results['sql_tests_failed']}")
        
        return summary
    
    def print_final_summary(self, summary: Dict, dry_run: bool):
        """Print comprehensive final summary."""
        print(f"\n📊 FINAL SUMMARY")
        print(f"{'=' * 50}")
        print(f"Files processed: {summary['files_processed']}")
        print(f"Files modified: {summary['files_modified']}")
        print(f"Total repairs: {summary['total_repairs']}")
        print(f"SQL tests run: {summary['total_sql_tests']}")
        print(f"SQL tests passed: {summary['sql_tests_passed']}")
        print(f"SQL tests failed: {summary['sql_tests_failed']}")
        
        if summary['total_sql_tests'] > 0:
            success_rate = (summary['sql_tests_passed'] / summary['total_sql_tests']) * 100
            print(f"SQL success rate: {success_rate:.1f}%")
        
        if summary['errors']:
            print(f"\n❌ ERRORS ENCOUNTERED ({len(summary['errors'])}):")
            for i, error in enumerate(summary['errors'][:5], 1):  # Show first 5 errors
                if 'line' in error:
                    print(f"   {i}. Line {error['line']}: {error['error']}")
                else:
                    print(f"   {i}. {error.get('error', 'Unknown error')}")
            
            if len(summary['errors']) > 5:
                print(f"   ... and {len(summary['errors']) - 5} more errors (see detailed logs)")
        
        if dry_run:
            print(f"\n🔍 DRY RUN COMPLETE - No files were modified")
            print(f"   Run with --live to apply repairs")
        else:
            print(f"\n✅ REPAIRS APPLIED")
            if summary['files_modified'] > 0:
                print(f"   Backups created in: data/repair_backups/")
        
        # Show logger summary
        self.logger.print_summary()

async def main():
    """Main execution function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Enhanced database reference fixer with logging')
    parser.add_argument('--live', action='store_true',
                       help='Apply fixes (default is dry-run)')
    parser.add_argument('--database', default='knowledgebase',
                       help='Database connection name')
    
    args = parser.parse_args()
    
    # Initialize fixer
    fixer = EnhancedDatabaseFixer(args.database)
    
    try:
        # Process codebase
        summary = await fixer.process_codebase(dry_run=not args.live)
        
        # Print final summary
        fixer.print_final_summary(summary, dry_run=not args.live)
        
        # Close logging session
        fixer.logger.close_session()
        
        # Return appropriate exit code
        return 0 if len(summary['errors']) == 0 else 1
        
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        fixer.logger.close_session()
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
