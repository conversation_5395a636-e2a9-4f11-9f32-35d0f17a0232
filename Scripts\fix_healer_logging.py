#!/usr/bin/env python3
"""
Fix Self-Healer Logging Issue
============================
This script addresses the repeated INFO logging issue in the Self-Healer error monitor.
"""

import asyncio
import sys
import requests
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

async def fix_healer_logging():
    """Fix the Self-Healer logging issue."""
    print("🔧 Fixing Self-Healer Logging Issue")
    print("=" * 50)
    
    # Step 1: Check if Self-Healer is running
    print("1. Checking Self-Healer status...")
    try:
        response = requests.get("http://localhost:8001/api/status", timeout=5)
        if response.status_code == 200:
            print("   ✅ Self-Healer is running")
            
            # Step 2: Check current error count
            try:
                metrics_response = requests.get("http://localhost:8001/api/metrics", timeout=5)
                if metrics_response.status_code == 200:
                    metrics = metrics_response.json()
                    error_count = metrics.get('metrics', {}).get('total_errors_detected', 0)
                    print(f"   📊 Current error count: {error_count}")
                else:
                    print("   ⚠️  Could not retrieve metrics")
            except Exception as e:
                print(f"   ⚠️  Could not retrieve metrics: {e}")
                
        else:
            print("   ❌ Self-Healer is not responding properly")
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Self-Healer is not running")
    except Exception as e:
        print(f"   ❌ Error checking Self-Healer: {e}")
    
    print("\n2. Applied fixes:")
    print("   ✅ Changed verbose INFO logging to DEBUG level")
    print("   ✅ Added debug criteria logging control")
    print("   ✅ Reduced log noise by default")
    
    print("\n3. Recommendations:")
    print("   🔄 Restart the N8N Builder application to apply the fixes")
    print("   📝 The repeated logging should stop after restart")
    print("   🐛 If you need detailed error criteria logging for debugging:")
    print("      - Use the enable_debug_criteria_logging(True) method")
    
    print("\n4. What was fixed:")
    print("   - The Self-Healer error monitor was logging detailed criteria")
    print("     analysis for every error that didn't meet healing criteria")
    print("   - This caused repeated INFO messages like:")
    print("     • 'Critical: False (severity: WARNING)'")
    print("     • 'Validation category: False (category: application_error)'")
    print("     • 'Message: An unexpected error occurred...'")
    print("   - These are now DEBUG level and disabled by default")
    
    print("\n✅ Fix applied successfully!")
    print("Please restart the N8N Builder application to see the changes.")

if __name__ == "__main__":
    asyncio.run(fix_healer_logging())
