@echo off
echo N8N Docker Container Fix
echo ========================

echo.
echo 1. Checking N8N container status...
docker ps --filter "name=n8n"

echo.
echo 2. Checking container working directory...
docker exec n8n pwd

echo.
echo 3. Checking if shared directory exists...
docker exec n8n ls -la /home/<USER>/

echo.
echo 4. Checking shared directory contents...
docker exec n8n ls -la /home/<USER>/shared/

echo.
echo 5. Checking DNS reports directory...
docker exec n8n ls -la /home/<USER>/shared/dns_reports/

echo.
echo 6. Checking DNS setup directory...
docker exec n8n ls -la /home/<USER>/shared/dns_reports/setup/

echo.
echo 7. Testing file access...
docker exec n8n test -f /home/<USER>/shared/dns_reports/setup/dns_cache_output.txt
if %errorlevel% equ 0 (
    echo ✓ DNS cache file is accessible from container
    docker exec n8n stat /home/<USER>/shared/dns_reports/setup/dns_cache_output.txt
) else (
    echo ✗ DNS cache file is NOT accessible from container
    echo This indicates a volume mount issue
)

echo.
echo 8. Checking N8N service status inside container...
docker exec n8n ps aux | findstr n8n

echo.
echo 9. Attempting to restart N8N container...
echo Stopping N8N container...
docker stop n8n
echo Starting N8N container...
docker start n8n

echo.
echo 10. Waiting for container to start...
timeout /t 10

echo.
echo 11. Checking new container status...
docker ps --filter "name=n8n"

echo.
echo Fix attempt complete. Check if N8N is now healthy.
pause
