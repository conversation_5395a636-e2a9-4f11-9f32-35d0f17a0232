#!/usr/bin/env python3
"""
Table Name Fix Script

This script uses the database repair summary CSV to systematically fix table name 
references across the codebase. It takes old/new table name mappings and updates
all occurrences found in the validation results.

Usage:
    python fix_table_names.py <csv_file> <old_table_name> <new_table_name>
    
Example:
    python fix_table_names.py repair_summary_20250823_110612.csv XRF_EntityAttributeValue XRF_Entity_AttributeValue

Author: N8N_Builder SQL Validation System
Date: 2025-08-23
"""

import sys
import csv
import re
import os
from pathlib import Path
from typing import List, Dict, Tuple
import argparse
from datetime import datetime

class TableNameFixer:
    def __init__(self, csv_file: str, old_name: str, new_name: str):
        self.csv_file = csv_file
        self.old_name = old_name
        self.new_name = new_name
        self.fixes_applied = []
        self.errors = []
        
    def load_repair_summary(self) -> List[Dict]:
        """Load the repair summary CSV and find entries with the old table name."""
        entries = []
        try:
            with open(self.csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    # Check if this row contains the old table name in SQL Validation column
                    sql_validation = row.get('SQL Validation', '')
                    error_info = row.get('Error', '')

                    # Look for the table name in either SQL validation results or error messages
                    if (self.old_name in sql_validation or
                        self.old_name in error_info or
                        row.get('Status', '') == 'failed'):

                        # Only include failed entries that mention our table
                        if (self.old_name in sql_validation or self.old_name in error_info):
                            entries.append(row)

            print(f"📊 Found {len(entries)} entries containing '{self.old_name}' in {self.csv_file}")
            return entries
            
        except FileNotFoundError:
            print(f"❌ Error: CSV file '{self.csv_file}' not found")
            return []
        except Exception as e:
            print(f"❌ Error reading CSV file: {e}")
            return []
    
    def fix_file_content(self, file_path: str, line_number: int, expected_content: str = "") -> bool:
        """Fix table name references in a specific file and nearby lines."""
        try:
            # Read the file
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Check if line number is valid
            if line_number < 1 or line_number > len(lines):
                self.errors.append(f"Invalid line number {line_number} in {file_path}")
                return False

            # Search in a range around the reported line number (±5 lines)
            search_start = max(0, line_number - 6)
            search_end = min(len(lines), line_number + 5)

            fixes_made = False

            for i in range(search_start, search_end):
                original_line = lines[i].rstrip('\n\r')

                # Replace the table name (case-sensitive, word boundaries)
                updated_line = re.sub(
                    r'\b' + re.escape(self.old_name) + r'\b',
                    self.new_name,
                    original_line
                )

                # Check if any changes were made
                if updated_line != original_line:
                    # Update the line
                    lines[i] = updated_line + '\n'

                    self.fixes_applied.append({
                        'file': file_path,
                        'line': i + 1,  # Convert back to 1-based
                        'old_content': original_line,
                        'new_content': updated_line
                    })

                    print(f"✅ Fixed {file_path}:{i + 1}")
                    print(f"   Old: {original_line.strip()}")
                    print(f"   New: {updated_line.strip()}")

                    fixes_made = True

            if not fixes_made:
                print(f"⚠️  No changes needed in {file_path} around line {line_number}")
                return False

            # Write the file back
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)

            return True

        except FileNotFoundError:
            self.errors.append(f"File not found: {file_path}")
            return False
        except Exception as e:
            self.errors.append(f"Error fixing {file_path}:{line_number} - {e}")
            return False
    
    def process_entries(self, entries: List[Dict]) -> None:
        """Process all entries and fix table name references."""
        print(f"\n🔧 Processing {len(entries)} entries to replace '{self.old_name}' with '{self.new_name}'")
        print("=" * 80)

        # Track processed files to avoid duplicates
        processed_files = set()

        for i, entry in enumerate(entries, 1):
            file_path = entry.get('File', '')
            line_number_str = entry.get('Line', '0')

            # Handle line number conversion
            try:
                line_number = int(line_number_str) if line_number_str else 0
            except ValueError:
                line_number = 0

            # Create a unique key for this file/line combination
            file_key = f"{file_path}:{line_number}"

            print(f"\n[{i}/{len(entries)}] Processing: {file_path}:{line_number}")

            # Skip if we've already processed this file/line combination
            if file_key in processed_files:
                print(f"⏭️  Already processed: {file_path}:{line_number}")
                continue

            processed_files.add(file_key)

            # Skip if file path is empty or invalid
            if not file_path or not os.path.exists(file_path):
                self.errors.append(f"File not found or invalid path: {file_path}")
                print(f"❌ File not found: {file_path}")
                continue

            # Skip if line number is invalid
            if line_number <= 0:
                self.errors.append(f"Invalid line number {line_number} for {file_path}")
                print(f"❌ Invalid line number: {line_number}")
                continue

            # Apply the fix (we'll read the original content from the file)
            self.fix_file_content(file_path, line_number, "")
    
    def generate_report(self) -> None:
        """Generate a summary report of all fixes applied."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"data/database_repair_logs/table_name_fixes_{timestamp}.txt"
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"Table Name Fix Report\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Source CSV: {self.csv_file}\n")
            f.write(f"Table Name Change: {self.old_name} → {self.new_name}\n")
            f.write("=" * 80 + "\n\n")
            
            f.write(f"SUMMARY:\n")
            f.write(f"  Fixes Applied: {len(self.fixes_applied)}\n")
            f.write(f"  Errors: {len(self.errors)}\n\n")
            
            if self.fixes_applied:
                f.write("FIXES APPLIED:\n")
                f.write("-" * 40 + "\n")
                for fix in self.fixes_applied:
                    f.write(f"File: {fix['file']}\n")
                    f.write(f"Line: {fix['line']}\n")
                    f.write(f"Old:  {fix['old_content'].strip()}\n")
                    f.write(f"New:  {fix['new_content'].strip()}\n")
                    f.write("\n")
            
            if self.errors:
                f.write("ERRORS:\n")
                f.write("-" * 40 + "\n")
                for error in self.errors:
                    f.write(f"  {error}\n")
        
        print(f"\n📄 Report saved to: {report_file}")
    
    def run(self) -> bool:
        """Main execution method."""
        print(f"🔧 Table Name Fixer")
        print(f"📁 CSV File: {self.csv_file}")
        print(f"🔄 Change: {self.old_name} → {self.new_name}")
        print("=" * 80)
        
        # Load entries from CSV
        entries = self.load_repair_summary()
        if not entries:
            print("❌ No entries found to process")
            return False
        
        # Process all entries
        self.process_entries(entries)
        
        # Generate report
        self.generate_report()
        
        # Print summary
        print("\n" + "=" * 80)
        print(f"🎯 SUMMARY:")
        print(f"   ✅ Fixes Applied: {len(self.fixes_applied)}")
        print(f"   ❌ Errors: {len(self.errors)}")
        
        if self.errors:
            print(f"\n⚠️  ERRORS:")
            for error in self.errors:
                print(f"   {error}")
        
        return len(self.fixes_applied) > 0

def main():
    parser = argparse.ArgumentParser(
        description="Fix table name references using database repair summary CSV",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python fix_table_names.py repair_summary.csv XRF_EntityAttributeValue XRF_Entity_AttributeValue
  python fix_table_names.py data/logs/repair_summary_20250823.csv OLD_TABLE NEW_TABLE
        """
    )
    
    parser.add_argument('csv_file', help='Path to the repair summary CSV file')
    parser.add_argument('old_name', help='Old table name to replace')
    parser.add_argument('new_name', help='New table name to use')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be changed without making changes')
    
    args = parser.parse_args()
    
    # Validate arguments
    if not os.path.exists(args.csv_file):
        print(f"❌ Error: CSV file '{args.csv_file}' not found")
        return 1
    
    if args.old_name == args.new_name:
        print(f"❌ Error: Old and new table names are the same")
        return 1
    
    # Create and run the fixer
    fixer = TableNameFixer(args.csv_file, args.old_name, args.new_name)
    
    if args.dry_run:
        print("🔍 DRY RUN MODE - No changes will be made")
        # TODO: Implement dry run functionality
        return 0
    
    success = fixer.run()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
