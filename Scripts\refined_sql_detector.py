#!/usr/bin/env python3
"""
Refined SQL Statement Detector

Precisely identifies SQL statements within quotation marks to avoid false positives
from Python imports, comments, and other non-SQL code.

AUTHOR: N8N_Builder Team
DATE: 2025-08-18
PURPOSE: Accurate SQL statement detection for validation
"""

import re
from typing import List, Tuple, Optional
from dataclasses import dataclass

@dataclass
class SQLStatement:
    """Represents a detected SQL statement."""
    line_number: int
    statement: str
    quote_type: str  # 'single', 'double', 'triple_single', 'triple_double'
    sql_type: str    # 'SELECT', 'INSERT', 'UPDATE', 'DELETE'
    start_pos: int
    end_pos: int

class RefinedSQLDetector:
    """Precisely detects SQL statements within quoted strings."""
    
    def __init__(self):
        # SQL keywords that must be present
        self.sql_keywords = {
            'SELECT', 'INSERT', 'UPDATE', 'DELETE'
        }
        
        # Supporting keywords that strengthen SQL detection
        self.supporting_keywords = {
            'FROM', 'WHERE', 'JOIN', 'INTO', 'SET', 'VALUES', 
            'ORDER BY', 'GROUP BY', 'HAVING', 'LIMIT', 'TOP'
        }
        
        # Patterns for different quote types
        self.quote_patterns = [
            # Triple quotes (multiline strings)
            (r'"""(.*?)"""', 'triple_double'),
            (r"'''(.*?)'''", 'triple_single'),
            # Regular quotes
            (r'"([^"\\]*(\\.[^"\\]*)*)"', 'double'),
            (r"'([^'\\]*(\\.[^'\\]*)*)'", 'single'),
        ]
        
        # Compile regex patterns for SQL detection
        self.sql_patterns = {}
        for keyword in self.sql_keywords:
            # Look for SQL keyword followed by supporting elements
            pattern = rf'\b{keyword}\b.*?\b(?:FROM|INTO|SET|VALUES)\b'
            self.sql_patterns[keyword] = re.compile(pattern, re.IGNORECASE | re.DOTALL)
    
    def extract_quoted_strings(self, content: str) -> List[Tuple[str, str, int, int]]:
        """Extract all quoted strings from content."""
        quoted_strings = []
        
        for pattern, quote_type in self.quote_patterns:
            regex = re.compile(pattern, re.DOTALL)
            
            for match in regex.finditer(content):
                quoted_text = match.group(1) if quote_type.startswith('triple') else match.group(1)
                start_pos = match.start()
                end_pos = match.end()
                
                quoted_strings.append((quoted_text, quote_type, start_pos, end_pos))
        
        # Sort by position to maintain order
        quoted_strings.sort(key=lambda x: x[2])
        return quoted_strings
    
    def is_sql_statement(self, text: str) -> Tuple[bool, str]:
        """Determine if a quoted string contains a SQL statement."""
        text_upper = text.upper()
        
        # Must start with a SQL keyword
        sql_type = None
        for keyword in self.sql_keywords:
            if text_upper.strip().startswith(keyword):
                sql_type = keyword
                break
        
        if not sql_type:
            return False, ""
        
        # Must contain at least one supporting keyword
        has_supporting = any(keyword in text_upper for keyword in self.supporting_keywords)
        
        if not has_supporting:
            return False, ""
        
        # Additional validation: check for SQL-like structure
        if sql_type == 'SELECT':
            # SELECT must have FROM (unless it's SELECT 1, SELECT @@VERSION, etc.)
            if 'FROM' not in text_upper and not re.search(r'SELECT\s+[\d@]', text_upper):
                return False, ""
        
        elif sql_type == 'INSERT':
            # INSERT must have INTO
            if 'INTO' not in text_upper:
                return False, ""
        
        elif sql_type == 'UPDATE':
            # UPDATE must have SET
            if 'SET' not in text_upper:
                return False, ""
        
        elif sql_type == 'DELETE':
            # DELETE must have FROM
            if 'FROM' not in text_upper:
                return False, ""
        
        return True, sql_type
    
    def find_line_number(self, content: str, position: int) -> int:
        """Find the line number for a given character position."""
        return content[:position].count('\n') + 1
    
    def detect_sql_in_content(self, content: str) -> List[SQLStatement]:
        """Detect all SQL statements in the given content."""
        sql_statements = []
        
        # Extract all quoted strings
        quoted_strings = self.extract_quoted_strings(content)
        
        for quoted_text, quote_type, start_pos, end_pos in quoted_strings:
            # Skip very short strings (likely not SQL)
            if len(quoted_text.strip()) < 10:
                continue
            
            # Check if it's a SQL statement
            is_sql, sql_type = self.is_sql_statement(quoted_text)
            
            if is_sql:
                line_number = self.find_line_number(content, start_pos)
                
                sql_statement = SQLStatement(
                    line_number=line_number,
                    statement=quoted_text.strip(),
                    quote_type=quote_type,
                    sql_type=sql_type,
                    start_pos=start_pos,
                    end_pos=end_pos
                )
                
                sql_statements.append(sql_statement)
        
        return sql_statements
    
    def detect_sql_in_file(self, file_path: str) -> List[SQLStatement]:
        """Detect SQL statements in a file."""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            return self.detect_sql_in_content(content)
        
        except Exception as e:
            print(f"Error reading file {file_path}: {e}")
            return []
    
    def validate_detection_accuracy(self, test_cases: List[Tuple[str, bool]]) -> dict:
        """Test the detector against known cases."""
        results = {
            'total_tests': len(test_cases),
            'correct': 0,
            'false_positives': 0,
            'false_negatives': 0,
            'details': []
        }
        
        for test_content, expected_has_sql in test_cases:
            detected_sql = self.detect_sql_in_content(test_content)
            has_sql = len(detected_sql) > 0
            
            if has_sql == expected_has_sql:
                results['correct'] += 1
                status = 'CORRECT'
            elif has_sql and not expected_has_sql:
                results['false_positives'] += 1
                status = 'FALSE POSITIVE'
            else:
                results['false_negatives'] += 1
                status = 'FALSE NEGATIVE'
            
            results['details'].append({
                'content': test_content[:50] + '...' if len(test_content) > 50 else test_content,
                'expected': expected_has_sql,
                'detected': has_sql,
                'status': status,
                'sql_found': [stmt.statement[:30] + '...' for stmt in detected_sql]
            })
        
        return results

def test_sql_detector():
    """Test the SQL detector with various cases."""
    detector = RefinedSQLDetector()
    
    # Test cases: (content, should_detect_sql)
    test_cases = [
        # Positive cases (should detect SQL)
        ('query = "SELECT * FROM REF_Entity WHERE Name = \'test\'"', True),
        ('sql = """INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)"""', True),
        ('cmd = "UPDATE REF_Entity SET Name = ? WHERE ID = ?"', True),
        ('delete_sql = "DELETE FROM REF_Entity WHERE ID = ?"', True),
        ('query = "SELECT COUNT(*) FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID"', True),
        
        # Negative cases (should NOT detect SQL)
        ('from pathlib import Path', False),
        ('from core.healer_manager import SelfHealerManager', False),
        ('import select, insert, update, delete', False),
        ('print("SELECT your option FROM the menu")', False),
        ('message = "Please INSERT your card INTO the slot"', False),
        ('log_message = "UPDATE completed successfully"', False),
        ('error = "DELETE operation failed"', False),
        ('# SELECT * FROM table -- this is a comment', False),
        ('SELECT = "some variable name"', False),
        
        # Edge cases
        ('query = "SELECT 1"', True),  # Simple SELECT
        ('query = "SELECT @@VERSION"', True),  # System function
        ('query = "SELECT * FROM"', False),  # Incomplete SQL
        ('text = "The word SELECT appears here but not as SQL"', False),
        ('sql = "SELECT name FROM users WHERE active = 1 ORDER BY name"', True),  # Complete SQL
    ]
    
    print("🧪 Testing Refined SQL Detector")
    print("=" * 50)
    
    results = detector.validate_detection_accuracy(test_cases)
    
    print(f"📊 Test Results:")
    print(f"   Total tests: {results['total_tests']}")
    print(f"   Correct: {results['correct']}")
    print(f"   False positives: {results['false_positives']}")
    print(f"   False negatives: {results['false_negatives']}")
    print(f"   Accuracy: {(results['correct']/results['total_tests']*100):.1f}%")
    
    # Show details for incorrect results
    incorrect = [d for d in results['details'] if d['status'] != 'CORRECT']
    if incorrect:
        print(f"\n❌ Incorrect Results ({len(incorrect)}):")
        for detail in incorrect:
            print(f"   {detail['status']}: {detail['content']}")
            print(f"      Expected: {detail['expected']}, Got: {detail['detected']}")
            if detail['sql_found']:
                print(f"      SQL Found: {detail['sql_found']}")
    else:
        print(f"\n✅ All tests passed!")
    
    return results

if __name__ == "__main__":
    # Run the test
    test_results = test_sql_detector()
    
    # Test on a real file if available
    detector = RefinedSQLDetector()
    
    test_file = "Self_Healer/core/knowledge_integration.py"
    try:
        sql_statements = detector.detect_sql_in_file(test_file)
        
        print(f"\n📄 Real File Test: {test_file}")
        print(f"   SQL statements found: {len(sql_statements)}")
        
        for stmt in sql_statements[:3]:  # Show first 3
            print(f"   Line {stmt.line_number} ({stmt.sql_type}): {stmt.statement[:50]}...")
            
    except Exception as e:
        print(f"\n⚠️  Could not test real file: {e}")
    
    print(f"\n🎯 SQL Detection Refinement Complete!")
    print(f"   Accuracy: {(test_results['correct']/test_results['total_tests']*100):.1f}%")
    print(f"   Ready for integration into validation system")
