#!/usr/bin/env python3
"""
Simple synchronous database table name validator
"""

import re
import sys
from pathlib import Path

def find_table_references_in_files():
    """Find potential table references in code files."""
    print("🔍 Simple Database Table Reference Validator")
    print("=" * 50)
    
    # Known table patterns from your naming conventions
    known_tables = {
        'REF_Entity', 'REF_Fact', 'REF_Opinion', 'REF_Source', 'REF_Category',
        'XRF_EntityFact', 'XRF_EntityOpinion', 'LOG_HealingSession', 'LOG_ErrorDetection'
    }
    
    # Common incorrect variations we might find
    incorrect_variations = {
        'REF_Entities': 'REF_Entity',  # The one we just fixed
        'REF_Facts': 'REF_Fact',
        'REF_Opinions': 'REF_Opinion',
        'REF_Sources': 'REF_Source',
        'REF_Categories': 'REF_Category'
    }
    
    # File extensions to scan
    file_extensions = {'.py', '.sql', '.ps1'}
    
    # Directories to scan
    scan_directories = ['n8n_builder', 'Self_Healer', 'Scripts', 'tests']
    
    issues_found = []
    files_scanned = 0
    
    project_root = Path(__file__).parent.parent
    
    print(f"📁 Scanning directories: {', '.join(scan_directories)}")
    print(f"🔍 Looking for table references...")
    
    for directory in scan_directories:
        dir_path = project_root / directory
        if not dir_path.exists():
            continue
            
        for file_path in dir_path.rglob('*'):
            if file_path.is_file() and file_path.suffix in file_extensions:
                files_scanned += 1
                
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        for line_num, line in enumerate(f, 1):
                            line_clean = line.strip()
                            
                            # Skip comments and empty lines
                            if not line_clean or line_clean.startswith('#') or line_clean.startswith('//'):
                                continue
                                
                            # Look for SQL keywords that might indicate table usage
                            if any(keyword in line_clean.upper() for keyword in ['FROM', 'JOIN', 'INSERT', 'UPDATE', 'DELETE']):
                                
                                # Check for incorrect table names
                                for incorrect, correct in incorrect_variations.items():
                                    if incorrect in line_clean:
                                        issues_found.append({
                                            'file': str(file_path.relative_to(project_root)),
                                            'line': line_num,
                                            'content': line_clean,
                                            'incorrect': incorrect,
                                            'correct': correct,
                                            'type': 'incorrect_table_name'
                                        })
                                
                                # Look for potential table references using regex
                                table_patterns = [
                                    r'\bFROM\s+([A-Za-z_][A-Za-z0-9_]*)',
                                    r'\bJOIN\s+([A-Za-z_][A-Za-z0-9_]*)',
                                    r'\bINSERT\s+INTO\s+([A-Za-z_][A-Za-z0-9_]*)',
                                    r'\bUPDATE\s+([A-Za-z_][A-Za-z0-9_]*)'
                                ]
                                
                                for pattern in table_patterns:
                                    matches = re.findall(pattern, line_clean, re.IGNORECASE)
                                    for match in matches:
                                        # Check if it looks like a table name but isn't in our known list
                                        if (match.startswith(('REF_', 'XRF_', 'LOG_', 'TBL_')) and 
                                            match not in known_tables and 
                                            match not in incorrect_variations):
                                            issues_found.append({
                                                'file': str(file_path.relative_to(project_root)),
                                                'line': line_num,
                                                'content': line_clean,
                                                'table_name': match,
                                                'type': 'unknown_table_name'
                                            })
                                
                except Exception as e:
                    print(f"⚠️  Error reading {file_path}: {e}")
    
    # Generate report
    print(f"\n📊 SCAN RESULTS:")
    print(f"   Files scanned: {files_scanned}")
    print(f"   Issues found: {len(issues_found)}")
    
    if issues_found:
        print(f"\n🚨 ISSUES REQUIRING ATTENTION:")
        
        # Group by type
        incorrect_names = [i for i in issues_found if i['type'] == 'incorrect_table_name']
        unknown_names = [i for i in issues_found if i['type'] == 'unknown_table_name']
        
        if incorrect_names:
            print(f"\n❌ INCORRECT TABLE NAMES ({len(incorrect_names)} found):")
            for issue in incorrect_names:
                print(f"   📄 {issue['file']}:{issue['line']}")
                print(f"      ❌ Found: {issue['incorrect']} → Should be: {issue['correct']}")
                print(f"      📝 {issue['content'][:80]}...")
                print()
        
        if unknown_names:
            print(f"\n⚠️  UNKNOWN TABLE NAMES ({len(unknown_names)} found):")
            for issue in unknown_names:
                print(f"   📄 {issue['file']}:{issue['line']}")
                print(f"      ❓ Table: {issue['table_name']}")
                print(f"      📝 {issue['content'][:80]}...")
                print()
        
        # Save detailed report
        report_file = project_root / 'data' / 'table_validation_report.txt'
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("DATABASE TABLE NAME VALIDATION REPORT\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Files scanned: {files_scanned}\n")
            f.write(f"Issues found: {len(issues_found)}\n\n")
            
            if incorrect_names:
                f.write("INCORRECT TABLE NAMES:\n")
                f.write("-" * 25 + "\n")
                for issue in incorrect_names:
                    f.write(f"File: {issue['file']}\n")
                    f.write(f"Line: {issue['line']}\n")
                    f.write(f"Incorrect: {issue['incorrect']}\n")
                    f.write(f"Correct: {issue['correct']}\n")
                    f.write(f"Content: {issue['content']}\n")
                    f.write("\n")
            
            if unknown_names:
                f.write("UNKNOWN TABLE NAMES:\n")
                f.write("-" * 20 + "\n")
                for issue in unknown_names:
                    f.write(f"File: {issue['file']}\n")
                    f.write(f"Line: {issue['line']}\n")
                    f.write(f"Table: {issue['table_name']}\n")
                    f.write(f"Content: {issue['content']}\n")
                    f.write("\n")
        
        print(f"📄 Detailed report saved to: {report_file}")
        return False
    else:
        print(f"\n🎉 SUCCESS: No table name issues found!")
        return True

if __name__ == "__main__":
    success = find_table_references_in_files()
    if not success:
        print(f"\n🔧 NEXT STEPS:")
        print(f"   1. Review the issues found above")
        print(f"   2. Fix incorrect table names in the identified files")
        print(f"   3. Verify unknown table names exist in your database")
        print(f"   4. Re-run this script to confirm fixes")
    sys.exit(0 if success else 1)
