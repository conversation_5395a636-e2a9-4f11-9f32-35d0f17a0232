#!/usr/bin/env python3
"""
SQL Validation Script

Converts SQL statements to safe SELECT queries for testing table and column names.
Validates that database references work without modifying data.

AUTHOR: N8N_Builder Team
DATE: 2025-08-18
PURPOSE: Safe SQL validation for database schema testing
"""

import re
import sys
import asyncio
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from n8n_builder.mcp_database_tool import MCPDatabaseTool
from Scripts.database_repair_logger import DatabaseRepairLogger

@dataclass
class SQLValidationResult:
    """Result of SQL validation test."""
    original_sql: str
    test_sql: str
    success: bool
    error_message: Optional[str] = None
    tables_found: List[str] = None
    columns_found: List[str] = None

class SQLValidator:
    """Validates SQL statements by converting them to safe SELECT queries."""
    
    def __init__(self, database_name: str = 'knowledgebase'):
        self.database_name = database_name
        self.db_tool = MCPDatabaseTool(database_name)
        self.logger = None  # Will be set externally
        
    def set_logger(self, logger: DatabaseRepairLogger):
        """Set the logger for this validator."""
        self.logger = logger
    
    def extract_sql_components(self, sql: str) -> Dict[str, List[str]]:
        """
        Extract tables and columns from SQL statement using segmented parsing approach.
        This method uses a robust segmented approach instead of brittle regex patterns.
        """
        # Use the new segmented parsing approach
        components = self._parse_sql_segments(sql)

        return {
            'tables': components['tables'],
            'columns': components['columns']
        }

    def _parse_sql_segments(self, sql: str) -> Dict[str, List[str]]:
        """
        Parse SQL using segmented approach for better reliability.
        Segments SQL into clauses and parses each appropriately.
        """
        # Normalize SQL
        sql_clean = re.sub(r'\s+', ' ', sql.strip())

        # Determine statement type
        statement_type = self._get_statement_type(sql_clean)

        # Segment the SQL into clauses
        segments = self._segment_sql_by_clauses(sql_clean, statement_type)

        # Parse tables and aliases from segments
        tables, table_aliases = self._parse_table_segments(segments)

        # Parse columns from segments, excluding aliases
        columns = self._parse_column_segments(segments, table_aliases, statement_type)

        return {
            'tables': list(set(tables)),
            'columns': list(set(columns)),
            'table_aliases': table_aliases
        }

    def _get_statement_type(self, sql: str) -> str:
        """Determine the type of SQL statement."""
        sql_upper = sql.upper().strip()

        if sql_upper.startswith('SELECT'):
            return 'SELECT'
        elif sql_upper.startswith('INSERT'):
            return 'INSERT'
        elif sql_upper.startswith('UPDATE'):
            return 'UPDATE'
        elif sql_upper.startswith('DELETE'):
            return 'DELETE'
        else:
            return 'UNKNOWN'

    def _segment_sql_by_clauses(self, sql: str, statement_type: str) -> Dict[str, str]:
        """
        Segment SQL into logical clauses based on statement type.
        Returns dict with clause names as keys and clause content as values.
        """
        segments = {}

        # Common clause patterns (shared across statement types)
        # Fixed patterns to handle end-of-string properly
        common_patterns = {
            'FROM': r'FROM\s+(.+?)(?=\s+(?:WHERE|GROUP\s+BY|HAVING|ORDER\s+BY|LIMIT|UNION|$))',
            'WHERE': r'WHERE\s+(.+?)(?=\s+(?:GROUP\s+BY|HAVING|ORDER\s+BY|LIMIT|UNION|$)|$)',
            'GROUP_BY': r'GROUP\s+BY\s+(.+?)(?=\s+(?:HAVING|ORDER\s+BY|LIMIT|UNION|$)|$)',
            'HAVING': r'HAVING\s+(.+?)(?=\s+(?:ORDER\s+BY|LIMIT|UNION|$)|$)',
            'ORDER_BY': r'ORDER\s+BY\s+(.+?)(?=\s+(?:LIMIT|UNION|$)|$)',
            'LIMIT': r'LIMIT\s+(.+?)(?=\s+(?:UNION|$)|$)'
        }

        # Statement-specific patterns
        if statement_type == 'SELECT':
            select_match = re.search(r'SELECT\s+(.+?)(?=\s+FROM)', sql, re.IGNORECASE | re.DOTALL)
            if select_match:
                segments['SELECT'] = select_match.group(1).strip()

        elif statement_type == 'INSERT':
            insert_match = re.search(r'INSERT\s+INTO\s+([^(]+?)(?:\s*\(|$)', sql, re.IGNORECASE)
            if insert_match:
                segments['INSERT_INTO'] = insert_match.group(1).strip()

            columns_match = re.search(r'INSERT\s+INTO\s+[^(]+?\s*\(([^)]+)\)', sql, re.IGNORECASE)
            if columns_match:
                segments['COLUMNS'] = columns_match.group(1).strip()

        elif statement_type == 'UPDATE':
            update_match = re.search(r'UPDATE\s+(.+?)(?=\s+SET)', sql, re.IGNORECASE)
            if update_match:
                segments['UPDATE'] = update_match.group(1).strip()

            set_match = re.search(r'SET\s+(.+?)(?=\s+(?:FROM|WHERE|$))', sql, re.IGNORECASE | re.DOTALL)
            if set_match:
                segments['SET'] = set_match.group(1).strip()

        elif statement_type == 'DELETE':
            # DELETE FROM - handle both "DELETE FROM table" and "DELETE table" patterns
            delete_match = re.search(r'DELETE\s+(?:FROM\s+)?(.+?)(?=\s+(?:WHERE|$))', sql, re.IGNORECASE)
            if delete_match:
                segments['DELETE_FROM'] = delete_match.group(1).strip()

        # Extract common clauses with improved matching
        for clause_name, pattern in common_patterns.items():
            match = re.search(pattern, sql, re.IGNORECASE | re.DOTALL)
            if match:
                segments[clause_name] = match.group(1).strip()

        # Special handling for simple cases where patterns might fail
        if statement_type == 'SELECT' and 'FROM' not in segments:
            # Try simpler FROM pattern for cases like "SELECT Name FROM Users"
            simple_from = re.search(r'FROM\s+([^;]+?)(?:\s*$)', sql, re.IGNORECASE)
            if simple_from:
                segments['FROM'] = simple_from.group(1).strip()

        return segments

    def _parse_table_segments(self, segments: Dict[str, str]) -> tuple[List[str], Dict[str, str]]:
        """
        Parse table references from relevant segments.
        Returns (tables, table_aliases) where table_aliases maps alias -> table_name.
        """
        all_tables = []
        all_aliases = {}

        # Determine which segments contain table references
        table_segments = []

        if 'FROM' in segments:
            table_segments.append(segments['FROM'])
        if 'UPDATE' in segments:
            table_segments.append(segments['UPDATE'])
        if 'DELETE_FROM' in segments:
            table_segments.append(segments['DELETE_FROM'])
        if 'INSERT_INTO' in segments:
            table_segments.append(segments['INSERT_INTO'])

        # Parse each table segment
        for segment_content in table_segments:
            tables, aliases = self._parse_single_table_segment(segment_content)
            all_tables.extend(tables)
            all_aliases.update(aliases)

        return all_tables, all_aliases

    def _parse_single_table_segment(self, content: str) -> tuple[List[str], Dict[str, str]]:
        """
        Parse a single table segment for tables and aliases.
        Handles comma-separated tables and JOIN syntax.
        """
        tables = []
        aliases = {}

        # Check for comma-separated tables (legacy syntax, no JOINs)
        if ',' in content and not self._has_join_keywords(content):
            # Comma-separated table list
            parts = [p.strip() for p in content.split(',')]
            for part in parts:
                table, alias = self._parse_single_table_reference(part)
                if table:
                    tables.append(table)
                    if alias:
                        aliases[alias] = table
        else:
            # JOIN syntax or single table
            tables, aliases = self._parse_join_syntax_tables(content)

        return tables, aliases

    def _has_join_keywords(self, content: str) -> bool:
        """Check if content contains JOIN keywords."""
        join_keywords = ['JOIN', 'INNER', 'LEFT', 'RIGHT', 'FULL', 'CROSS']
        content_upper = content.upper()
        return any(keyword in content_upper for keyword in join_keywords)

    def _parse_join_syntax_tables(self, content: str) -> tuple[List[str], Dict[str, str]]:
        """Parse JOIN syntax for tables and aliases."""
        tables = []
        aliases = {}

        # Handle single table case (no JOINs)
        if not self._has_join_keywords(content):
            table, alias = self._parse_single_table_reference(content.strip())
            if table:
                tables.append(table)
                if alias:
                    aliases[alias] = table
            return tables, aliases

        # Split on JOIN keywords while preserving structure
        join_pattern = r'\b(?:INNER\s+|LEFT\s+|RIGHT\s+|FULL\s+|CROSS\s+)?JOIN\b'
        parts = re.split(join_pattern, content, flags=re.IGNORECASE)

        # Process each part
        for part in parts:
            if not part.strip():
                continue

            # Remove ON clause if present
            if ' ON ' in part.upper():
                part = re.split(r'\s+ON\s+', part, flags=re.IGNORECASE)[0]

            table, alias = self._parse_single_table_reference(part.strip())
            if table:
                tables.append(table)
                if alias:
                    aliases[alias] = table

        return tables, aliases

    def _parse_single_table_reference(self, table_ref: str) -> tuple[str, str]:
        """
        Parse a single table reference for table name and alias.

        Handles:
        - Table1 -> (Table1, None)
        - Table1 T1 -> (Table1, T1)
        - Table1 AS T1 -> (Table1, T1)
        - schema.Table1 T1 -> (Table1, T1)
        """
        if not table_ref.strip():
            return None, None

        # Clean table name (remove schema prefix)
        def clean_table_name(name):
            return name.split('.')[-1] if '.' in name else name

        tokens = table_ref.strip().split()

        if len(tokens) == 1:
            # Just table name
            return clean_table_name(tokens[0]), None

        elif len(tokens) == 2:
            if tokens[1].upper() == 'AS':
                # Incomplete AS clause
                return clean_table_name(tokens[0]), None
            else:
                # Table alias
                return clean_table_name(tokens[0]), tokens[1]

        elif len(tokens) == 3 and tokens[1].upper() == 'AS':
            # Table AS alias
            return clean_table_name(tokens[0]), tokens[2]

        else:
            # Complex case - take first as table, last as potential alias
            return clean_table_name(tokens[0]), tokens[-1] if len(tokens) > 1 else None

    def _parse_column_segments(self, segments: Dict[str, str], table_aliases: Dict[str, str], statement_type: str) -> List[str]:
        """
        Parse column references from appropriate segments based on statement type.
        Excludes SQL keywords, functions, and table aliases.
        """
        columns = []

        # SQL keywords and functions to exclude from column detection
        sql_keywords = {
            'SELECT', 'FROM', 'WHERE', 'JOIN', 'LEFT', 'RIGHT', 'INNER', 'OUTER', 'FULL',
            'ON', 'AS', 'AND', 'OR', 'NOT', 'IN', 'EXISTS', 'BETWEEN', 'LIKE', 'IS', 'NULL',
            'ORDER', 'BY', 'GROUP', 'HAVING', 'DISTINCT', 'TOP', 'LIMIT', 'OFFSET',
            'COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'CAST', 'CONVERT', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END',
            'OVER', 'PARTITION', 'ROW_NUMBER', 'RANK', 'DENSE_RANK',
            'INT', 'INTEGER', 'VARCHAR', 'CHAR', 'TEXT', 'FLOAT', 'DECIMAL', 'NUMERIC', 'DATE', 'DATETIME', 'TIME',
            'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'ALTER', 'DROP', 'TRUNCATE',
            'UNION', 'INTERSECT', 'EXCEPT', 'ALL', 'ANY', 'SOME'
        }

        # Parse columns based on statement type
        if statement_type == 'SELECT' and 'SELECT' in segments:
            columns.extend(self._parse_select_clause_columns(segments['SELECT'], table_aliases, sql_keywords))

        elif statement_type == 'INSERT' and 'COLUMNS' in segments:
            columns.extend(self._parse_insert_columns(segments['COLUMNS'], sql_keywords))

        elif statement_type == 'UPDATE' and 'SET' in segments:
            columns.extend(self._parse_set_clause_columns(segments['SET'], table_aliases, sql_keywords))

        # Parse WHERE clause columns for all statement types
        if 'WHERE' in segments:
            columns.extend(self._parse_where_clause_columns(segments['WHERE'], table_aliases, sql_keywords))

        # Parse JOIN ON clause columns from FROM segment
        if 'FROM' in segments:
            columns.extend(self._parse_join_on_clause_columns(segments['FROM'], table_aliases, sql_keywords))

        return columns

    def _parse_select_clause_columns(self, select_clause: str, table_aliases: Dict[str, str], sql_keywords: Set[str]) -> List[str]:
        """Parse SELECT clause for column names."""
        if select_clause.strip() == '*':
            return ['*']

        columns = []

        # Split by commas, respecting parentheses for function calls
        column_parts = self._split_respecting_parentheses(select_clause, ',')

        for part in column_parts:
            part = part.strip()

            # Check if it's a function call
            if self._is_function_call(part):
                # Extract column references from inside function calls
                function_columns = self._extract_columns_from_functions(part, table_aliases, sql_keywords)
                columns.extend(function_columns)
                continue

            # Remove AS aliases
            part = re.sub(r'\s+AS\s+[a-zA-Z_][a-zA-Z0-9_]*$', '', part, flags=re.IGNORECASE).strip()

            # Extract column references
            if '.' in part:
                # Qualified column reference (table.column or alias.column)
                qualified_refs = re.findall(r'([a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*)', part)
                for ref in qualified_refs:
                    if '.' in ref:
                        table_or_alias, column = ref.split('.', 1)
                        if self._is_valid_column_name(column, table_aliases, sql_keywords):
                            columns.append(column)
            else:
                # Unqualified column reference
                col_matches = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b', part)
                for col in col_matches:
                    if self._is_valid_column_name(col, table_aliases, sql_keywords):
                        columns.append(col)

        return columns

    def _parse_insert_columns(self, columns_clause: str, sql_keywords: Set[str]) -> List[str]:
        """Parse INSERT column list."""
        columns = []
        column_parts = [part.strip() for part in columns_clause.split(',')]

        for part in column_parts:
            # Simple column name validation
            if re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', part) and part.upper() not in sql_keywords:
                columns.append(part)

        return columns

    def _parse_set_clause_columns(self, set_clause: str, table_aliases: Dict[str, str], sql_keywords: Set[str]) -> List[str]:
        """Parse UPDATE SET clause for column names."""
        columns = []

        # Split by commas, respecting parentheses
        set_parts = self._split_respecting_parentheses(set_clause, ',')

        for part in set_parts:
            # Look for column = value patterns
            eq_match = re.search(r'^([a-zA-Z_][a-zA-Z0-9_.]*)\s*=', part.strip())
            if eq_match:
                col_ref = eq_match.group(1)
                if '.' in col_ref:
                    # Qualified reference
                    table_or_alias, column = col_ref.split('.', 1)
                    if self._is_valid_column_name(column, table_aliases, sql_keywords):
                        columns.append(column)
                else:
                    # Unqualified reference
                    if self._is_valid_column_name(col_ref, table_aliases, sql_keywords):
                        columns.append(col_ref)

        return columns

    def _parse_where_clause_columns(self, where_clause: str, table_aliases: Dict[str, str], sql_keywords: Set[str]) -> List[str]:
        """Parse WHERE clause for column references."""
        columns = []

        # Enhanced patterns to catch more WHERE clause variations
        col_patterns = [
            # Comparison operators
            r'\b([a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*)\s*[=<>!]+',  # qualified: table.column =
            r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*[=<>!]+',  # unqualified: column =

            # LIKE, IN, BETWEEN operators
            r'\b([a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*)\s+(?:LIKE|IN|BETWEEN)',  # qualified with LIKE/IN/BETWEEN
            r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s+(?:LIKE|IN|BETWEEN)',  # unqualified with LIKE/IN/BETWEEN

            # IS NULL/NOT NULL
            r'\b([a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*)\s+IS\s+(?:NULL|NOT)',  # qualified IS NULL
            r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s+IS\s+(?:NULL|NOT)',  # unqualified IS NULL

            # Simple column references in conditions (before AND/OR)
            r'\b([a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*)\b',  # any qualified reference
            r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b(?=\s*[=<>!])',  # unqualified before operator
        ]

        for pattern in col_patterns:
            matches = re.findall(pattern, where_clause, re.IGNORECASE)
            for match in matches:
                if '.' in match:
                    # Qualified reference
                    table_or_alias, column = match.split('.', 1)
                    if self._is_valid_column_name(column, table_aliases, sql_keywords):
                        columns.append(column)
                else:
                    # Unqualified reference
                    if self._is_valid_column_name(match, table_aliases, sql_keywords):
                        columns.append(match)

        return columns

    def _is_valid_column_name(self, name: str, table_aliases: Dict[str, str], sql_keywords: Set[str]) -> bool:
        """Check if name is a valid column (not keyword, alias, or number)."""
        return (name.upper() not in sql_keywords and
                name not in table_aliases and
                not name.isdigit() and
                re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', name))

    def _is_function_call(self, text: str) -> bool:
        """Check if text contains SQL function calls."""
        function_patterns = [
            r'\bCOUNT\s*\(', r'\bSUM\s*\(', r'\bAVG\s*\(', r'\bMIN\s*\(', r'\bMAX\s*\(',
            r'\bCAST\s*\(', r'\bCONVERT\s*\(', r'\bCASE\s+'
        ]

        text_upper = text.upper()
        return any(re.search(pattern, text_upper) for pattern in function_patterns)

    def _split_respecting_parentheses(self, text: str, delimiter: str) -> List[str]:
        """Split text by delimiter while respecting parentheses depth."""
        parts = []
        current_part = ""
        paren_depth = 0

        for char in text:
            if char == '(':
                paren_depth += 1
            elif char == ')':
                paren_depth -= 1
            elif char == delimiter and paren_depth == 0:
                parts.append(current_part.strip())
                current_part = ""
                continue
            current_part += char

        if current_part.strip():
            parts.append(current_part.strip())

        return parts

    def _parse_join_on_clause_columns(self, from_clause: str, table_aliases: Dict[str, str], sql_keywords: Set[str]) -> List[str]:
        """Parse JOIN ON clauses for column references."""
        columns = []

        # Find all ON clauses in the FROM segment using simpler pattern
        on_clauses = re.findall(r'\bON\s+(.+?)(?=\s*$)', from_clause, re.IGNORECASE)

        # Also try to find ON clauses that might be followed by other keywords
        if not on_clauses:
            on_clauses = re.findall(r'\bON\s+([^)]+?)(?=\s+(?:LEFT|RIGHT|INNER|FULL|CROSS|JOIN|WHERE|GROUP|ORDER|LIMIT|$))', from_clause, re.IGNORECASE)

        for on_clause in on_clauses:
            # Parse each ON clause like a WHERE clause
            on_columns = self._parse_where_clause_columns(on_clause.strip(), table_aliases, sql_keywords)
            columns.extend(on_columns)

        return columns

    def _extract_columns_from_functions(self, function_text: str, table_aliases: Dict[str, str], sql_keywords: Set[str]) -> List[str]:
        """Extract column references from inside function calls."""
        columns = []

        # Extract content inside parentheses of function calls
        # Handle nested parentheses properly
        paren_content = []
        paren_depth = 0
        current_content = ""
        in_function = False

        for char in function_text:
            if char == '(':
                paren_depth += 1
                in_function = True
            elif char == ')':
                paren_depth -= 1
                if paren_depth == 0 and in_function:
                    paren_content.append(current_content.strip())
                    current_content = ""
                    in_function = False
            elif in_function and paren_depth > 0:
                current_content += char

        # Parse each function's content for column references
        for content in paren_content:
            if not content or content == '*':
                continue

            # Look for column references in function parameters
            if '.' in content:
                # Qualified references: table.column or alias.column
                qualified_refs = re.findall(r'([a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z_][a-zA-Z0-9_]*)', content)
                for ref in qualified_refs:
                    if '.' in ref:
                        table_or_alias, column = ref.split('.', 1)
                        if self._is_valid_column_name(column, table_aliases, sql_keywords):
                            columns.append(column)
            else:
                # Unqualified references
                col_matches = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\b', content)
                for col in col_matches:
                    if self._is_valid_column_name(col, table_aliases, sql_keywords):
                        columns.append(col)

        return columns

    def convert_to_test_query(self, sql: str) -> str:
        """Convert any SQL statement to a safe SELECT query for testing."""
        sql_clean = sql.strip()
        sql_upper = sql_clean.upper()

        # If it's already a SELECT, convert to parameter-free test query
        if sql_upper.startswith('SELECT'):
            # Extract table names for a simple existence test
            from_match = re.search(r'\bFROM\s+([A-Za-z_][A-Za-z0-9_]*)', sql_clean, re.IGNORECASE)
            if from_match:
                table_name = from_match.group(1)
                return f"SELECT TOP 0 * FROM {table_name}"

            # Fallback: try to preserve structure but remove parameters
            # Process comparison operators first (more specific patterns)
            test_sql = re.sub(r'\s*>=\s*\?', ' >= 0', sql_clean, flags=re.IGNORECASE)
            test_sql = re.sub(r'\s*<=\s*\?', ' <= 999999', test_sql, flags=re.IGNORECASE)
            test_sql = re.sub(r'\s*>\s*\?', ' > 0', test_sql, flags=re.IGNORECASE)
            test_sql = re.sub(r'\s*<\s*\?', ' < 999999', test_sql, flags=re.IGNORECASE)
            test_sql = re.sub(r'\s*=\s*\?', ' IS NOT NULL', test_sql)
            test_sql = re.sub(r'\s*LIKE\s*\?', " LIKE '%'", test_sql, flags=re.IGNORECASE)

            # Remove ORDER BY from subqueries for SQL Server compatibility
            test_sql = re.sub(r'\s+ORDER\s+BY\s+[^)]+(?=\s*$)', '', test_sql, flags=re.IGNORECASE)

            return f"SELECT TOP 1 * FROM ({test_sql}) AS test_query"

        # Convert INSERT to SELECT
        elif sql_upper.startswith('INSERT'):
            insert_match = re.match(r'INSERT\s+INTO\s+([A-Za-z_][A-Za-z0-9_]*)', sql_clean, re.IGNORECASE)
            if insert_match:
                table_name = insert_match.group(1)
                return f"SELECT TOP 0 * FROM {table_name}"  # Returns no rows but tests table existence

        # Convert UPDATE to SELECT
        elif sql_upper.startswith('UPDATE'):
            update_match = re.match(r'UPDATE\s+([A-Za-z_][A-Za-z0-9_]*)', sql_clean, re.IGNORECASE)
            if update_match:
                table_name = update_match.group(1)
                return f"SELECT TOP 0 * FROM {table_name}"  # Parameter-free table existence test

        # Convert DELETE to SELECT
        elif sql_upper.startswith('DELETE'):
            delete_match = re.match(r'DELETE\s+FROM\s+([A-Za-z_][A-Za-z0-9_]*)', sql_clean, re.IGNORECASE)
            if delete_match:
                table_name = delete_match.group(1)
                return f"SELECT TOP 0 * FROM {table_name}"  # Parameter-free table existence test
        
        # For other statements, try to extract table and create a simple SELECT
        else:
            components = self.extract_sql_components(sql_clean)
            if components['tables']:
                table_name = components['tables'][0]  # Use first table found
                return f"SELECT TOP 0 * FROM {table_name}"
        
        # If we can't convert it, return a comment
        return f"-- Could not convert to test query: {sql_clean}"
    
    async def validate_sql(self, original_sql: str, file_path: str = "", line_number: int = 0) -> SQLValidationResult:
        """Validate SQL by converting to test query and executing."""
        try:
            # Convert to test query
            test_sql = self.convert_to_test_query(original_sql)
            
            # Skip if we couldn't convert
            if test_sql.startswith('--'):
                return SQLValidationResult(
                    original_sql=original_sql,
                    test_sql=test_sql,
                    success=False,
                    error_message="Could not convert to testable query"
                )
            
            # Execute the test query
            result = await self.db_tool.execute_query(test_sql)
            
            if result.get('status') == 'success':
                # Extract components for reporting
                components = self.extract_sql_components(original_sql)
                
                validation_result = SQLValidationResult(
                    original_sql=original_sql,
                    test_sql=test_sql,
                    success=True,
                    tables_found=components['tables'],
                    columns_found=components['columns']
                )
                
                # Log success
                if self.logger:
                    self.logger.log_sql_test(
                        file_path, line_number, original_sql, test_sql,
                        f"SUCCESS - Tables: {', '.join(components['tables'])}, Columns: {', '.join(components['columns'])}"
                    )
                
                return validation_result
            else:
                error_msg = result.get('error', 'Unknown database error')
                
                validation_result = SQLValidationResult(
                    original_sql=original_sql,
                    test_sql=test_sql,
                    success=False,
                    error_message=error_msg
                )
                
                # Log failure
                if self.logger:
                    self.logger.log_sql_test(
                        file_path, line_number, original_sql, test_sql,
                        f"FAILED - {error_msg}", "failed"
                    )
                
                return validation_result
                
        except Exception as e:
            error_msg = f"Validation error: {str(e)}"
            
            validation_result = SQLValidationResult(
                original_sql=original_sql,
                test_sql="",
                success=False,
                error_message=error_msg
            )
            
            # Log exception
            if self.logger:
                self.logger.log_sql_test(
                    file_path, line_number, original_sql, "",
                    f"EXCEPTION - {error_msg}", "failed"
                )
            
            return validation_result
    
    async def validate_file_sql(self, file_path: Path) -> List[SQLValidationResult]:
        """Validate all SQL statements found in a file."""
        results = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                line_clean = line.strip()
                
                # Skip comments and empty lines
                if not line_clean or line_clean.startswith('#') or line_clean.startswith('//'):
                    continue
                
                # Look for SQL statements
                sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'FROM', 'JOIN']
                if any(keyword in line_clean.upper() for keyword in sql_keywords):
                    # Extract potential SQL statement
                    # This is a simplified approach - could be enhanced for multi-line SQL
                    if any(line_clean.upper().startswith(kw) for kw in ['SELECT', 'INSERT', 'UPDATE', 'DELETE']):
                        result = await self.validate_sql(line_clean, str(file_path), line_num)
                        results.append(result)
                    elif 'FROM ' in line_clean.upper() or 'JOIN ' in line_clean.upper():
                        # Try to construct a simple SELECT for table validation
                        tables = self.extract_sql_components(line_clean)['tables']
                        if tables:
                            test_query = f"SELECT 1 FROM {tables[0]} WHERE 1=0"
                            result = await self.validate_sql(test_query, str(file_path), line_num)
                            results.append(result)
        
        except Exception as e:
            print(f"Error processing file {file_path}: {e}")
        
        return results
    
    async def validate_codebase(self, directories: List[str] = None, 
                               extensions: List[str] = None) -> Dict[str, List[SQLValidationResult]]:
        """Validate SQL in entire codebase."""
        if directories is None:
            directories = ['Self_Healer', 'Scripts', 'tests']
        
        if extensions is None:
            extensions = ['.py', '.sql']
        
        project_root = Path(__file__).parent.parent
        all_results = {}
        
        print(f"🔍 SQL Validation Starting...")
        print(f"Directories: {', '.join(directories)}")
        print(f"Extensions: {', '.join(extensions)}")
        
        for directory in directories:
            dir_path = project_root / directory
            if not dir_path.exists():
                continue
            
            print(f"\n📁 Validating SQL in: {directory}")
            
            for file_path in dir_path.rglob('*'):
                if file_path.is_file() and file_path.suffix in extensions:
                    file_results = await self.validate_file_sql(file_path)
                    
                    if file_results:
                        relative_path = str(file_path.relative_to(project_root))
                        all_results[relative_path] = file_results
                        
                        success_count = len([r for r in file_results if r.success])
                        failed_count = len([r for r in file_results if not r.success])
                        
                        print(f"   📄 {relative_path}: {success_count} passed, {failed_count} failed")
        
        return all_results

async def main():
    """Main execution function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Validate SQL statements in codebase')
    parser.add_argument('--database', default='knowledgebase',
                       help='Database connection name')
    parser.add_argument('--directories', nargs='+',
                       default=['Self_Healer', 'Scripts', 'tests'],
                       help='Directories to scan')
    parser.add_argument('--extensions', nargs='+',
                       default=['.py', '.sql'],
                       help='File extensions to process')
    
    args = parser.parse_args()
    
    # Initialize validator and logger
    validator = SQLValidator(args.database)
    logger = DatabaseRepairLogger()
    validator.set_logger(logger)
    
    # Run validation
    results = await validator.validate_codebase(args.directories, args.extensions)
    
    # Generate summary
    total_tests = sum(len(file_results) for file_results in results.values())
    total_passed = sum(len([r for r in file_results if r.success]) for file_results in results.values())
    total_failed = total_tests - total_passed
    
    print(f"\n📊 SQL VALIDATION SUMMARY:")
    print(f"   Files processed: {len(results)}")
    print(f"   Total SQL tests: {total_tests}")
    print(f"   Passed: {total_passed}")
    print(f"   Failed: {total_failed}")
    print(f"   Success rate: {(total_passed/total_tests*100):.1f}%" if total_tests > 0 else "   No tests found")
    
    # Print detailed results for failures
    if total_failed > 0:
        print(f"\n❌ FAILED SQL VALIDATIONS:")
        for file_path, file_results in results.items():
            failed_results = [r for r in file_results if not r.success]
            if failed_results:
                print(f"\n📄 {file_path}:")
                for result in failed_results:
                    print(f"   SQL: {result.original_sql[:60]}...")
                    print(f"   Error: {result.error_message}")
    
    # Close logger and show summary
    logger.print_summary()
    logger.close_session()
    
    return 0 if total_failed == 0 else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
