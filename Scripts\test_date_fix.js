// Test the date fix for N8N workflow
// This script demonstrates the difference between UTC and local timezone dates

console.log('=== N8N Workflow Date Fix Test ===');
console.log('');

// Current date/time information
const now = new Date();
console.log('Current Date/Time Information:');
console.log('  Local time:', now.toLocaleString());
console.log('  UTC time:', now.toUTCString());
console.log('  ISO string:', now.toISOString());
console.log('');

// OLD METHOD (problematic)
const oldMethod = now.toISOString().split('T')[0];
console.log('OLD METHOD (UTC-based):');
console.log('  new Date().toISOString().split("T")[0]');
console.log('  Result:', oldMethod);
console.log('  Problem: Uses UTC date, may be "tomorrow" in local timezone');
console.log('');

// NEW METHOD (fixed)
const newMethod = now.toLocaleDateString('en-CA');
console.log('NEW METHOD (Local timezone-based):');
console.log('  new Date().toLocaleDateString("en-CA")');
console.log('  Result:', newMethod);
console.log('  Benefit: Uses local timezone date, matches user expectation');
console.log('');

// Comparison
console.log('COMPARISON:');
console.log('  UTC date:', oldMethod);
console.log('  Local date:', newMethod);
if (oldMethod !== newMethod) {
    console.log('  ⚠️  DATES DIFFER! This explains the filename mismatch.');
    console.log('  The workflow was creating files with UTC dates.');
} else {
    console.log('  ✅ Dates match (timezone difference < 24 hours)');
}
console.log('');

// Show timezone offset
const timezoneOffset = now.getTimezoneOffset();
const offsetHours = Math.abs(timezoneOffset / 60);
const offsetDirection = timezoneOffset > 0 ? 'behind' : 'ahead of';
console.log('TIMEZONE INFORMATION:');
console.log(`  Local timezone is ${offsetHours} hours ${offsetDirection} UTC`);
console.log(`  Timezone offset: ${timezoneOffset} minutes`);
console.log('');

// Test edge cases
console.log('EDGE CASE TESTING:');

// Test late evening (when UTC might roll over to next day)
const lateEvening = new Date();
lateEvening.setHours(23, 30, 0, 0); // 11:30 PM local time

const lateEveningUTC = lateEvening.toISOString().split('T')[0];
const lateEveningLocal = lateEvening.toLocaleDateString('en-CA');

console.log('  Late evening test (11:30 PM local):');
console.log('    UTC date:', lateEveningUTC);
console.log('    Local date:', lateEveningLocal);
if (lateEveningUTC !== lateEveningLocal) {
    console.log('    ⚠️  UTC date differs from local date at late evening');
} else {
    console.log('    ✅ Dates still match at late evening');
}
console.log('');

// Test early morning (when local might be behind UTC)
const earlyMorning = new Date();
earlyMorning.setHours(2, 30, 0, 0); // 2:30 AM local time

const earlyMorningUTC = earlyMorning.toISOString().split('T')[0];
const earlyMorningLocal = earlyMorning.toLocaleDateString('en-CA');

console.log('  Early morning test (2:30 AM local):');
console.log('    UTC date:', earlyMorningUTC);
console.log('    Local date:', earlyMorningLocal);
if (earlyMorningUTC !== earlyMorningLocal) {
    console.log('    ⚠️  UTC date differs from local date at early morning');
} else {
    console.log('    ✅ Dates still match at early morning');
}
console.log('');

console.log('=== CONCLUSION ===');
console.log('The N8N workflow has been updated to use:');
console.log('  new Date().toLocaleDateString("en-CA")');
console.log('');
console.log('This ensures filenames always match the local date,');
console.log('regardless of timezone differences between Docker and host.');
console.log('');
console.log('Expected filename format: dns_security_report_YYYY-MM-DD.html');
console.log(`Current expected filename: dns_security_report_${newMethod}.html`);
