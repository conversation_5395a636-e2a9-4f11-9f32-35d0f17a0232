@echo off
echo DNS Docker Mount Test
echo =====================

echo.
echo 1. Checking if N8N container is running...
docker ps --filter "name=n8n-main" --format "table {{.Names}}\t{{.Status}}"
if %errorlevel% neq 0 (
    echo ERROR: Docker not available or N8N not running
    goto :end
)

echo.
echo 2. Checking Windows file...
if exist "data\dns_reports\setup\dns_cache_output.txt" (
    echo ✓ Windows file exists
    dir "data\dns_reports\setup\dns_cache_output.txt"
) else (
    echo ✗ Windows file missing
    goto :end
)

echo.
echo 3. Testing Docker file access...
docker exec n8n-main test -f /home/<USER>/shared/dns_reports/setup/dns_cache_output.txt
if %errorlevel% equ 0 (
    echo ✓ Docker can see the file
    echo File stats from Docker:
    docker exec n8n-main stat -c "Size: %%s bytes, Modified: %%Y (unix timestamp)" /home/<USER>/shared/dns_reports/setup/dns_cache_output.txt
) else (
    echo ✗ Docker cannot see the file - volume mount issue!
)

echo.
echo 4. Testing N8N workflow command...
docker exec n8n-main sh -c "stat -c '%%Y' /home/<USER>/shared/dns_reports/setup/dns_cache_output.txt 2>/dev/null || echo 'FILE_NOT_FOUND'"

:end
echo.
echo Test complete. If Docker can't see the file, restart N8N Docker containers.
pause
