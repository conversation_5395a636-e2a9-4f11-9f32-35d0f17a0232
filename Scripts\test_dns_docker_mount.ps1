# Test DNS Docker Mount - Verify file visibility and timestamps
# This script tests if <PERSON><PERSON> can see the DNS cache file correctly

Write-Host "DNS Docker Mount Test" -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan

# Check if Dock<PERSON> is running
Write-Host "`n1. Checking Docker status..." -ForegroundColor Yellow
try {
    $dockerStatus = docker ps --format "table {{.Names}}\t{{.Status}}" | Select-String "n8n"
    if ($dockerStatus) {
        Write-Host "✅ N8N Docker container is running" -ForegroundColor Green
        Write-Host "   $dockerStatus" -ForegroundColor White
    } else {
        Write-Host "❌ N8N Docker container not found" -ForegroundColor Red
        Write-Host "   Start N8N first: .\n8n-docker\Start-N8N-Stable.ps1" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "❌ Docker not available: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Check Windows file
Write-Host "`n2. Checking Windows file..." -ForegroundColor Yellow
$windowsFile = "data\dns_reports\setup\dns_cache_output.txt"
if (Test-Path $windowsFile) {
    $fileInfo = Get-Item $windowsFile
    Write-Host "✅ Windows file exists" -ForegroundColor Green
    Write-Host "   Path: $($fileInfo.FullName)" -ForegroundColor White
    Write-Host "   Size: $($fileInfo.Length) bytes" -ForegroundColor White
    Write-Host "   Modified: $($fileInfo.LastWriteTime)" -ForegroundColor White
    Write-Host "   UTC: $($fileInfo.LastWriteTime.ToUniversalTime())" -ForegroundColor White
    
    # Calculate Unix timestamp
    $unixTime = [int64](($fileInfo.LastWriteTime.ToUniversalTime() - (Get-Date '1970-01-01')).TotalSeconds)
    Write-Host "   Unix Timestamp: $unixTime" -ForegroundColor White
} else {
    Write-Host "❌ Windows file not found: $windowsFile" -ForegroundColor Red
    exit 1
}

# Check Docker container file
Write-Host "`n3. Checking Docker container file..." -ForegroundColor Yellow
$dockerPath = "/home/<USER>/shared/dns_reports/setup/dns_cache_output.txt"
try {
    # Check if file exists in container
    $dockerFileCheck = docker exec n8n-main test -f $dockerPath
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker file exists" -ForegroundColor Green
        
        # Get file stats from container
        $dockerStat = docker exec n8n-main stat -c '%Y %s' $dockerPath
        $statParts = $dockerStat.Split(' ')
        $dockerUnixTime = [int64]$statParts[0]
        $dockerSize = [int64]$statParts[1]
        
        Write-Host "   Path: $dockerPath" -ForegroundColor White
        Write-Host "   Size: $dockerSize bytes" -ForegroundColor White
        Write-Host "   Unix Timestamp: $dockerUnixTime" -ForegroundColor White
        Write-Host "   UTC Time: $((Get-Date '1970-01-01').AddSeconds($dockerUnixTime).ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor White
        
        # Compare timestamps
        Write-Host "`n4. Timestamp Comparison..." -ForegroundColor Yellow
        $timeDiff = [math]::Abs($unixTime - $dockerUnixTime)
        Write-Host "   Windows Unix Time: $unixTime" -ForegroundColor White
        Write-Host "   Docker Unix Time:  $dockerUnixTime" -ForegroundColor White
        Write-Host "   Difference: $timeDiff seconds" -ForegroundColor White
        
        if ($timeDiff -le 2) {
            Write-Host "✅ Timestamps match (within 2 seconds)" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Timestamp mismatch detected!" -ForegroundColor Yellow
            Write-Host "   This could cause age validation issues" -ForegroundColor Yellow
        }
        
        # Check current age
        $currentUnixTime = [int64]((Get-Date).ToUniversalTime() - (Get-Date '1970-01-01')).TotalSeconds
        $fileAge = $currentUnixTime - $dockerUnixTime
        $ageMinutes = [math]::Floor($fileAge / 60)
        
        Write-Host "`n5. File Age Analysis..." -ForegroundColor Yellow
        Write-Host "   Current Unix Time: $currentUnixTime" -ForegroundColor White
        Write-Host "   File Age: $fileAge seconds ($ageMinutes minutes)" -ForegroundColor White
        
        if ($ageMinutes -le 65) {
            Write-Host "✅ File is fresh (≤65 minutes)" -ForegroundColor Green
        } else {
            Write-Host "❌ File is stale (>65 minutes)" -ForegroundColor Red
        }
        
    } else {
        Write-Host "❌ Docker file not found: $dockerPath" -ForegroundColor Red
        Write-Host "   Volume mount may not be working correctly" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Error checking Docker file: $($_.Exception.Message)" -ForegroundColor Red
}

# Test N8N workflow file check command
Write-Host "`n6. Testing N8N file check command..." -ForegroundColor Yellow
try {
    $n8nCommand = "stat -c '%Y' $dockerPath 2>/dev/null || echo 'FILE_NOT_FOUND'"
    $n8nResult = docker exec n8n-main sh -c $n8nCommand
    Write-Host "   Command: $n8nCommand" -ForegroundColor White
    Write-Host "   Result: $n8nResult" -ForegroundColor White
    
    if ($n8nResult -eq "FILE_NOT_FOUND") {
        Write-Host "❌ N8N workflow will see FILE_NOT_FOUND" -ForegroundColor Red
    } elseif ($n8nResult -match '^\d+$') {
        $n8nUnixTime = [int64]$n8nResult
        $n8nAge = $currentUnixTime - $n8nUnixTime
        $n8nAgeMinutes = [math]::Floor($n8nAge / 60)
        Write-Host "✅ N8N workflow will see timestamp: $n8nUnixTime" -ForegroundColor Green
        Write-Host "   N8N calculated age: $n8nAgeMinutes minutes" -ForegroundColor White
        
        if ($n8nAgeMinutes -le 65) {
            Write-Host "✅ N8N should accept this file as fresh" -ForegroundColor Green
        } else {
            Write-Host "❌ N8N will reject this file as stale" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️  Unexpected result format" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Error testing N8N command: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n📋 Summary:" -ForegroundColor Cyan
Write-Host "============" -ForegroundColor Cyan
Write-Host "This test helps identify Docker volume mount and timestamp issues." -ForegroundColor White
Write-Host "If timestamps don't match or N8N can't see the file, the DNS" -ForegroundColor White
Write-Host "monitoring will fail even though the Windows automation works." -ForegroundColor White
