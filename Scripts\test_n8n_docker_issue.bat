@echo off
echo N8N Docker DNS Issue Diagnostic
echo ================================

echo.
echo 1. Checking Docker containers...
docker ps --filter "name=n8n" --format "table {{.Names}}\t{{.Status}}"

echo.
echo 2. Checking Windows DNS cache file...
if exist "data\dns_reports\setup\dns_cache_output.txt" (
    echo ✓ Windows file exists
    echo File details:
    dir "data\dns_reports\setup\dns_cache_output.txt"
) else (
    echo ✗ Windows DNS cache file missing
    goto :end
)

echo.
echo 3. Testing Docker file access...
docker exec n8n-main test -f /home/<USER>/shared/dns_reports/setup/dns_cache_output.txt
if %errorlevel% equ 0 (
    echo ✓ Docker can see the DNS cache file
    echo.
    echo 4. Getting file timestamp from Docker...
    docker exec n8n-main stat -c "Unix timestamp: %%Y, Size: %%s bytes" /home/<USER>/shared/dns_reports/setup/dns_cache_output.txt
    
    echo.
    echo 5. Testing N8N workflow command...
    echo Command result:
    docker exec n8n-main sh -c "stat -c '%%Y' /home/<USER>/shared/dns_reports/setup/dns_cache_output.txt 2>/dev/null || echo 'FILE_NOT_FOUND'"
    
    echo.
    echo 6. Calculating file age...
    for /f %%i in ('docker exec n8n-main sh -c "date +%%s"') do set current_time=%%i
    for /f %%i in ('docker exec n8n-main stat -c "%%Y" /home/<USER>/shared/dns_reports/setup/dns_cache_output.txt') do set file_time=%%i
    set /a age_seconds=%current_time% - %file_time%
    set /a age_minutes=%age_seconds% / 60
    echo Current Unix time: %current_time%
    echo File Unix time: %file_time%
    echo File age: %age_minutes% minutes
    
    if %age_minutes% LEQ 65 (
        echo ✓ File is fresh (≤65 minutes)
        echo The N8N workflow should accept this file
    ) else (
        echo ✗ File is stale (>65 minutes)
        echo The N8N workflow will reject this file
    )
    
) else (
    echo ✗ Docker cannot see the DNS cache file
    echo This indicates a volume mount issue
    echo.
    echo Recommended actions:
    echo 1. Restart N8N Docker containers
    echo 2. Check volume mount configuration
    echo 3. Verify file permissions
)

:end
echo.
echo Diagnostic complete.
pause
