#!/usr/bin/env python3
"""
Simple test of database schema validation concept
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from n8n_builder.mcp_database_tool import MCPDatabaseTool

async def test_schema_validation():
    """Test the basic concept of schema validation."""
    print("🔍 Testing Database Schema Validation Concept")
    print("=" * 50)
    
    try:
        # Initialize database connection
        db_tool = MCPDatabaseTool('knowledgebase')
        
        # Test connection
        print("1. Testing database connection...")
        conn_result = await db_tool.test_connection()
        
        if not conn_result['connected']:
            print(f"❌ Connection failed: {conn_result['error']}")
            return False
            
        print(f"✅ Connected to database: {conn_result['database_name']}")
        
        # Get table names
        print("\n2. Loading table names...")
        tables_query = """
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
        """
        
        result = await db_tool.execute_query(tables_query)
        if not result.get('rows'):
            print("❌ No tables found")
            return False
            
        table_names = {row['TABLE_NAME'] for row in result['rows']}
        print(f"✅ Found {len(table_names)} tables:")
        for table in sorted(table_names):
            print(f"   - {table}")
        
        # Test a simple file scan
        print("\n3. Testing file scanning...")
        test_file = Path(__file__).parent / "validate_stored_procedures.py"
        
        if test_file.exists():
            print(f"📄 Scanning: {test_file.name}")
            
            with open(test_file, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    line_clean = line.strip()
                    
                    # Look for SQL keywords
                    if any(keyword in line_clean.upper() for keyword in ['FROM', 'JOIN', 'INSERT', 'UPDATE']):
                        # Simple regex to find potential table names
                        import re
                        
                        # Look for FROM clause
                        from_match = re.search(r'\bFROM\s+([A-Za-z_][A-Za-z0-9_]*)', line_clean, re.IGNORECASE)
                        if from_match:
                            table_ref = from_match.group(1)
                            if table_ref in table_names:
                                print(f"   ✅ Line {line_num}: Valid table '{table_ref}'")
                            else:
                                print(f"   ❌ Line {line_num}: Invalid table '{table_ref}' in: {line_clean[:60]}...")
                                
        print("\n🎉 Basic validation concept test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_schema_validation())
    if success:
        print("\n✅ The database schema validation concept is working!")
        print("   The full script should work once any issues are resolved.")
    else:
        print("\n❌ There are issues that need to be addressed.")
