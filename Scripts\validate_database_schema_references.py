#!/usr/bin/env python3
"""
Database Schema Reference Validator

This script validates that all table and column names referenced in SQL statements
throughout the codebase actually exist in the database schema. It helps catch
naming convention violations and typos before they cause runtime errors.

AUTHOR: N8N_Builder Team
DATE: 2025-08-18
PURPOSE: Automated validation of database schema references in code
USAGE: python Scripts/validate_database_schema_references.py [--include-fields]
"""

import asyncio
import re
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass
import json

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from n8n_builder.mcp_database_tool import MCPDatabaseTool

@dataclass
class SchemaReference:
    """Represents a reference to a database object in code."""
    file_path: str
    line_number: int
    line_content: str
    table_name: str
    column_name: Optional[str] = None
    reference_type: str = "table"  # "table" or "column"

@dataclass
class ValidationResult:
    """Results of schema validation."""
    valid_references: List[SchemaReference]
    invalid_references: List[SchemaReference]
    unknown_references: List[SchemaReference]  # Could be valid but not in our patterns
    
class DatabaseSchemaValidator:
    """Validates database schema references in code files."""
    
    def __init__(self, database_name: str = 'knowledgebase'):
        self.database_name = database_name
        self.db_tool = MCPDatabaseTool(database_name)
        
        # Database schema cache
        self.tables: Dict[str, Dict[str, str]] = {}  # table_name -> {column_name: data_type}
        self.table_names: Set[str] = set()
        self.all_column_names: Set[str] = set()
        
        # SQL patterns for finding table and column references
        self.sql_patterns = {
            'table_references': [
                # FROM clause
                re.compile(r'\bFROM\s+([A-Za-z_][A-Za-z0-9_]*)', re.IGNORECASE),
                # JOIN clauses
                re.compile(r'\bJOIN\s+([A-Za-z_][A-Za-z0-9_]*)', re.IGNORECASE),
                # INSERT INTO
                re.compile(r'\bINSERT\s+INTO\s+([A-Za-z_][A-Za-z0-9_]*)', re.IGNORECASE),
                # UPDATE
                re.compile(r'\bUPDATE\s+([A-Za-z_][A-Za-z0-9_]*)', re.IGNORECASE),
                # DELETE FROM
                re.compile(r'\bDELETE\s+FROM\s+([A-Za-z_][A-Za-z0-9_]*)', re.IGNORECASE),
                # Table aliases (more complex pattern)
                re.compile(r'\bFROM\s+([A-Za-z_][A-Za-z0-9_]*)\s+(?:AS\s+)?([A-Za-z_][A-Za-z0-9_]*)', re.IGNORECASE),
            ],
            'column_references': [
                # SELECT columns
                re.compile(r'\bSELECT\s+(.+?)\s+FROM', re.IGNORECASE | re.DOTALL),
                # WHERE conditions
                re.compile(r'\bWHERE\s+(.+?)(?:\s+ORDER\s+BY|\s+GROUP\s+BY|\s*$)', re.IGNORECASE | re.DOTALL),
                # ORDER BY
                re.compile(r'\bORDER\s+BY\s+(.+?)(?:\s+LIMIT|\s*$)', re.IGNORECASE | re.DOTALL),
                # GROUP BY
                re.compile(r'\bGROUP\s+BY\s+(.+?)(?:\s+HAVING|\s+ORDER\s+BY|\s*$)', re.IGNORECASE | re.DOTALL),
            ]
        }
        
        # File extensions to scan
        self.file_extensions = {'.py', '.sql', '.ps1'}
        
        # Directories to scan
        self.scan_directories = [
            'n8n_builder',
            'Self_Healer', 
            'Scripts',
            'tests',
            'data'
        ]
        
    async def load_database_schema(self) -> bool:
        """Load the complete database schema."""
        try:
            print("🔍 Loading database schema...")
            
            # Test connection
            conn_result = await self.db_tool.test_connection()
            if not conn_result['connected']:
                print(f"❌ Database connection failed: {conn_result['error']}")
                return False
                
            print(f"✅ Connected to database: {conn_result['database_name']}")
            
            # Get all tables
            tables_query = """
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
            """
            
            result = await self.db_tool.execute_query(tables_query)
            if not result.get('rows'):
                print("❌ No tables found in database")
                return False
                
            print(f"📋 Found {len(result['rows'])} tables")
            
            # Load schema for each table
            for row in result['rows']:
                table_name = row['TABLE_NAME']
                self.table_names.add(table_name)
                
                # Get columns for this table
                columns_query = f"""
                SELECT COLUMN_NAME, DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
                """
                
                col_result = await self.db_tool.execute_query(columns_query)
                if col_result.get('rows'):
                    table_columns = {}
                    for col_row in col_result['rows']:
                        col_name = col_row['COLUMN_NAME']
                        data_type = col_row['DATA_TYPE']
                        table_columns[col_name] = data_type
                        self.all_column_names.add(col_name)
                    
                    self.tables[table_name] = table_columns
                    print(f"   - {table_name}: {len(table_columns)} columns")
                    
            print(f"✅ Schema loaded: {len(self.tables)} tables, {len(self.all_column_names)} unique column names")
            return True
            
        except Exception as e:
            print(f"❌ Error loading database schema: {e}")
            return False
    
    def find_sql_in_file(self, file_path: Path) -> List[Tuple[int, str]]:
        """Find lines containing SQL statements in a file."""
        sql_lines = []
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    line_clean = line.strip()
                    
                    # Skip comments and empty lines
                    if not line_clean or line_clean.startswith('#') or line_clean.startswith('//'):
                        continue
                        
                    # Look for SQL keywords
                    sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'FROM', 'JOIN', 'WHERE']
                    if any(keyword in line_clean.upper() for keyword in sql_keywords):
                        sql_lines.append((line_num, line_clean))
                        
        except Exception as e:
            print(f"⚠️  Error reading file {file_path}: {e}")
            
        return sql_lines
    
    def extract_table_references(self, line: str) -> List[str]:
        """Extract table names from a SQL line."""
        table_names = []
        
        for pattern in self.sql_patterns['table_references']:
            matches = pattern.findall(line)
            for match in matches:
                if isinstance(match, tuple):
                    # Handle patterns that return tuples (like table aliases)
                    table_names.extend([m for m in match if m])
                else:
                    table_names.append(match)
                    
        # Clean up table names (remove quotes, etc.)
        cleaned_names = []
        for name in table_names:
            cleaned = name.strip('\'"[]`')
            if cleaned and not cleaned.lower() in ['as', 'on', 'where', 'and', 'or']:
                cleaned_names.append(cleaned)
                
        return list(set(cleaned_names))  # Remove duplicates
    
    def extract_column_references(self, line: str, include_fields: bool = False) -> List[str]:
        """Extract column names from a SQL line."""
        if not include_fields:
            return []
            
        column_names = []
        
        for pattern in self.sql_patterns['column_references']:
            matches = pattern.findall(line)
            for match in matches:
                # Parse column names from the match
                # This is a simplified approach - could be enhanced
                columns = re.findall(r'\b([A-Za-z_][A-Za-z0-9_]*)\b', match)
                column_names.extend(columns)
                
        # Filter out SQL keywords and common words
        sql_keywords = {
            'select', 'from', 'where', 'and', 'or', 'not', 'in', 'like', 
            'between', 'is', 'null', 'order', 'by', 'group', 'having',
            'count', 'sum', 'avg', 'max', 'min', 'distinct', 'top',
            'as', 'on', 'join', 'inner', 'left', 'right', 'outer'
        }
        
        cleaned_columns = []
        for col in column_names:
            if col.lower() not in sql_keywords and len(col) > 1:
                cleaned_columns.append(col)
                
        return list(set(cleaned_columns))
    
    async def validate_file(self, file_path: Path, include_fields: bool = False) -> List[SchemaReference]:
        """Validate database references in a single file."""
        references = []
        sql_lines = self.find_sql_in_file(file_path)
        
        for line_num, line_content in sql_lines:
            # Check table references
            table_names = self.extract_table_references(line_content)
            for table_name in table_names:
                ref = SchemaReference(
                    file_path=str(file_path),
                    line_number=line_num,
                    line_content=line_content,
                    table_name=table_name,
                    reference_type="table"
                )
                references.append(ref)
                
            # Check column references if requested
            if include_fields:
                column_names = self.extract_column_references(line_content, include_fields)
                for column_name in column_names:
                    ref = SchemaReference(
                        file_path=str(file_path),
                        line_number=line_num,
                        line_content=line_content,
                        table_name="",  # We don't know which table yet
                        column_name=column_name,
                        reference_type="column"
                    )
                    references.append(ref)
                    
        return references
    
    def categorize_references(self, references: List[SchemaReference]) -> ValidationResult:
        """Categorize references as valid, invalid, or unknown."""
        valid_refs = []
        invalid_refs = []
        unknown_refs = []
        
        for ref in references:
            if ref.reference_type == "table":
                if ref.table_name in self.table_names:
                    valid_refs.append(ref)
                elif self._looks_like_table_name(ref.table_name):
                    invalid_refs.append(ref)
                else:
                    unknown_refs.append(ref)
                    
            elif ref.reference_type == "column":
                if ref.column_name in self.all_column_names:
                    valid_refs.append(ref)
                elif self._looks_like_column_name(ref.column_name):
                    invalid_refs.append(ref)
                else:
                    unknown_refs.append(ref)
                    
        return ValidationResult(valid_refs, invalid_refs, unknown_refs)
    
    def _looks_like_table_name(self, name: str) -> bool:
        """Heuristic to determine if a name looks like it should be a table name."""
        # Check if it follows our naming conventions
        return (name.startswith(('REF_', 'XRF_', 'TBL_', 'LOG_')) or 
                name.endswith(('_Log', '_History', '_Temp')) or
                len(name) > 3)
    
    def _looks_like_column_name(self, name: str) -> bool:
        """Heuristic to determine if a name looks like it should be a column name."""
        # Basic heuristic - could be enhanced
        return (len(name) > 2 and 
                name[0].isupper() and 
                not name.isupper() and
                '_' not in name[:3])
    
    async def scan_codebase(self, include_fields: bool = False) -> ValidationResult:
        """Scan the entire codebase for database references."""
        print(f"🔍 Scanning codebase for database references...")
        print(f"   Include field validation: {include_fields}")
        
        all_references = []
        file_count = 0
        
        project_root = Path(__file__).parent.parent
        
        for directory in self.scan_directories:
            dir_path = project_root / directory
            if not dir_path.exists():
                continue
                
            print(f"📁 Scanning directory: {directory}")
            
            for file_path in dir_path.rglob('*'):
                if file_path.is_file() and file_path.suffix in self.file_extensions:
                    file_count += 1
                    refs = await self.validate_file(file_path, include_fields)
                    all_references.extend(refs)
                    
                    if refs:
                        print(f"   📄 {file_path.relative_to(project_root)}: {len(refs)} references")
        
        print(f"✅ Scanned {file_count} files, found {len(all_references)} database references")
        
        # Categorize all references
        return self.categorize_references(all_references)
    
    def generate_report(self, results: ValidationResult, output_file: str = None) -> str:
        """Generate a detailed validation report."""
        if output_file is None:
            output_file = f"data/database_schema_validation_report_{Path().cwd().name}.html"
            
        # Ensure output directory exists
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        html_content = self._generate_html_report(results)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
            
        return str(output_path)
    
    def _generate_html_report(self, results: ValidationResult) -> str:
        """Generate HTML report content."""
        total_refs = len(results.valid_references) + len(results.invalid_references) + len(results.unknown_references)
        
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Database Schema Validation Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .summary {{ display: flex; gap: 20px; margin: 20px 0; }}
        .metric {{ background-color: #e8f4f8; padding: 15px; border-radius: 5px; text-align: center; }}
        .metric.error {{ background-color: #ffe8e8; }}
        .metric.warning {{ background-color: #fff8e8; }}
        .metric.success {{ background-color: #e8f8e8; }}
        .section {{ margin: 30px 0; }}
        .reference {{ background-color: #f9f9f9; padding: 10px; margin: 5px 0; border-left: 4px solid #ccc; }}
        .reference.invalid {{ border-left-color: #ff4444; }}
        .reference.unknown {{ border-left-color: #ffaa44; }}
        .reference.valid {{ border-left-color: #44ff44; }}
        .file-path {{ font-weight: bold; color: #0066cc; }}
        .line-number {{ color: #666; font-size: 0.9em; }}
        .code {{ font-family: monospace; background-color: #f0f0f0; padding: 5px; margin: 5px 0; }}
        .table-name {{ font-weight: bold; color: #cc0066; }}
        .column-name {{ font-weight: bold; color: #0066cc; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Database Schema Validation Report</h1>
        <p>Generated: {Path().cwd().name} - {asyncio.get_event_loop().time()}</p>
        <p>Database: {self.database_name}</p>
        <p>Tables in schema: {len(self.table_names)}</p>
        <p>Columns in schema: {len(self.all_column_names)}</p>
    </div>
    
    <div class="summary">
        <div class="metric success">
            <h3>{len(results.valid_references)}</h3>
            <p>Valid References</p>
        </div>
        <div class="metric error">
            <h3>{len(results.invalid_references)}</h3>
            <p>Invalid References</p>
        </div>
        <div class="metric warning">
            <h3>{len(results.unknown_references)}</h3>
            <p>Unknown References</p>
        </div>
        <div class="metric">
            <h3>{total_refs}</h3>
            <p>Total References</p>
        </div>
    </div>
"""
        
        # Invalid references section
        if results.invalid_references:
            html += """
    <div class="section">
        <h2>❌ Invalid References (Require Attention)</h2>
        <p>These references appear to be database objects but don't exist in the schema:</p>
"""
            for ref in results.invalid_references:
                object_name = ref.table_name if ref.reference_type == "table" else ref.column_name
                html += f"""
        <div class="reference invalid">
            <div class="file-path">{ref.file_path}</div>
            <div class="line-number">Line {ref.line_number}</div>
            <div class="code">{ref.line_content}</div>
            <div>Invalid {ref.reference_type}: <span class="{'table-name' if ref.reference_type == 'table' else 'column-name'}">{object_name}</span></div>
        </div>
"""
            html += "    </div>"
        
        # Unknown references section  
        if results.unknown_references:
            html += """
    <div class="section">
        <h2>⚠️ Unknown References (Review Recommended)</h2>
        <p>These references might be valid but don't match our detection patterns:</p>
"""
            for ref in results.unknown_references:
                object_name = ref.table_name if ref.reference_type == "table" else ref.column_name
                html += f"""
        <div class="reference unknown">
            <div class="file-path">{ref.file_path}</div>
            <div class="line-number">Line {ref.line_number}</div>
            <div class="code">{ref.line_content}</div>
            <div>Unknown {ref.reference_type}: <span class="{'table-name' if ref.reference_type == 'table' else 'column-name'}">{object_name}</span></div>
        </div>
"""
            html += "    </div>"
        
        # Valid references section (summary only)
        if results.valid_references:
            html += f"""
    <div class="section">
        <h2>✅ Valid References ({len(results.valid_references)} found)</h2>
        <p>All valid references are working correctly. Expand below to see details if needed.</p>
        <details>
            <summary>Show valid references</summary>
"""
            for ref in results.valid_references[:50]:  # Limit to first 50 to avoid huge reports
                object_name = ref.table_name if ref.reference_type == "table" else ref.column_name
                html += f"""
            <div class="reference valid">
                <div class="file-path">{ref.file_path}</div>
                <div class="line-number">Line {ref.line_number}</div>
                <div>Valid {ref.reference_type}: <span class="{'table-name' if ref.reference_type == 'table' else 'column-name'}">{object_name}</span></div>
            </div>
"""
            if len(results.valid_references) > 50:
                html += f"<p>... and {len(results.valid_references) - 50} more valid references</p>"
            html += "        </details>\n    </div>"
        
        html += """
</body>
</html>
"""
        return html

async def main():
    """Main execution function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Validate database schema references in codebase')
    parser.add_argument('--include-fields', action='store_true', 
                       help='Also validate column/field names (experimental)')
    parser.add_argument('--database', default='knowledgebase',
                       help='Database connection name (default: knowledgebase)')
    parser.add_argument('--output', 
                       help='Output file path (default: auto-generated)')
    
    args = parser.parse_args()
    
    print("🔍 Database Schema Reference Validator")
    print("=" * 50)
    
    validator = DatabaseSchemaValidator(args.database)
    
    # Load database schema
    if not await validator.load_database_schema():
        print("❌ Failed to load database schema. Exiting.")
        return 1
    
    # Scan codebase
    results = await validator.scan_codebase(args.include_fields)
    
    # Generate report
    report_path = validator.generate_report(results, args.output)
    
    # Print summary
    total_refs = len(results.valid_references) + len(results.invalid_references) + len(results.unknown_references)
    print(f"\n📊 VALIDATION SUMMARY:")
    print(f"   Total references found: {total_refs}")
    print(f"   ✅ Valid references: {len(results.valid_references)}")
    print(f"   ❌ Invalid references: {len(results.invalid_references)}")
    print(f"   ⚠️  Unknown references: {len(results.unknown_references)}")
    print(f"\n📄 Detailed report saved to: {report_path}")
    
    if results.invalid_references:
        print(f"\n🚨 ATTENTION REQUIRED:")
        print(f"   Found {len(results.invalid_references)} invalid database references!")
        print(f"   Please review the report and fix these issues.")
        return 1
    else:
        print(f"\n🎉 SUCCESS: No invalid database references found!")
        return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
