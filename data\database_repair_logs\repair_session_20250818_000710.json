{"session_id": "20250818_000710", "start_time": "2025-08-18T00:07:10.645470", "entries": [{"timestamp": "2025-08-18T00:07:10.689206", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 109, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n               COUNT(e.ID) as EvidenceCount,\n               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage\n        FROM REF_Fact f\n        LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n        WHERE f.DataSource LIKE '%Self-Healer%'", "modified_content": "SELECT 1\n        FROM REF_Fact f\n        LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n        WHERE f.DataSource LIKE '%Self-Healer%'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, REF_Fact, Columns: FLOAT, CreateDate, DataSource, e, CAST, OverallAverage, AVG, Name, EvidenceCount, f, COUNT, ID, OVER, ValidityRating"}, {"timestamp": "2025-08-18T00:07:10.692827", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 109, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n               COUNT(e.ID) as EvidenceCount,\n               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage\n        FROM REF_Fact f\n        LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n        WHERE f.DataSource LIKE '%Self-Healer%'", "modified_content": "SELECT 1\n        FROM REF_Fact f\n        LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n        WHERE f.DataSource LIKE '%Self-Healer%'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, REF_Fact, Columns: FLOAT, CreateDate, DataSource, e, CAST, OverallAverage, AVG, Name, EvidenceCount, f, COUNT, ID, OVER, ValidityRating"}, {"timestamp": "2025-08-18T00:07:10.694329", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 194, "original_content": "INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)\n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Evidence WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, Columns: "}, {"timestamp": "2025-08-18T00:07:10.696333", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 194, "original_content": "INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)\n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Evidence WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, Columns: "}, {"timestamp": "2025-08-18T00:07:10.698333", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 206, "original_content": "SELECT ValidityRating FROM REF_Fact WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Fact WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.699835", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 218, "original_content": "SELECT COUNT(*) as EvidenceCount FROM REF_Evidence WHERE FactID = ?", "modified_content": "SELECT 1 FROM REF_Evidence WHERE FactID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.701838", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 226, "original_content": "UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Fact WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.703864", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 260, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n                   'fact' as KnowledgeType\n            FROM REF_Fact f\n            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)\n            AND f.DataSource LIKE '%Self-Healer%'\n            AND f.ValidityRating >= ?\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)\n            AND f.DataSource LIKE '%Self-Healer%'\n            AND f.ValidityRating >= ?\n            ORDER BY f.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.706315", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 260, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n                   'fact' as KnowledgeType\n            FROM REF_Fact f\n            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)\n            AND f.DataSource LIKE '%Self-Healer%'\n            AND f.ValidityRating >= ?\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)\n            AND f.DataSource LIKE '%Self-Healer%'\n            AND f.ValidityRating >= ?\n            ORDER BY f.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.710314", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 276, "original_content": "SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,\n                   'opinion' as KnowledgeType\n            FROM REF_Opinion o\n            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)\n            AND o.DataSource LIKE '%Self-Healer%'\n            AND o.ValidityRating >= ?\n            ORDER BY o.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Opinion o\n            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)\n            AND o.DataSource LIKE '%Self-Healer%'\n            AND o.ValidityRating >= ?\n            ORDER BY o.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.713314", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 276, "original_content": "SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,\n                   'opinion' as KnowledgeType\n            FROM REF_Opinion o\n            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)\n            AND o.DataSource LIKE '%Self-Healer%'\n            AND o.ValidityRating >= ?\n            ORDER BY o.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Opinion o\n            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)\n            AND o.DataSource LIKE '%Self-Healer%'\n            AND o.ValidityRating >= ?\n            ORDER BY o.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.715096", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 292, "original_content": "SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,\n                   f.ValidityRating, 'evidence' as KnowledgeType\n            FROM REF_Evidence e\n            JOIN REF_Fact f ON e.FactID = f.ID\n            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)\n            AND e.DataSource LIKE '%Self-Healer%'\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Evidence e\n            JOIN REF_Fact f ON e.FactID = f.ID\n            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)\n            AND e.DataSource LIKE '%Self-Healer%'\n            ORDER BY f.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.717095", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 292, "original_content": "SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,\n                   f.ValidityRating, 'evidence' as KnowledgeType\n            FROM REF_Evidence e\n            JOIN REF_Fact f ON e.FactID = f.ID\n            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)\n            AND e.DataSource LIKE '%Self-Healer%'\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Evidence e\n            JOIN REF_Fact f ON e.FactID = f.ID\n            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)\n            AND e.DataSource LIKE '%Self-Healer%'\n            ORDER BY f.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.720489", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 329, "original_content": "SELECT \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END as Category,\n            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness\n        FROM REF_Fact f\n        WHERE f.DataSource LIKE '%Self-Healer%'\n        GROUP BY \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END", "modified_content": "SELECT \n            1\n        FROM REF_Fact f\n        WHERE f.DataSource LIKE '%Self-Healer%'\n        GROUP BY \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: END, AVG, Name, Configuration, ELSE, CAST, THEN, f, JSON_Parsing, Database, LIKE, CASE, Other, Workflow, JSON, AvgEffectiveness, WHEN, Network, Category, FLOAT, ValidityRating"}, {"timestamp": "2025-08-18T00:07:10.722488", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 329, "original_content": "SELECT \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END as Category,\n            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness\n        FROM REF_Fact f\n        WHERE f.DataSource LIKE '%Self-Healer%'\n        GROUP BY \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END", "modified_content": "SELECT \n            1\n        FROM REF_Fact f\n        WHERE f.DataSource LIKE '%Self-Healer%'\n        GROUP BY \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: END, AVG, Name, Configuration, ELSE, CAST, THEN, f, JSON_Parsing, Database, LIKE, CASE, Other, Workflow, JSON, AvgEffectiveness, WHEN, Network, Category, FLOAT, ValidityRating"}, {"timestamp": "2025-08-18T00:07:10.732544", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 241, "original_content": "INSERT INTO REF_Fact (Name, ValidityRating, DataSource) \n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Fact WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: "}, {"timestamp": "2025-08-18T00:07:10.734777", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 241, "original_content": "INSERT INTO REF_Fact (Name, ValidityRating, DataSource) \n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Fact WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: "}, {"timestamp": "2025-08-18T00:07:10.736777", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 267, "original_content": "INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)\n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Evidence WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, Columns: "}, {"timestamp": "2025-08-18T00:07:10.738776", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 267, "original_content": "INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)\n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Evidence WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, Columns: "}, {"timestamp": "2025-08-18T00:07:10.772879", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 374, "original_content": "SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ?\n            GROUP BY f.ID, f.ValidityRating", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ?\n            GROUP BY f.ID, f.ValidityRating", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.803711", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 374, "original_content": "SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ?\n            GROUP BY f.ID, f.ValidityRating", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ?\n            GROUP BY f.ID, f.ValidityRating", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.848334", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 396, "original_content": "UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Fact WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.897904", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 410, "original_content": "SELECT ID FROM REF_Entity WHERE Name = ?", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.913101", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 419, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            FROM REF_Fact f\n            WHERE f.DataSource LIKE ?\n            ORDER BY f.CreateDate DESC", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            WHERE f.DataSource LIKE ?\n            ORDER BY f.CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.915980", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 419, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            FROM REF_Fact f\n            WHERE f.DataSource LIKE ?\n            ORDER BY f.CreateDate DESC", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            WHERE f.DataSource LIKE ?\n            ORDER BY f.CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.918979", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 430, "original_content": "SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate\n                FROM REF_Evidence e\n                WHERE e.FactID = ?", "modified_content": "SELECT 1\n                FROM REF_Evidence e\n                WHERE e.FactID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.921408", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 430, "original_content": "SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate\n                FROM REF_Evidence e\n                WHERE e.FactID = ?", "modified_content": "SELECT 1\n                FROM REF_Evidence e\n                WHERE e.FactID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.924413", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 439, "original_content": "SELECT a.Name as Attribute<PERSON><PERSON>, ev.EntityValue, eav.CreateDate\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?", "modified_content": "SELECT 1\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.926412", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 439, "original_content": "SELECT a.Name as Attribute<PERSON><PERSON>, ev.EntityValue, eav.CreateDate\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?", "modified_content": "SELECT 1\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.929413", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 465, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n                   COUNT(e.ID) as EvidenceCount\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            ORDER BY f.ValidityRating DESC, f.CreateDate DESC", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            ORDER BY f.ValidityRating DESC, f.CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.932413", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 465, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n                   COUNT(e.ID) as EvidenceCount\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            ORDER BY f.ValidityRating DESC, f.CreateDate DESC", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            ORDER BY f.ValidityRating DESC, f.CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.934415", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 477, "original_content": "SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate\n            FROM REF_Opinion o\n            WHERE o.Name LIKE ? OR o.Opinion LIKE ?\n            ORDER BY o.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Opinion o\n            WHERE o.Name LIKE ? OR o.Opinion LIKE ?\n            ORDER BY o.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.937459", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 477, "original_content": "SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate\n            FROM REF_Opinion o\n            WHERE o.Name LIKE ? OR o.Opinion LIKE ?\n            ORDER BY o.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Opinion o\n            WHERE o.Name LIKE ? OR o.Opinion LIKE ?\n            ORDER BY o.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.940394", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 552, "original_content": "SELECT ID FROM REF_Category WHERE Name = ?", "modified_content": "SELECT 1 FROM REF_Category WHERE Name = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.942393", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 559, "original_content": "INSERT INTO REF_Category (Name) OUTPUT INSERTED.ID VALUES (?)", "modified_content": "SELECT 1 FROM REF_Category WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Category, Columns: "}, {"timestamp": "2025-08-18T00:07:10.946394", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 566, "original_content": "SELECT ID FROM REF_Entity WHERE Name = ?", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.949393", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 573, "original_content": "INSERT INTO REF_Entity (Name) OUTPUT INSERTED.ID VALUES (?)", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T00:07:10.951393", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 580, "original_content": "SELECT ID FROM REF_Attribute WHERE Name = ?", "modified_content": "SELECT 1 FROM REF_Attribute WHERE Name = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.954461", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 587, "original_content": "INSERT INTO REF_Attribute (Name) OUTPUT INSERTED.ID VALUES (?)", "modified_content": "SELECT 1 FROM REF_Attribute WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Attribute, Columns: "}, {"timestamp": "2025-08-18T00:07:10.957092", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 594, "original_content": "SELECT ID FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?", "modified_content": "SELECT 1 FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.960091", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 602, "original_content": "INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)\n            OUTPUT INSERTED.ID\n            VALUES (?, ?, ?, ?)", "modified_content": "SELECT 1 FROM REF_EntityValue WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: "}, {"timestamp": "2025-08-18T00:07:10.963095", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 602, "original_content": "INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)\n            OUTPUT INSERTED.ID\n            VALUES (?, ?, ?, ?)", "modified_content": "SELECT 1 FROM REF_EntityValue WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: "}, {"timestamp": "2025-08-18T00:07:10.965095", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 610, "original_content": "INSERT INTO REF_EntityValue (Name, EntityValue) OUTPUT INSERTED.ID VALUES (?, ?)", "modified_content": "SELECT 1 FROM REF_EntityValue WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: "}, {"timestamp": "2025-08-18T00:07:10.968095", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 620, "original_content": "SELECT ID FROM XRF_EntityAttributeValue\n        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?", "modified_content": "SELECT 1 FROM XRF_EntityAttributeValue\n        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.970094", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 620, "original_content": "SELECT ID FROM XRF_EntityAttributeValue\n        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?", "modified_content": "SELECT 1 FROM XRF_EntityAttributeValue\n        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.974095", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 630, "original_content": "INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)\n        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)", "modified_content": "SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T00:07:10.977674", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 630, "original_content": "INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)\n        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)", "modified_content": "SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T00:07:10.985872", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 855, "original_content": "SELECT ID, Name, CreateDate\n            FROM REF_Entity\n            WHERE Name = ? OR Name LIKE ?", "modified_content": "SELECT 1\n            FROM REF_Entity\n            WHERE Name = ? OR Name LIKE ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.988872", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 855, "original_content": "SELECT ID, Name, CreateDate\n            FROM REF_Entity\n            WHERE Name = ? OR Name LIKE ?", "modified_content": "SELECT 1\n            FROM REF_Entity\n            WHERE Name = ? OR Name LIKE ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.992592", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 872, "original_content": "SELECT\n                a.Name as Attribute<PERSON><PERSON>,\n                ev.EntityValue as AttributeV<PERSON><PERSON>,\n                ev.NumericValue,\n                ev.ValueUnits,\n                ev.Name as ValueName,\n                eav.CreateDate\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?\n            ORDER BY a.Name", "modified_content": "SELECT\n                1\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?\n            ORDER BY a.Name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:10.995215", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 872, "original_content": "SELECT\n                a.Name as Attribute<PERSON><PERSON>,\n                ev.EntityValue as AttributeV<PERSON><PERSON>,\n                ev.NumericValue,\n                ev.ValueUnits,\n                ev.Name as ValueName,\n                eav.CreateDate\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?\n            ORDER BY a.Name", "modified_content": "SELECT\n                1\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?\n            ORDER BY a.Name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.004598", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 914, "original_content": "SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            FROM REF_Fact f\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            ORDER BY f.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.036864", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 914, "original_content": "SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            FROM REF_Fact f\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            ORDER BY f.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.084210", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 926, "original_content": "SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate\n            FROM REF_Evidence e\n            WHERE e.Name LIKE ? OR e.DataSource LIKE ?\n            ORDER BY e.CreateDate DESC", "modified_content": "SELECT 1\n            FROM REF_Evidence e\n            WHERE e.Name LIKE ? OR e.DataSource LIKE ?\n            ORDER BY e.CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.112213", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 926, "original_content": "SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate\n            FROM REF_Evidence e\n            WHERE e.Name LIKE ? OR e.DataSource LIKE ?\n            ORDER BY e.CreateDate DESC", "modified_content": "SELECT 1\n            FROM REF_Evidence e\n            WHERE e.Name LIKE ? OR e.DataSource LIKE ?\n            ORDER BY e.CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.129779", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 991, "original_content": "SELECT Name, ValidityRating, DataSource, CreateDate\n            FROM REF_Fact\n            WHERE Name LIKE ? OR DataSource LIKE ?\n            ORDER BY ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Fact\n            WHERE Name LIKE ? OR DataSource LIKE ?\n            ORDER BY ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.156310", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 991, "original_content": "SELECT Name, ValidityRating, DataSource, CreateDate\n            FROM REF_Fact\n            WHERE Name LIKE ? OR DataSource LIKE ?\n            ORDER BY ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Fact\n            WHERE Name LIKE ? OR DataSource LIKE ?\n            ORDER BY ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.160610", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 1009, "original_content": "SELECT Name, ValidityRating, Opinion, DataSource, CreateDate\n            FROM REF_Opinion\n            WHERE Name LIKE ? OR Opinion LIKE ?\n            ORDER BY ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Opinion\n            WHERE Name LIKE ? OR Opinion LIKE ?\n            ORDER BY ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.163571", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 1009, "original_content": "SELECT Name, ValidityRating, Opinion, DataSource, CreateDate\n            FROM REF_Opinion\n            WHERE Name LIKE ? OR Opinion LIKE ?\n            ORDER BY ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Opinion\n            WHERE Name LIKE ? OR Opinion LIKE ?\n            ORDER BY ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.177543", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 1021, "original_content": "SELECT DISTINCT e.Name, e.CreateDate\n            FROM REF_Entity e\n            WHERE e.Name LIKE 'Session_%'\n            AND EXISTS (\n                SELECT 1 FROM XRF_EntityAttributeValue eav\n                JOIN REF_Attribute a ON eav.AttributeID = a.ID\n                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n                WHERE eav.EntityID = e.ID\n                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)\n            )\n            ORDER BY e.CreateDate DESC", "modified_content": "SELECT 1\n            FROM REF_Entity e\n            WHERE e.Name LIKE 'Session_%'\n            AND EXISTS (\n                SELECT 1 FROM XRF_EntityAttributeValue eav\n                JOIN REF_Attribute a ON eav.AttributeID = a.ID\n                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n                WHERE eav.EntityID = e.ID\n                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)\n            )\n            ORDER BY e.CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.185118", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 1021, "original_content": "SELECT DISTINCT e.Name, e.CreateDate\n            FROM REF_Entity e\n            WHERE e.Name LIKE 'Session_%'\n            AND EXISTS (\n                SELECT 1 FROM XRF_EntityAttributeValue eav\n                JOIN REF_Attribute a ON eav.AttributeID = a.ID\n                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n                WHERE eav.EntityID = e.ID\n                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)\n            )\n            ORDER BY e.CreateDate DESC", "modified_content": "SELECT 1\n            FROM REF_Entity e\n            WHERE e.Name LIKE 'Session_%'\n            AND EXISTS (\n                SELECT 1 FROM XRF_EntityAttributeValue eav\n                JOIN REF_Attribute a ON eav.AttributeID = a.ID\n                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n                WHERE eav.EntityID = e.ID\n                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)\n            )\n            ORDER BY e.CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.195746", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 34, "original_content": "SELECT COLUMN_NAME, DATA_TYPE, IS_NULL<PERSON>LE, COLUMN_DEFAULT\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = REF_EntityValue\n        ORDER BY ORDINAL_POSITION", "modified_content": "SELECT 1\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = REF_EntityValue\n        ORDER BY ORDINAL_POSITION", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_EntityValue'. (207) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T00:07:11.199867", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 34, "original_content": "SELECT COLUMN_NAME, DATA_TYPE, IS_NULL<PERSON>LE, COLUMN_DEFAULT\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = REF_EntityValue\n        ORDER BY ORDINAL_POSITION", "modified_content": "SELECT 1\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = REF_EntityValue\n        ORDER BY ORDINAL_POSITION", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_EntityValue'. (207) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T00:07:11.210658", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 66, "original_content": "SELECT name, create_date, modify_date\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'\n        ORDER BY name", "modified_content": "SELECT 1\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'\n        ORDER BY name", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name, create_date, modify_date"}, {"timestamp": "2025-08-18T00:07:11.221038", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 66, "original_content": "SELECT name, create_date, modify_date\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'\n        ORDER BY name", "modified_content": "SELECT 1\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'\n        ORDER BY name", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name, create_date, modify_date"}, {"timestamp": "2025-08-18T00:07:11.225500", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 93, "original_content": "SELECT name FROM sys.procedures WHERE name = '{proc_name}'", "modified_content": "SELECT 1 FROM sys.procedures WHERE name = '{proc_name}'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name"}, {"timestamp": "2025-08-18T00:07:11.231212", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 106, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'\n        ORDER BY TABLE_NAME", "modified_content": "SELECT 1 \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'\n        ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME"}, {"timestamp": "2025-08-18T00:07:11.234216", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 106, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'\n        ORDER BY TABLE_NAME", "modified_content": "SELECT 1 \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'\n        ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME"}, {"timestamp": "2025-08-18T00:07:11.239260", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 119, "original_content": "SELECT COUNT(*) as row_count FROM {table_name}", "modified_content": "SELECT 1 FROM {table_name}", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.246259", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_schema.py", "line_number": 9, "original_content": "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME", "modified_content": "SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME"}, {"timestamp": "2025-08-18T00:07:11.249813", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_schema.py", "line_number": 17, "original_content": "SELECT TOP 5 * FROM REF_Entity WHERE Name LIKE 'Session_%'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name LIKE 'Session_%'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T00:07:11.256403", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_schema.py", "line_number": 26, "original_content": "SELECT TOP 10 * FROM REF_Entity ORDER BY CreateDate DESC", "modified_content": "SELECT 1 FROM REF_Entity ORDER BY CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T00:07:11.259404", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_schema.py", "line_number": 33, "original_content": "SELECT TOP 5 * FROM XRF_EntityAttributeValue", "modified_content": "SELECT 1 FROM XRF_EntityAttributeValue", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T00:07:11.271327", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\create_analytics_procedure.py", "line_number": 48, "original_content": "SELECT name, create_date \n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT 1 \n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name, create_date"}, {"timestamp": "2025-08-18T00:07:11.315900", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\create_analytics_procedure.py", "line_number": 48, "original_content": "SELECT name, create_date \n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT 1 \n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name, create_date"}, {"timestamp": "2025-08-18T00:07:11.337272", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\create_schema_procedure.sql", "line_number": 113, "original_content": "SELECT @count = COUNT(*) FROM", "modified_content": "SELECT @count = COUNT(*) FROM", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable \"@count\". (137) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.386250", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Fix table name: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.388695", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Fix table name: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.391158", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Fix table name: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.393963", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Fix table name: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.414639", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 117, "original_content": "SELECT * FROM \\1 ORDER BY", "modified_content": "SELECT 1 FROM \\1 ORDER BY", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '\\\\1'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T00:07:11.427291", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\check_database_tables.py", "line_number": 37, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' \n        AND TABLE_NAME LIKE 'REF_%' \n        ORDER BY TABLE_NAME", "modified_content": "SELECT 1 \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' \n        AND TABLE_NAME LIKE 'REF_%' \n        ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME"}, {"timestamp": "2025-08-18T00:07:11.431294", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\check_database_tables.py", "line_number": 37, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' \n        AND TABLE_NAME LIKE 'REF_%' \n        ORDER BY TABLE_NAME", "modified_content": "SELECT 1 \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' \n        AND TABLE_NAME LIKE 'REF_%' \n        ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME"}, {"timestamp": "2025-08-18T00:07:11.436298", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\check_database_tables.py", "line_number": 77, "original_content": "SELECT TOP 1 * FROM REF_Entity", "modified_content": "SELECT 1 FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T00:07:11.439297", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\check_database_tables.py", "line_number": 87, "original_content": "SELECT TOP 1 * FROM REF_Entity", "modified_content": "SELECT 1 FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T00:07:11.445298", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\database_repair_logger.py", "line_number": 0, "original_content": "Contains 2 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Fix table name: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.449298", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\database_repair_logger.py", "line_number": 268, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = 'test'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-18T00:07:11.453349", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\database_repair_logger.py", "line_number": 269, "original_content": "SELECT 1 FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = 'test'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-18T00:07:11.459991", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Fix table name: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.462991", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Attributes'", "modified_content": "Replaced with 'REF_Attribute'", "rule_applied": "Fix table name: REF_Attributes → REF_Attribute", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.464990", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_EntityValues'", "modified_content": "Replaced with 'REF_EntityValue'", "rule_applied": "Fix table name: REF_EntityValues → REF_EntityValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.468990", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'XRF_Entity_Attribute_Value'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Fix table name: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.470991", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'XRF_Entity_AttributeValue'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Fix table name: XRF_Entity_AttributeValue → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.474991", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Fix table name: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.476990", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Fix table name: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.479990", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Fix table name: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.482993", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Fix table name: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.573806", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 208, "original_content": "SELECT * FROM REF_Entity WHERE Name = \\'test\\'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = \\'test\\'", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\\\'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T00:07:11.580192", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 209, "original_content": "INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)", "modified_content": "SELECT 1 FROM REF_Fact WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: "}, {"timestamp": "2025-08-18T00:07:11.584207", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 209, "original_content": "INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)", "modified_content": "SELECT 1 FROM REF_Fact WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: "}, {"timestamp": "2025-08-18T00:07:11.588096", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 210, "original_content": "UPDATE REF_Entity SET Name = ? WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Entity WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.606907", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 211, "original_content": "DELETE FROM REF_Entity WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Entity WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.621799", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 212, "original_content": "SELECT COUNT(*) FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID", "modified_content": "SELECT 1 FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T00:07:11.658798", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 218, "original_content": "SELECT your option FROM the menu", "modified_content": "SELECT 1 FROM the menu", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T00:07:11.681780", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 228, "original_content": "SELECT * FROM", "modified_content": "SELECT * FROM", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'FROM'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T00:07:11.687019", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 230, "original_content": "SELECT name FROM users WHERE active = 1 ORDER BY name", "modified_content": "SELECT 1 FROM users WHERE active = 1 ORDER BY name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T00:07:11.694223", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Fix table name: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.698788", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Fix table name: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.701788", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Fix table name: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.704788", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Fix table name: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.707787", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Fix table name: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:11.712788", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 109, "original_content": "SELECT 1 FROM {table_name} WHERE 1=0", "modified_content": "SELECT 1 FROM {table_name} WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.716838", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 120, "original_content": "SELECT 1 FROM {table_name} WHERE {where_clause}", "modified_content": "SELECT 1 FROM {table_name} WHERE {where_clause}", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.721838", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 122, "original_content": "SELECT 1 FROM {table_name} WHERE 1=0", "modified_content": "SELECT 1 FROM {table_name} WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.725838", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 133, "original_content": "SELECT 1 FROM {table_name} WHERE {where_clause}", "modified_content": "SELECT 1 FROM {table_name} WHERE {where_clause}", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.729838", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 135, "original_content": "SELECT 1 FROM {table_name} WHERE 1=0", "modified_content": "SELECT 1 FROM {table_name} WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.733881", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 142, "original_content": "SELECT 1 FROM {table_name} WHERE 1=0", "modified_content": "SELECT 1 FROM {table_name} WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.737881", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 250, "original_content": "SELECT 1 FROM {tables[0]} WHERE 1=0", "modified_content": "SELECT 1 FROM {tables[0]} WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:11.758921", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_schema_validator.py", "line_number": 36, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE'\n        ORDER BY TABLE_NAME", "modified_content": "SELECT 1 \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE'\n        ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME"}, {"timestamp": "2025-08-18T00:07:11.764254", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_schema_validator.py", "line_number": 36, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE'\n        ORDER BY TABLE_NAME", "modified_content": "SELECT 1 \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE'\n        ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME"}, {"timestamp": "2025-08-18T00:07:11.815140", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_database_schema_references.py", "line_number": 111, "original_content": "SELECT TABLE_NAME \n            FROM INFORMATION_SCHEMA.TABLES \n            WHERE TABLE_TYPE = 'BASE TABLE'\n            ORDER BY TABLE_NAME", "modified_content": "SELECT 1 \n            FROM INFORMATION_SCHEMA.TABLES \n            WHERE TABLE_TYPE = 'BASE TABLE'\n            ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME"}, {"timestamp": "2025-08-18T00:07:11.827177", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_database_schema_references.py", "line_number": 111, "original_content": "SELECT TABLE_NAME \n            FROM INFORMATION_SCHEMA.TABLES \n            WHERE TABLE_TYPE = 'BASE TABLE'\n            ORDER BY TABLE_NAME", "modified_content": "SELECT 1 \n            FROM INFORMATION_SCHEMA.TABLES \n            WHERE TABLE_TYPE = 'BASE TABLE'\n            ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME"}, {"timestamp": "2025-08-18T00:07:11.844213", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_database_schema_references.py", "line_number": 131, "original_content": "SELECT COLUMN_NAME, DATA_TYPE \n                FROM INFORMATION_SCHEMA.COLUMNS \n                WHERE TABLE_NAME = '{table_name}'\n                ORDER BY ORDINAL_POSITION", "modified_content": "SELECT 1 \n                FROM INFORMATION_SCHEMA.COLUMNS \n                WHERE TABLE_NAME = '{table_name}'\n                ORDER BY ORDINAL_POSITION", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: COLUMN_NAME, TABLE_NAME, DATA_TYPE"}, {"timestamp": "2025-08-18T00:07:11.864148", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_database_schema_references.py", "line_number": 131, "original_content": "SELECT COLUMN_NAME, DATA_TYPE \n                FROM INFORMATION_SCHEMA.COLUMNS \n                WHERE TABLE_NAME = '{table_name}'\n                ORDER BY ORDINAL_POSITION", "modified_content": "SELECT 1 \n                FROM INFORMATION_SCHEMA.COLUMNS \n                WHERE TABLE_NAME = '{table_name}'\n                ORDER BY ORDINAL_POSITION", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: COLUMN_NAME, TABLE_NAME, DATA_TYPE"}, {"timestamp": "2025-08-18T00:07:11.887871", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_stored_procedures.py", "line_number": 12, "original_content": "SELECT \n            name,\n            create_date,\n            modify_date\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT \n            1\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name, create_date, modify_date"}, {"timestamp": "2025-08-18T00:07:11.909592", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_stored_procedures.py", "line_number": 12, "original_content": "SELECT \n            name,\n            create_date,\n            modify_date\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT \n            1\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name, create_date, modify_date"}, {"timestamp": "2025-08-18T00:07:11.937395", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_stored_procedures.py", "line_number": 83, "original_content": "SELECT name FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT 1 FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name"}, {"timestamp": "2025-08-18T00:07:11.960032", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_stored_procedures.py", "line_number": 83, "original_content": "SELECT name FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT 1 FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name"}, {"timestamp": "2025-08-18T00:07:11.996507", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_dashboard_db.py", "line_number": 11, "original_content": "SELECT * FROM REF_Entity WHERE Name LIKE 'Session_%'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name LIKE 'Session_%'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T00:07:12.047989", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_healing_pipeline.py", "line_number": 257, "original_content": "SELECT COUNT(*) as count FROM REF_Fact", "modified_content": "SELECT 1 FROM REF_Fact", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: count, COUNT"}, {"timestamp": "2025-08-18T00:07:12.072030", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 0, "original_content": "Contains 3 instances of 'XRF_Entity_Attribute_Value'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Fix table name: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:12.086419", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 79, "original_content": "SELECT \n                name,\n                create_date,\n                modify_date,\n                type_desc\n            FROM sys.objects \n            WHERE type = 'P' \n            AND name IN ({})\n            ORDER BY name", "modified_content": "SELECT \n                1\n            FROM sys.objects \n            WHERE type = 'P' \n            AND name IN ({})\n            ORDER BY name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:12.106369", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 79, "original_content": "SELECT \n                name,\n                create_date,\n                modify_date,\n                type_desc\n            FROM sys.objects \n            WHERE type = 'P' \n            AND name IN ({})\n            ORDER BY name", "modified_content": "SELECT \n                1\n            FROM sys.objects \n            WHERE type = 'P' \n            AND name IN ({})\n            ORDER BY name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:12.141177", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 129, "original_content": "SELECT TOP 5 ID, Name FROM REF_Entity ORDER BY ID", "modified_content": "SELECT 1 FROM REF_Entity ORDER BY ID", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: ID, Name"}, {"timestamp": "2025-08-18T00:07:12.177756", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 135, "original_content": "INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)\n                OUTPUT INSERTED.ID, INSERTED.Name\n                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T00:07:12.183911", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 135, "original_content": "INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)\n                OUTPUT INSERTED.ID, INSERTED.Name\n                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T00:07:12.198303", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 360, "original_content": "SELECT name FROM sys.objects\n            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'", "modified_content": "SELECT 1 FROM sys.objects\n            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name, type"}, {"timestamp": "2025-08-18T00:07:12.203102", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 360, "original_content": "SELECT name FROM sys.objects\n            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'", "modified_content": "SELECT 1 FROM sys.objects\n            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name, type"}, {"timestamp": "2025-08-18T00:07:12.212252", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 393, "original_content": "SELECT TABLE_NAME, TABLE_TYPE\n                FROM INFORMATION_SCHEMA.TABLES\n                WHERE TABLE_TYPE = 'BASE TABLE'\n                AND TABLE_NAME LIKE 'REF_%'\n                ORDER BY TABLE_NAME", "modified_content": "SELECT 1\n                FROM INFORMATION_SCHEMA.TABLES\n                WHERE TABLE_TYPE = 'BASE TABLE'\n                AND TABLE_NAME LIKE 'REF_%'\n                ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME"}, {"timestamp": "2025-08-18T00:07:12.217287", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 393, "original_content": "SELECT TABLE_NAME, TABLE_TYPE\n                FROM INFORMATION_SCHEMA.TABLES\n                WHERE TABLE_TYPE = 'BASE TABLE'\n                AND TABLE_NAME LIKE 'REF_%'\n                ORDER BY TABLE_NAME", "modified_content": "SELECT 1\n                FROM INFORMATION_SCHEMA.TABLES\n                WHERE TABLE_TYPE = 'BASE TABLE'\n                AND TABLE_NAME LIKE 'REF_%'\n                ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME"}, {"timestamp": "2025-08-18T00:07:12.224100", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_mcp_database.py", "line_number": 180, "original_content": "SELECT COUNT(*) as total_rows FROM [{schema_name}].[{table_name}]", "modified_content": "SELECT 1 FROM [{schema_name}].[{table_name}]", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T00:07:12.230543", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 0, "original_content": "Contains 6 instances of 'REF_EntityValues'", "modified_content": "Replaced with 'REF_EntityValue'", "rule_applied": "Fix table name: REF_EntityValues → REF_EntityValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T00:07:12.258784", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 29, "original_content": "SELECT COLUMN_NAME, DAT<PERSON>_TYPE, IS_NULLABLE\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = 'REF_EntityValue'\n        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')\n        ORDER BY COLUMN_NAME", "modified_content": "SELECT 1\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = 'REF_EntityValue'\n        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')\n        ORDER BY COLUMN_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: COLUMN_NAME, IS_NULLABLE, TABLE_NAME, DATA_TYPE"}, {"timestamp": "2025-08-18T00:07:12.281916", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 29, "original_content": "SELECT COLUMN_NAME, DAT<PERSON>_TYPE, IS_NULLABLE\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = 'REF_EntityValue'\n        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')\n        ORDER BY COLUMN_NAME", "modified_content": "SELECT 1\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = 'REF_EntityValue'\n        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')\n        ORDER BY COLUMN_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: COLUMN_NAME, IS_NULLABLE, TABLE_NAME, DATA_TYPE"}, {"timestamp": "2025-08-18T00:07:12.310603", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 48, "original_content": "SELECT \n            COUNT(*) as TotalRows,\n            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,\n            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows\n        FROM REF_EntityValue", "modified_content": "SELECT \n            1\n        FROM REF_EntityValue", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: NULL, IS, WHEN, UnitsRows, TotalRows, NumericValue, END, CASE, ValueUnits, THEN, COUNT, NumericRows, NOT"}, {"timestamp": "2025-08-18T00:07:12.325839", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 48, "original_content": "SELECT \n            COUNT(*) as TotalRows,\n            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,\n            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows\n        FROM REF_EntityValue", "modified_content": "SELECT \n            1\n        FROM REF_EntityValue", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: NULL, IS, WHEN, UnitsRows, TotalRows, NumericValue, END, CASE, ValueUnits, THEN, COUNT, NumericRows, NOT"}, {"timestamp": "2025-08-18T00:07:12.380249", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 66, "original_content": "SELECT TOP 5\n            Name,\n            EntityValue,\n            NumericValue,\n            ValueUnits\n        FROM REF_EntityValue \n        WHERE NumericValue IS NOT NULL\n        ORDER BY CreateDate DESC", "modified_content": "SELECT 1\n        FROM REF_EntityValue \n        WHERE NumericValue IS NOT NULL\n        ORDER BY CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue, Name, EntityValue"}, {"timestamp": "2025-08-18T00:07:12.427290", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 66, "original_content": "SELECT TOP 5\n            Name,\n            EntityValue,\n            NumericValue,\n            ValueUnits\n        FROM REF_EntityValue \n        WHERE NumericValue IS NOT NULL\n        ORDER BY CreateDate DESC", "modified_content": "SELECT 1\n        FROM REF_EntityValue \n        WHERE NumericValue IS NOT NULL\n        ORDER BY CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue, Name, EntityValue"}, {"timestamp": "2025-08-18T00:07:12.432628", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 149, "original_content": "SELECT EntityValue, NumericValue, ValueUnits\n            FROM REF_EntityValue \n            WHERE ID = ?", "modified_content": "SELECT 1\n            FROM REF_EntityValue \n            WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:12.445848", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 149, "original_content": "SELECT EntityValue, NumericValue, ValueUnits\n            FROM REF_EntityValue \n            WHERE ID = ?", "modified_content": "SELECT 1\n            FROM REF_EntityValue \n            WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:12.471344", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_stored_procedures.py", "line_number": 56, "original_content": "SELECT name \n        FROM sys.objects \n        WHERE type = 'P' \n        AND name IN ({})", "modified_content": "SELECT 1 \n        FROM sys.objects \n        WHERE type = 'P' \n        AND name IN ({})", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T00:07:12.507088", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_stored_procedures.py", "line_number": 56, "original_content": "SELECT name \n        FROM sys.objects \n        WHERE type = 'P' \n        AND name IN ({})", "modified_content": "SELECT 1 \n        FROM sys.objects \n        WHERE type = 'P' \n        AND name IN ({})", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}], "last_updated": "2025-08-18T00:07:12.507592", "end_time": "2025-08-18T00:07:12.542610", "summary": {"session_id": "20250818_000710", "total_entries": 152, "operations": {"sql_test": 131, "repair": 21}, "statuses": {"success": 80, "failed": 72}, "sql_validation": {"total_tests": 131, "passed": 59, "failed": 72, "success_rate": 45.038167938931295}, "log_files": {"detailed": "data\\database_repair_logs\\repair_session_20250818_000710.json", "summary": "data\\database_repair_logs\\repair_summary_20250818_000710.csv", "validation": "data\\database_repair_logs\\sql_validation_20250818_000710.txt"}}}