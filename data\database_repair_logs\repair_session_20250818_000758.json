{"session_id": "20250818_000758", "start_time": "2025-08-18T00:07:58.983451", "entries": [{"timestamp": "2025-08-18T00:07:59.014478", "operation_type": "sql_test", "file_path": "test_file.py", "line_number": 11, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = 'test'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-18T00:07:59.017902", "operation_type": "sql_test", "file_path": "test_file.py", "line_number": 14, "original_content": "SELECT your option from the menu", "modified_content": "SELECT 1 from the menu", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T00:07:59.020901", "operation_type": "sql_test", "file_path": "test_file.py", "line_number": 17, "original_content": "INSERT INTO REF_Fact (EntityID, Name, Value) \n    VALUES (?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Fact WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: "}, {"timestamp": "2025-08-18T00:07:59.022014", "operation_type": "sql_test", "file_path": "test_file.py", "line_number": 17, "original_content": "INSERT INTO REF_Fact (EntityID, Name, Value) \n    VALUES (?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Fact WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: "}, {"timestamp": "2025-08-18T00:07:59.024018", "operation_type": "sql_test", "file_path": "test_file.py", "line_number": 26, "original_content": "UPDATE REF_Entity SET Name = ? WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Entity WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}], "last_updated": "2025-08-18T00:07:59.025018", "end_time": "2025-08-18T00:07:59.026018", "summary": {"session_id": "20250818_000758", "total_entries": 5, "operations": {"sql_test": 5}, "statuses": {"success": 3, "failed": 2}, "sql_validation": {"total_tests": 5, "passed": 3, "failed": 2, "success_rate": 60.0}, "log_files": {"detailed": "data\\database_repair_logs\\repair_session_20250818_000758.json", "summary": "data\\database_repair_logs\\repair_summary_20250818_000758.csv", "validation": "data\\database_repair_logs\\sql_validation_20250818_000758.txt"}}}