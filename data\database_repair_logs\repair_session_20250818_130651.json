{"session_id": "20250818_130651", "start_time": "2025-08-18T13:06:51.749280", "entries": [{"timestamp": "2025-08-18T13:06:51.846945", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\example_usage.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.848945", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Start_SelfHealer.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.850050", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.850722", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.851726", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\context_analyzer.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.852726", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\error_monitor.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.854107", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\generic_types.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.856110", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\healer_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.857110", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_database_wrapper.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.858111", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.859514", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\learning_engine.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.860517", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\project_adapter.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.861517", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\solution_generator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.863517", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\solution_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.864517", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.865636", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.867639", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.868639", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_schema.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.871200", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\create_analytics_procedure.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.873204", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\create_simple_session_procedure.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.874205", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\create_schema_procedure.sql", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.876352", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\create_selfhealer_procedures.sql", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.878350", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\create_session_procedures.sql", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.879350", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\fix_table_naming_convention.sql", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.880853", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\get_knowledgebase_schema.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.883338", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\install_and_test.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.884343", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\schema_enhancement_numeric_values.sql", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.885826", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\update_procedures_numeric_values.sql", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.887742", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\tests\\check_healer_status.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.889746", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\tests\\debug_error_criteria.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.891747", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\tests\\debug_self_healer.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.893840", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\tests\\debug_self_healer_flow.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.894844", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\tests\\fix_healer_sync.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.896844", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\tests\\force_error_detection.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.898844", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\analyze_documentation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.904847", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\analyze_naming_convention.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.906714", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\analyze_project_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.907713", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\analyze_script_paths.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.909714", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\analyze_venv_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.911771", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\analyze_workspace_folders.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.913770", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.915273", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.916815", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.918814", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.919815", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.921814", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_script_paths.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.923982", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\check_database_tables.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.925982", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\clean_commit_messages.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.926982", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\compare_readme_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.930034", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\comprehensive_repo_scan.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.932034", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\config_backup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.934034", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\create_documentation_consolidation_plan.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.936034", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\database_repair_logger.py", "line_number": 0, "original_content": "Contains 2 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.938038", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\database_repair_logger.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.940240", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\debug_analyze_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.942239", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\delete_obsolete_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.944240", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.946302", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\detect_private_components.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.948302", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\detect_private_components2.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.950302", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\dev_publish.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.952809", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.953810", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Attributes'", "modified_content": "Replaced with 'REF_Attribute'", "rule_applied": "Table name fix: REF_Attributes → REF_Attribute", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.956271", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_EntityValues'", "modified_content": "Replaced with 'REF_EntityValue'", "rule_applied": "Table name fix: REF_EntityValues → REF_EntityValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.958274", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.960455", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.962027", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.965082", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.967085", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'XRF_Entity_Attribute_Value'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.968085", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'XRF_Entity_AttributeValue'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_AttributeValue → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.970085", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.972564", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\execute_separation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.975573", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\final_documentation_validation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.977573", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\fix_analyze_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.979569", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\fix_healer_logging.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.983075", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\fix_script_references.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.985075", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\generate_consolidation_report.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.987075", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\generate_process_flow.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.989545", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\github_repository_setup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.991553", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\improve_documentation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.994557", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\migrate_naming_convention.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.996558", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\prepare_public_release.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:51.999623", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\pre_commit_cleanup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.001623", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\pre_execution_verification.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.004624", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\project_cleanup_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.006623", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\quick_test_research.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.009567", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.011968", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\restart_n8n_clean.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.013642", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\safe_analyze_project_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.015987", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\safe_cleanup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.018786", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sanitize_documentation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.020914", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\scan_md_for_private_refs.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.024390", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\setup_log_rotation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.027298", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\shutdown.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.029695", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_database_fixer.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.034391", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.038351", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.041021", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.043588", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.046138", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.048684", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.051241", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.053542", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\streamlined_documentation_cleanup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.056542", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_detection_simple.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.059543", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_enhanced_sync.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.061542", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_llm_connection.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.064543", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_logging.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.066546", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_mcp_research.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.070329", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_refined_detection.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.072332", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_safe_cleanup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.075811", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_schema_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.078814", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_self_healer_real_world.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.081331", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_verification_systems.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.084344", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_workflow_generator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.088037", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\update_folder_references.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.090340", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_database_schema_references.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.093339", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_documentation_links.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.096339", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_documentation_quality.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.100339", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_stored_procedures.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.103343", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\verification_pipeline.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.106303", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\analyze_documentation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.109307", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\analyze_project_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.112323", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\analyze_script_paths.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.116491", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\analyze_venv_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.119192", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\analyze_workspace_folders.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.122494", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\clean_commit_messages.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.125497", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\compare_readme_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.128816", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\comprehensive_repo_scan.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.132809", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\create_documentation_consolidation_plan.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.135980", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\debug_analyze_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.138983", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\delete_obsolete_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.142024", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\dev_publish.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.144401", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\execute_separation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.148404", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\final_documentation_validation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.151404", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\fix_analyze_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.154568", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\fix_script_references.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.157796", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\generate_consolidation_report.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.161250", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\github_repository_setup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.164984", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\improve_documentation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.167989", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\migrate_naming_convention.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.170989", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\prepare_public_release.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.174056", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\pre_commit_cleanup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.177919", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\pre_execution_verification.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.181789", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\project_cleanup_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.184793", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\sanitize_documentation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.187792", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\scan_md_for_private_refs.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.190793", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\setup_log_rotation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.195101", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\shutdown.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.198559", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\streamlined_documentation_cleanup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.201562", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\test_detection_simple.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.204561", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\test_enhanced_sync.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.207749", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\test_safe_cleanup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.211799", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\test_verification_systems.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.214898", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\update_folder_references.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.217653", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\validate_documentation_links.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.221370", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\validate_documentation_quality.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.224588", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\verification_pipeline.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.229281", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.231675", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\configurable_database_fixer.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.236067", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\config_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.239056", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\run_comprehensive_test.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.243123", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\simple_test.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.247169", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.250347", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\conftest.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.253407", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\investigate_healer_disconnect.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.256408", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\quick_healer_check.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.260866", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\run_system_tests.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.263951", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_actual_response.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.268227", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_complete_integration.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.271781", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_complete_self_healer_flow.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.274947", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_dashboard_db.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.278639", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_dashboard_improvements.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.282430", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_dashboard_integration.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.285891", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_dependencies.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.290054", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_enhanced_extraction.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.293557", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_env.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.296560", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_filesystem_utilities.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.300981", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_full_modification.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.304465", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_healing_pipeline.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.308054", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_integration.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.312497", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_iteration_methods.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.315497", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 0, "original_content": "Contains 3 instances of 'XRF_Entity_Attribute_Value'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.319524", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.323527", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_live_self_healer.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.327564", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_log_rotation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.330578", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_mcp_database.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.335611", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_mcp_research.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.340274", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_n8n_builder_unit.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.344742", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 0, "original_content": "Contains 6 instances of 'REF_EntityValues'", "modified_content": "Replaced with 'REF_EntityValue'", "rule_applied": "Table name fix: REF_EntityValues → REF_EntityValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.347764", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.358567", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_project_api.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.362842", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_project_cleanup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.367516", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_project_management.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.370570", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_research_quality.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.374570", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_self_healer_fix.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.378647", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_self_healer_integration.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.382604", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_self_healer_rescan.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.386609", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_smart_logging.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.390608", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_stored_procedures.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.394824", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_system_health.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.399531", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_version_management.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.403661", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agui_server.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.406989", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\app.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.412330", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\cli.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.416334", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\code_generation_patterns.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.420390", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\config.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.425391", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\enhanced_prompt_builder.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.429393", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\error_handler.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.434390", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\example_enhanced_workflow.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.438638", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\knowledge_cache.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.441644", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\logging_config.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.447131", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\log_rotation_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.451134", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_server.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.454593", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.464148", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_research_server.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.478984", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_research_tool.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.484235", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_workflow_server.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.488788", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\n8n_builder.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.493600", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\optional_integrations.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.498210", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\performance_optimizer.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.502816", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\project_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.509349", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\research_formatter.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.514507", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\research_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.519750", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\retry_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.524407", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\validators.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.529030", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\workflow_differ.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.533250", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.538259", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\base_agent.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.542258", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.546327", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\agent_integration_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.550395", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\error_recovery.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.555900", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\event_stream_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.560974", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\event_types.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.564657", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\message_broker.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.570321", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\message_protocol.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.574362", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\monitoring.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.578441", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\security.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.582993", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\state_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.587933", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\ui_controller.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.591931", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.595993", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\validation\\error_codes.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.602035", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\validation\\validators\\connection.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.606063", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\validation\\validators\\node.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.610575", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\validation\\validators\\workflow_logic.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.616379", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\validation\\validators\\workflow_structure.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.621112", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\packaging\\_musllinux.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.626116", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.632162", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\__main__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.637296", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\build_env.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.641899", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\cache.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.646394", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\main.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.651448", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\pyproject.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.655788", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.660791", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\cli\\base_command.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.666184", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\cli\\main.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.671227", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\index\\collector.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.676796", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\models\\direct_url.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.681286", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\models\\target_python.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.690732", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\operations\\install\\wheel.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.696312", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\req\\constructors.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.701052", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\req\\req_install.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.706715", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\req\\req_uninstall.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.711313", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\utils\\misc.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.717741", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\_cmd.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.723035", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\chardet\\cli\\chardetect.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.727286", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\compat.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.731371", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\database.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.737979", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\index.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.741981", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\locators.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.746989", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\manifest.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.752990", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\metadata.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.758040", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\scripts.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.762934", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\util.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.768707", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\wheel.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.773708", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distro\\distro.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.778708", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distro\\__main__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.783214", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\__main__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.787841", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\cmdline.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.793453", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatters\\html.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.799456", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatters\\_mapping.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.804805", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\python.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.811098", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\_mapping.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.815626", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.820626", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_impl.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.825880", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_in_process\\_in_process.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.833321", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_in_process\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.839180", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\requests\\certs.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.844517", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\requests\\help.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.849976", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\abc.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.854866", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\align.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.861458", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\ansi.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.866908", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\box.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.871097", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\cells.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.877883", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\color.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.883389", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.888157", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\control.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.894488", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\default_styles.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.899491", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\diagnose.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.904946", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\emoji.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.910350", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\highlighter.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.915387", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\json.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.920891", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\layout.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.927208", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\live.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.932697", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\logging.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.937698", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\markup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.943064", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\padding.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.948965", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\palette.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.955055", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\panel.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.960403", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\pretty.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.967496", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.975260", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress_bar.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.981300", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\prompt.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.987355", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\repr.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.992600", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\rule.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:52.998275", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\scope.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.003562", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\segment.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.011297", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\spinner.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.018048", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\status.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.023368", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\styled.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.028371", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\syntax.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.034777", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\table.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.041822", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\text.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.048480", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\theme.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.054093", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\traceback.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.059838", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\tree.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.067899", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_log_render.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.073216", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_ratio.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.079222", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_win32_console.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.085238", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_windows.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.090750", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_wrap.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.097755", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.102757", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__main__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.109046", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\connectionpool.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.114908", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\ssl_.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.121028", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\webencodings\\mklabels.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.128419", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.134423", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\cmdline.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.140490", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\formatters\\html.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.146933", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\formatters\\_mapping.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.152297", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\actionscript.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.159007", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\archetype.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.165801", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\asm.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.171094", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\basic.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.177093", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\bqn.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.182428", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\configs.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.188765", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\css.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.195109", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\c_like.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.201115", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\data.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.207112", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\graphviz.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.213167", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\haskell.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.219740", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\haxe.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.225742", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\html.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.232201", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\idl.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.238216", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\inferno.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.244324", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\installers.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.251597", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\javascript.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.257126", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\jsx.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.264162", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\lisp.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.275422", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\markup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.281386", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\matlab.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.288391", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\mosel.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.295536", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\objective.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.301703", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\pawn.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.306910", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\python.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.314282", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\r.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.320264", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\resource.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.326854", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\roboconf.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.333060", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\scripting.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.339656", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\sql.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.346238", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\tablegen.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.352709", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\tal.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.359108", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\tcl.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.366112", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\templates.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.372161", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\ul4.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.377754", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\usd.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.383592", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\webmisc.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.392001", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\_mapping.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.398866", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\_php_builtins.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.404997", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.411438", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pytest\\__main__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.417441", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\build_meta.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.424092", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\discovery.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.430089", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\dist.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.438075", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\msvc.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.444232", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\package_index.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.450518", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\wheel.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.457373", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_imp.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.464408", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.470790", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\build_ext.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.477234", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\build_py.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.483405", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\easy_install.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.490403", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\editable_wheel.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.497018", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.504637", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\install_lib.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.511158", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\py36compat.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.524703", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\test.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.533113", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\config\\expand.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.541317", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.548244", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.555875", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.562525", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.570528", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.577147", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.584255", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.590262", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.598379", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.605379", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_distutils_hack\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.613884", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\deprecated.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.621030", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\doctest.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.628630", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\fixtures.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.636673", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\main.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.645000", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\nodes.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.652041", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\pathlib.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.659630", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\pytester.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.666618", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\python.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.674065", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\terminal.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.681440", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\assertion\\rewrite.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.689070", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\config\\findpaths.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.697075", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\config\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.706124", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\mark\\structures.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.713454", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\_code\\code.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T13:06:53.720572", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\_py\\path.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}], "last_updated": "2025-08-18T13:06:53.721572", "end_time": "2025-08-18T13:06:53.728252", "summary": {"session_id": "20250818_130651", "total_entries": 421, "operations": {"validation": 400, "repair": 21}, "statuses": {"failed": 400, "success": 21}, "sql_validation": {"total_tests": 0, "passed": 0, "failed": 0, "success_rate": 0}, "log_files": {"detailed": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\database_repair_logs\\repair_session_20250818_130651.json", "summary": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\database_repair_logs\\repair_summary_20250818_130651.csv", "validation": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\database_repair_logs\\sql_validation_20250818_130651.txt"}}}