{"session_id": "20250818_211717", "start_time": "2025-08-18T21:17:17.557363", "entries": [{"timestamp": "2025-08-18T21:17:17.657475", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\example_usage.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.661096", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Start_SelfHealer.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.661096", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.661096", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.661096", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\context_analyzer.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.661096", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\error_monitor.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.661096", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\generic_types.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.661096", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\healer_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.661096", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_database_wrapper.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.661096", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.672778", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\learning_engine.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.674782", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\project_adapter.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.675753", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\solution_generator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.677874", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\solution_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.677874", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.677874", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.680877", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.680877", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_schema.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.680877", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\create_analytics_procedure.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.680877", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\create_simple_session_procedure.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.687585", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\create_schema_procedure.sql", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.689589", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\create_selfhealer_procedures.sql", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.691589", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\create_session_procedures.sql", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.693069", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\fix_table_naming_convention.sql", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.693673", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\get_knowledgebase_schema.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.693673", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\install_and_test.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.693673", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\schema_enhancement_numeric_values.sql", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.699676", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\update_procedures_numeric_values.sql", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.699676", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\tests\\check_healer_status.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.699676", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\tests\\debug_error_criteria.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.705032", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\tests\\debug_self_healer.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.706357", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\tests\\debug_self_healer_flow.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.708950", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\tests\\fix_healer_sync.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.709950", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\tests\\force_error_detection.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.709950", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\analyze_documentation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.709950", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\analyze_naming_convention.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.709950", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\analyze_project_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.709950", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\analyze_script_paths.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.709950", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\analyze_venv_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.722840", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\analyze_workspace_folders.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.724752", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.726709", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.728017", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.729763", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.731813", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.733257", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_script_paths.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.735352", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\check_database_tables.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.737355", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\clean_commit_messages.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.739596", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\compare_readme_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.741625", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\comprehensive_repo_scan.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.743589", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\config_backup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.744589", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\create_documentation_consolidation_plan.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.744589", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\database_repair_logger.py", "line_number": 0, "original_content": "Contains 2 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.749101", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\database_repair_logger.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.751481", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\debug_analyze_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.753616", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\delete_obsolete_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.755782", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.755782", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\detect_private_components.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.755782", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\detect_private_components2.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.755782", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\dev_publish.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.755782", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.767372", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Attributes'", "modified_content": "Replaced with 'REF_Attribute'", "rule_applied": "Table name fix: REF_Attributes → REF_Attribute", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.770002", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_EntityValues'", "modified_content": "Replaced with 'REF_EntityValue'", "rule_applied": "Table name fix: REF_EntityValues → REF_EntityValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.772112", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.774115", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.776197", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.778197", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.780934", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'XRF_Entity_Attribute_Value'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.780934", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'XRF_Entity_AttributeValue'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_AttributeValue → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.785926", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.787931", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\execute_separation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.789603", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\final_documentation_validation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.791693", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\fix_analyze_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.793768", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\fix_healer_logging.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.797730", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\fix_script_references.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.800965", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\generate_consolidation_report.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.802954", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\generate_process_flow.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.804323", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\github_repository_setup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.806960", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\improve_documentation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.806960", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\migrate_naming_convention.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.811346", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\prepare_public_release.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.813834", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\pre_commit_cleanup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.818232", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\pre_execution_verification.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.821236", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\project_cleanup_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.822236", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\quick_test_research.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.822236", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.828822", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\restart_n8n_clean.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.830826", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\safe_analyze_project_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.837274", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\safe_cleanup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.837274", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sanitize_documentation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.843025", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\scan_md_for_private_refs.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.843025", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\setup_log_rotation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.843025", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\shutdown.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.853506", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_database_fixer.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.856302", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.859369", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.861954", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.865659", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.867663", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.870881", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.873165", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.874165", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\streamlined_documentation_cleanup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.874165", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_detection_simple.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.881190", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_enhanced_sync.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.881190", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_llm_connection.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.881190", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_logging.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.891156", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_mcp_research.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.895035", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_refined_detection.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.896034", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_safe_cleanup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.900191", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_schema_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.904191", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_self_healer_real_world.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.904191", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_verification_systems.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.904191", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_workflow_generator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.904191", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\update_folder_references.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.904191", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_database_schema_references.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.919833", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_documentation_links.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.923919", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_documentation_quality.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.926954", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_stored_procedures.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.930639", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\verification_pipeline.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.931639", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\analyze_documentation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.931639", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\analyze_project_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.931639", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\analyze_script_paths.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.931639", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\analyze_venv_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.947917", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\analyze_workspace_folders.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.950661", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\clean_commit_messages.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.953733", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\compare_readme_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.957395", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\comprehensive_repo_scan.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.960395", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\create_documentation_consolidation_plan.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.961395", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\debug_analyze_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.961395", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\delete_obsolete_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.961395", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\dev_publish.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.961395", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\execute_separation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.961395", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\final_documentation_validation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.980376", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\fix_analyze_files.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.983349", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\fix_script_references.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.987222", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\generate_consolidation_report.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.990767", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\github_repository_setup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.992946", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\improve_documentation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.992946", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\migrate_naming_convention.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.992946", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\prepare_public_release.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:17.992946", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\pre_commit_cleanup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.006792", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\pre_execution_verification.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.009792", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\project_cleanup_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.010957", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\sanitize_documentation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.016522", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\scan_md_for_private_refs.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.019122", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\setup_log_rotation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.023694", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\shutdown.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.023694", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\streamlined_documentation_cleanup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.023694", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\test_detection_simple.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.034449", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\test_enhanced_sync.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.037448", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\test_safe_cleanup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.037448", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\test_verification_systems.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.037448", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\update_folder_references.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.048553", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\validate_documentation_links.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.055351", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\validate_documentation_quality.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.055351", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\backups\\path_fixes_20250717_185445\\verification_pipeline.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.055351", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.055351", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\configurable_database_fixer.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.071005", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\config_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.071005", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\run_comprehensive_test.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.080093", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\simple_test.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.084918", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.086915", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\conftest.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.086915", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\investigate_healer_disconnect.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.086915", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\quick_healer_check.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.086915", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\run_system_tests.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.086915", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_actual_response.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.102560", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_complete_integration.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.110152", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_complete_self_healer_flow.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.114413", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_dashboard_db.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.117408", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_dashboard_improvements.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.120955", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_dashboard_integration.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.124433", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_dependencies.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.128509", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_enhanced_extraction.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.132376", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_env.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.136314", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_filesystem_utilities.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.139892", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_full_modification.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.143845", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_healing_pipeline.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.147731", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_integration.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.152011", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_iteration_methods.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.155899", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 0, "original_content": "Contains 3 instances of 'XRF_Entity_Attribute_Value'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.160526", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.163831", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_live_self_healer.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.167241", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_log_rotation.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.170996", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_mcp_database.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.176880", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_mcp_research.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.180667", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_n8n_builder_unit.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.181670", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 0, "original_content": "Contains 6 instances of 'REF_EntityValues'", "modified_content": "Replaced with 'REF_EntityValue'", "rule_applied": "Table name fix: REF_EntityValues → REF_EntityValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.181670", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.192878", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_project_api.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.194878", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_project_cleanup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.200454", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_project_management.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.205046", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_research_quality.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.206371", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_self_healer_fix.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.206371", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_self_healer_integration.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.206371", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_self_healer_rescan.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.206371", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_smart_logging.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.225231", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_stored_procedures.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.228234", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_system_health.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.233183", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_version_management.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.238251", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agui_server.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.239295", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\app.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.239295", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\cli.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.239295", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\code_generation_patterns.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.255864", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\config.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.258868", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\enhanced_prompt_builder.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.258868", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\error_handler.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.269801", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\example_enhanced_workflow.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.274057", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\knowledge_cache.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.274057", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\logging_config.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.281575", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\log_rotation_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.281575", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_server.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.291716", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.295720", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_research_server.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.299993", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_research_tool.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.299993", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_workflow_server.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.299993", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\n8n_builder.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.315999", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\optional_integrations.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.320541", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\performance_optimizer.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.321541", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\project_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.331189", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\research_formatter.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.331466", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\research_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.331466", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\retry_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.331466", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\validators.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.348625", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\workflow_differ.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.354223", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.357653", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\base_agent.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.363640", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.368730", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\agent_integration_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.368730", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\error_recovery.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.381678", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\event_stream_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.386748", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\event_types.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.391438", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\message_broker.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.396506", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\message_protocol.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.401128", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\monitoring.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.406365", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\security.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.410973", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\state_manager.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.415647", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\ui_controller.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.419653", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\agents\\integration\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.424759", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\validation\\error_codes.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.425353", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\validation\\validators\\connection.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.425353", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\validation\\validators\\node.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.425353", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\validation\\validators\\workflow_logic.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.444283", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\validation\\validators\\workflow_structure.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.447287", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\packaging\\_musllinux.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.454461", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.459341", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\__main__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.461344", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\build_env.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.461344", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\cache.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.461344", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\main.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.477634", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\pyproject.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.481638", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.489095", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\cli\\base_command.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.494094", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\cli\\main.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.495030", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\index\\collector.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.495030", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\models\\direct_url.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.495030", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\models\\target_python.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.513575", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\operations\\install\\wheel.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.518671", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\req\\constructors.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.518671", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\req\\req_install.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.518671", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\req\\req_uninstall.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.535255", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_internal\\utils\\misc.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.539681", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\cachecontrol\\_cmd.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.540682", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\chardet\\cli\\chardetect.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.551272", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\compat.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.553612", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\database.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.553612", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\index.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.566032", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\locators.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.571039", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\manifest.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.571039", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\metadata.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.581530", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\scripts.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.585692", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\util.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.585692", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\wheel.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.597846", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distro\\distro.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.602303", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distro\\__main__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.608955", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\platformdirs\\__main__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.614934", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\cmdline.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.618921", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatters\\html.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.618921", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\formatters\\_mapping.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.627372", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\python.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.635086", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\_mapping.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.641181", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pygments\\lexers\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.647377", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_impl.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.649381", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_in_process\\_in_process.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.649381", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\pyproject_hooks\\_in_process\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.665025", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\requests\\certs.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.666883", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\requests\\help.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.676683", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\abc.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.681698", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\align.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.681698", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\ansi.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.681698", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\box.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.696471", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\cells.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.704056", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\color.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.710156", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.713162", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\control.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.713162", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\default_styles.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.713162", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\diagnose.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.733184", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\emoji.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.739368", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\highlighter.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.741892", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\json.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.750395", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\layout.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.753100", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\live.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.760274", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\logging.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.767230", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\markup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.772144", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\padding.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.773148", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\palette.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.781691", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\panel.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.789955", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\pretty.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.797540", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.802981", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress_bar.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.805483", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\prompt.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.815154", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\repr.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.815154", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\rule.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.828899", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\scope.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.835513", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\segment.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.835513", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\spinner.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.835513", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\status.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.854743", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\styled.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.860180", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\syntax.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.864026", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\table.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.864026", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\text.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.881545", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\theme.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.881545", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\traceback.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.898008", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\tree.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.898008", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_log_render.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.915209", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_ratio.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.919618", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_win32_console.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.926416", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_windows.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.926416", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\_wrap.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.939427", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.942348", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\__main__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.950851", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\connectionpool.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.959166", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\urllib3\\util\\ssl_.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.961170", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\webencodings\\mklabels.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.961170", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.976818", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\cmdline.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.981824", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\formatters\\html.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.989391", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\formatters\\_mapping.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:18.989391", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\actionscript.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.002401", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\archetype.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.002401", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\asm.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.002401", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\basic.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.022952", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\bqn.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.022952", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\configs.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.022952", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\css.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.039834", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\c_like.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.039834", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\data.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.053646", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\graphviz.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.056960", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\haskell.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.056960", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\haxe.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.071635", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\html.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.078803", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\idl.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.085234", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\inferno.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.088685", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\installers.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.096848", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\javascript.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.103074", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\jsx.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.109932", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\lisp.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.116186", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\markup.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.116186", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\matlab.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.128419", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\mosel.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.128419", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\objective.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.143658", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\pawn.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.149370", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\python.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.150873", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\r.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.150873", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\resource.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.164553", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\roboconf.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.176793", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\scripting.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.181843", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\sql.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.181843", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\tablegen.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.196648", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\tal.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.206590", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\tcl.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.206590", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\templates.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.222421", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\ul4.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.227903", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\usd.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.237076", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\webmisc.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.242908", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\_mapping.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.242908", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\_php_builtins.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.258551", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pygments\\lexers\\_sourcemod_builtins.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.266645", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pytest\\__main__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.268721", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\build_meta.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.281768", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\discovery.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.284778", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\dist.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.284778", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\msvc.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.304690", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\package_index.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.304690", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\wheel.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.304690", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_imp.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.325842", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.333220", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\build_ext.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.333220", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\build_py.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.345731", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\easy_install.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.351240", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\editable_wheel.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.361753", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.366847", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\install_lib.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.366847", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\py36compat.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.366847", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\command\\test.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.382013", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\config\\expand.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.396677", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.399567", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.399567", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.414579", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.424747", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.425623", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.425623", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.445270", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.453780", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.460284", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_distutils_hack\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.461286", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\deprecated.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.461286", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\doctest.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.482101", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\fixtures.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.487450", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\main.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.487450", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\nodes.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.503617", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\pathlib.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.509429", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\pytester.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.519574", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\python.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.522654", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\terminal.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.534223", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\assertion\\rewrite.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.540780", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\config\\findpaths.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.549326", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\config\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.553911", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\mark\\structures.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.553911", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\_code\\code.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:17:19.566147", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\_pytest\\_py\\path.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: 'NoneType' object has no attribute 'items'", "sql_validation_result": null}], "last_updated": "2025-08-18T21:17:19.566147", "end_time": "2025-08-18T21:17:19.581137", "summary": {"session_id": "20250818_211717", "total_entries": 421, "operations": {"validation": 400, "repair": 21}, "statuses": {"failed": 400, "success": 21}, "sql_validation": {"total_tests": 0, "passed": 0, "failed": 0, "success_rate": 0}, "log_files": {"detailed": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\database_repair_logs\\repair_session_20250818_211717.json", "summary": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\database_repair_logs\\repair_summary_20250818_211717.csv", "validation": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\database_repair_logs\\sql_validation_20250818_211717.txt"}}}