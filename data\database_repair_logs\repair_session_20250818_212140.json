{"session_id": "20250818_212140", "start_time": "2025-08-18T21:21:40.498664", "entries": [{"timestamp": "2025-08-18T21:21:40.600335", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.605341", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.610384", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.611109", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.612113", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_schema.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.614112", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\create_analytics_procedure.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.615112", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\create_schema_procedure.sql", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.622131", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.623592", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.624597", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.626596", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.627866", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.629871", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\check_database_tables.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.632944", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\database_repair_logger.py", "line_number": 0, "original_content": "Contains 2 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.634944", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\database_repair_logger.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.636945", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.638944", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.641222", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Attributes'", "modified_content": "Replaced with 'REF_Attribute'", "rule_applied": "Table name fix: REF_Attributes → REF_Attribute", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.642223", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_EntityValues'", "modified_content": "Replaced with 'REF_EntityValue'", "rule_applied": "Table name fix: REF_EntityValues → REF_EntityValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.644270", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.646270", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.647532", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.648535", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.650537", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'XRF_Entity_Attribute_Value'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.651535", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'XRF_Entity_AttributeValue'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_AttributeValue → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.660748", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.665259", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.666465", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.667742", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.668745", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.670746", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.672746", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.676745", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_refined_detection.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.677747", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_schema_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.682278", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_database_schema_references.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.686277", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_stored_procedures.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.705389", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.709390", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\simple_test.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.711356", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.716471", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_dashboard_db.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.720470", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_healing_pipeline.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.723470", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 0, "original_content": "Contains 3 instances of 'XRF_Entity_Attribute_Value'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.726470", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.729476", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_mcp_database.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.732189", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 0, "original_content": "Contains 6 instances of 'REF_EntityValues'", "modified_content": "Replaced with 'REF_EntityValue'", "rule_applied": "Table name fix: REF_EntityValues → REF_EntityValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.734192", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.739389", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_stored_procedures.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.747251", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_server.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.750299", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.775373", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\manifest.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.794142", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.801601", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.873588", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:21:40.878513", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}], "last_updated": "2025-08-18T21:21:40.878513", "end_time": "2025-08-18T21:21:40.891143", "summary": {"session_id": "20250818_212140", "total_entries": 54, "operations": {"validation": 33, "repair": 21}, "statuses": {"failed": 33, "success": 21}, "sql_validation": {"total_tests": 0, "passed": 0, "failed": 0, "success_rate": 0}, "log_files": {"detailed": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\database_repair_logs\\repair_session_20250818_212140.json", "summary": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\database_repair_logs\\repair_summary_20250818_212140.csv", "validation": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\database_repair_logs\\sql_validation_20250818_212140.txt"}}}