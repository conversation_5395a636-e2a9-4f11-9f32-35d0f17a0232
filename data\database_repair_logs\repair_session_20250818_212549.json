{"session_id": "20250818_212549", "start_time": "2025-08-18T21:25:49.219902", "entries": [{"timestamp": "2025-08-18T21:25:49.314374", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.321601", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.325869", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.326924", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.329002", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_schema.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.330006", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\create_analytics_procedure.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.331591", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\create_schema_procedure.sql", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.339753", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.340754", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.341755", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.341755", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.344995", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.346556", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\check_database_tables.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.350555", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\database_repair_logger.py", "line_number": 0, "original_content": "Contains 2 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.350555", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\database_repair_logger.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.354195", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.356194", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.357194", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Attributes'", "modified_content": "Replaced with 'REF_Attribute'", "rule_applied": "Table name fix: REF_Attributes → REF_Attribute", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.358771", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_EntityValues'", "modified_content": "Replaced with 'REF_EntityValue'", "rule_applied": "Table name fix: REF_EntityValues → REF_EntityValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.361277", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.362277", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.365392", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.366688", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.367688", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'XRF_Entity_Attribute_Value'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.369396", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'XRF_Entity_AttributeValue'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_AttributeValue → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.378123", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.382126", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.383128", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.385454", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.387458", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.388457", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.391201", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.394458", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_refined_detection.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.397631", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_schema_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.401634", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_database_schema_references.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.403875", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_stored_procedures.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.422354", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.425224", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\simple_test.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.427897", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.433186", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_dashboard_db.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.436122", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_healing_pipeline.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.438496", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 0, "original_content": "Contains 3 instances of 'XRF_Entity_Attribute_Value'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.438496", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.445184", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_mcp_database.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.448188", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 0, "original_content": "Contains 6 instances of 'REF_EntityValues'", "modified_content": "Replaced with 'REF_EntityValue'", "rule_applied": "Table name fix: REF_EntityValues → REF_EntityValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.450996", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.456070", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_stored_procedures.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.465609", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_server.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.465609", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.487976", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\manifest.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.508694", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.517535", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.585592", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}, {"timestamp": "2025-08-18T21:25:49.596604", "operation_type": "validation", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py", "line_number": 0, "original_content": "", "modified_content": "", "rule_applied": "file_processing_error", "status": "failed", "error_message": "Error processing file: argument of type 'NoneType' is not iterable", "sql_validation_result": null}], "last_updated": "2025-08-18T21:25:49.596604"}