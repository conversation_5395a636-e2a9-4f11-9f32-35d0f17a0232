{"session_id": "20250818_213553", "start_time": "2025-08-18T21:35:53.541999", "entries": [{"timestamp": "2025-08-18T21:35:53.705993", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 109, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n               COUNT(e.ID) as EvidenceCount,\n               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage\n        FROM REF_Fact f\n        LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n        WHERE f.DataSource LIKE '%Self-Healer%'", "modified_content": "SELECT 1\n        FROM REF_Fact f\n        LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n        WHERE f.DataSource LIKE '%Self-Healer%'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: AVG, CreateDate, ValidityRating, ID, EvidenceCount, OVER, FLOAT, COUNT, f, OverallAverage, DataSource, e, CAST, Name"}, {"timestamp": "2025-08-18T21:35:53.705993", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 109, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n               COUNT(e.ID) as EvidenceCount,\n               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage\n        FROM REF_Fact f\n        LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n        WHERE f.DataSource LIKE '%Self-Healer%'", "modified_content": "SELECT 1\n        FROM REF_Fact f\n        LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n        WHERE f.DataSource LIKE '%Self-Healer%'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: AVG, CreateDate, ValidityRating, ID, EvidenceCount, OVER, FLOAT, COUNT, f, OverallAverage, DataSource, e, CAST, Name"}, {"timestamp": "2025-08-18T21:35:53.705993", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 194, "original_content": "INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)\n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Evidence WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, Columns: "}, {"timestamp": "2025-08-18T21:35:53.705993", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 194, "original_content": "INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)\n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Evidence WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, Columns: "}, {"timestamp": "2025-08-18T21:35:53.721620", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 206, "original_content": "SELECT ValidityRating FROM REF_Fact WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Fact WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.742730", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 218, "original_content": "SELECT COUNT(*) as EvidenceCount FROM REF_Evidence WHERE FactID = ?", "modified_content": "SELECT 1 FROM REF_Evidence WHERE FactID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.745299", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 226, "original_content": "UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Fact WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.747356", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 260, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n                   'fact' as KnowledgeType\n            FROM REF_Fact f\n            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)\n            AND f.DataSource LIKE '%Self-Healer%'\n            AND f.ValidityRating >= ?\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)\n            AND f.DataSource LIKE '%Self-Healer%'\n            AND f.ValidityRating >= ?\n            ORDER BY f.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.749408", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 260, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n                   'fact' as KnowledgeType\n            FROM REF_Fact f\n            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)\n            AND f.DataSource LIKE '%Self-Healer%'\n            AND f.ValidityRating >= ?\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)\n            AND f.DataSource LIKE '%Self-Healer%'\n            AND f.ValidityRating >= ?\n            ORDER BY f.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.751901", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 276, "original_content": "SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,\n                   'opinion' as KnowledgeType\n            FROM REF_Opinion o\n            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)\n            AND o.DataSource LIKE '%Self-Healer%'\n            AND o.ValidityRating >= ?\n            ORDER BY o.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Opinion o\n            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)\n            AND o.DataSource LIKE '%Self-Healer%'\n            AND o.ValidityRating >= ?\n            ORDER BY o.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.754630", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 276, "original_content": "SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,\n                   'opinion' as KnowledgeType\n            FROM REF_Opinion o\n            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)\n            AND o.DataSource LIKE '%Self-Healer%'\n            AND o.ValidityRating >= ?\n            ORDER BY o.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Opinion o\n            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)\n            AND o.DataSource LIKE '%Self-Healer%'\n            AND o.ValidityRating >= ?\n            ORDER BY o.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.756907", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 292, "original_content": "SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,\n                   f.ValidityRating, 'evidence' as KnowledgeType\n            FROM REF_Evidence e\n            JOIN REF_Fact f ON e.FactID = f.ID\n            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)\n            AND e.DataSource LIKE '%Self-Healer%'\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Evidence e\n            JOIN REF_Fact f ON e.FactID = f.ID\n            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)\n            AND e.DataSource LIKE '%Self-Healer%'\n            ORDER BY f.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.759481", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 292, "original_content": "SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,\n                   f.ValidityRating, 'evidence' as KnowledgeType\n            FROM REF_Evidence e\n            JOIN REF_Fact f ON e.FactID = f.ID\n            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)\n            AND e.DataSource LIKE '%Self-Healer%'\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Evidence e\n            JOIN REF_Fact f ON e.FactID = f.ID\n            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)\n            AND e.DataSource LIKE '%Self-Healer%'\n            ORDER BY f.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.762557", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 329, "original_content": "SELECT \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END as Category,\n            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness\n        FROM REF_Fact f\n        WHERE f.DataSource LIKE '%Self-Healer%'\n        GROUP BY \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END", "modified_content": "SELECT \n            1\n        FROM REF_Fact f\n        WHERE f.DataSource LIKE '%Self-Healer%'\n        GROUP BY \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: LIKE, f, THEN, Name, JSON_Parsing, Network, Category, Configuration, CASE, END, AvgEffectiveness, AVG, JSON, WHEN, Other, ValidityRating, ELSE, FLOAT, Workflow, Database, CAST"}, {"timestamp": "2025-08-18T21:35:53.764603", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 329, "original_content": "SELECT \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END as Category,\n            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness\n        FROM REF_Fact f\n        WHERE f.DataSource LIKE '%Self-Healer%'\n        GROUP BY \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END", "modified_content": "SELECT \n            1\n        FROM REF_Fact f\n        WHERE f.DataSource LIKE '%Self-Healer%'\n        GROUP BY \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: LIKE, f, THEN, Name, JSON_Parsing, Network, Category, Configuration, CASE, END, AvgEffectiveness, AVG, JSON, WHEN, Other, ValidityRating, ELSE, FLOAT, Workflow, Database, CAST"}, {"timestamp": "2025-08-18T21:35:53.771182", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 241, "original_content": "INSERT INTO REF_Fact (Name, ValidityRating, DataSource) \n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Fact WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: "}, {"timestamp": "2025-08-18T21:35:53.773182", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 241, "original_content": "INSERT INTO REF_Fact (Name, ValidityRating, DataSource) \n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Fact WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: "}, {"timestamp": "2025-08-18T21:35:53.774685", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 267, "original_content": "INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)\n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Evidence WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, Columns: "}, {"timestamp": "2025-08-18T21:35:53.774685", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 267, "original_content": "INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)\n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Evidence WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, Columns: "}, {"timestamp": "2025-08-18T21:35:53.780418", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 374, "original_content": "SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ?\n            GROUP BY f.ID, f.ValidityRating", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ?\n            GROUP BY f.ID, f.ValidityRating", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.782953", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 374, "original_content": "SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ?\n            GROUP BY f.ID, f.ValidityRating", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ?\n            GROUP BY f.ID, f.ValidityRating", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.786671", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 396, "original_content": "UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Fact WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.787691", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 410, "original_content": "SELECT ID FROM REF_Entity WHERE Name = ?", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.790796", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 419, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            FROM REF_Fact f\n            WHERE f.DataSource LIKE ?\n            ORDER BY f.CreateDate DESC", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            WHERE f.DataSource LIKE ?\n            ORDER BY f.CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.794220", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 419, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            FROM REF_Fact f\n            WHERE f.DataSource LIKE ?\n            ORDER BY f.CreateDate DESC", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            WHERE f.DataSource LIKE ?\n            ORDER BY f.CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.794318", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 430, "original_content": "SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate\n                FROM REF_Evidence e\n                WHERE e.FactID = ?", "modified_content": "SELECT 1\n                FROM REF_Evidence e\n                WHERE e.FactID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.799274", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 430, "original_content": "SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate\n                FROM REF_Evidence e\n                WHERE e.FactID = ?", "modified_content": "SELECT 1\n                FROM REF_Evidence e\n                WHERE e.FactID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.802851", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 439, "original_content": "SELECT a.Name as Attribute<PERSON><PERSON>, ev.EntityValue, eav.CreateDate\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?", "modified_content": "SELECT 1\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.805973", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 439, "original_content": "SELECT a.Name as Attribute<PERSON><PERSON>, ev.EntityValue, eav.CreateDate\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?", "modified_content": "SELECT 1\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.808457", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 465, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n                   COUNT(e.ID) as EvidenceCount\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            ORDER BY f.ValidityRating DESC, f.CreateDate DESC", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            ORDER BY f.ValidityRating DESC, f.CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.811541", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 465, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n                   COUNT(e.ID) as EvidenceCount\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            ORDER BY f.ValidityRating DESC, f.CreateDate DESC", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            ORDER BY f.ValidityRating DESC, f.CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.813831", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 477, "original_content": "SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate\n            FROM REF_Opinion o\n            WHERE o.Name LIKE ? OR o.Opinion LIKE ?\n            ORDER BY o.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Opinion o\n            WHERE o.Name LIKE ? OR o.Opinion LIKE ?\n            ORDER BY o.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.816608", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 477, "original_content": "SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate\n            FROM REF_Opinion o\n            WHERE o.Name LIKE ? OR o.Opinion LIKE ?\n            ORDER BY o.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Opinion o\n            WHERE o.Name LIKE ? OR o.Opinion LIKE ?\n            ORDER BY o.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.818525", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 552, "original_content": "SELECT ID FROM REF_Category WHERE Name = ?", "modified_content": "SELECT 1 FROM REF_Category WHERE Name = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.821101", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 559, "original_content": "INSERT INTO REF_Category (Name) OUTPUT INSERTED.ID VALUES (?)", "modified_content": "SELECT 1 FROM REF_Category WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Category, Columns: "}, {"timestamp": "2025-08-18T21:35:53.824815", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 566, "original_content": "SELECT ID FROM REF_Entity WHERE Name = ?", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.826938", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 573, "original_content": "INSERT INTO REF_Entity (Name) OUTPUT INSERTED.ID VALUES (?)", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:53.829452", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 580, "original_content": "SELECT ID FROM REF_Attribute WHERE Name = ?", "modified_content": "SELECT 1 FROM REF_Attribute WHERE Name = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.832990", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 587, "original_content": "INSERT INTO REF_Attribute (Name) OUTPUT INSERTED.ID VALUES (?)", "modified_content": "SELECT 1 FROM REF_Attribute WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Attribute, Columns: "}, {"timestamp": "2025-08-18T21:35:53.835989", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 594, "original_content": "SELECT ID FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?", "modified_content": "SELECT 1 FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.836989", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 602, "original_content": "INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)\n            OUTPUT INSERTED.ID\n            VALUES (?, ?, ?, ?)", "modified_content": "SELECT 1 FROM REF_EntityValue WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: "}, {"timestamp": "2025-08-18T21:35:53.836989", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 602, "original_content": "INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)\n            OUTPUT INSERTED.ID\n            VALUES (?, ?, ?, ?)", "modified_content": "SELECT 1 FROM REF_EntityValue WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: "}, {"timestamp": "2025-08-18T21:35:53.836989", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 610, "original_content": "INSERT INTO REF_EntityValue (Name, EntityValue) OUTPUT INSERTED.ID VALUES (?, ?)", "modified_content": "SELECT 1 FROM REF_EntityValue WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: "}, {"timestamp": "2025-08-18T21:35:53.836989", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 620, "original_content": "SELECT ID FROM XRF_EntityAttributeValue\n        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?", "modified_content": "SELECT 1 FROM XRF_EntityAttributeValue\n        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.849488", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 620, "original_content": "SELECT ID FROM XRF_EntityAttributeValue\n        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?", "modified_content": "SELECT 1 FROM XRF_EntityAttributeValue\n        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.853138", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 630, "original_content": "INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)\n        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)", "modified_content": "SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:53.855645", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 630, "original_content": "INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)\n        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)", "modified_content": "SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:53.863222", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 855, "original_content": "SELECT ID, Name, CreateDate\n            FROM REF_Entity\n            WHERE Name = ? OR Name LIKE ?", "modified_content": "SELECT 1\n            FROM REF_Entity\n            WHERE Name = ? OR Name LIKE ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.865528", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 855, "original_content": "SELECT ID, Name, CreateDate\n            FROM REF_Entity\n            WHERE Name = ? OR Name LIKE ?", "modified_content": "SELECT 1\n            FROM REF_Entity\n            WHERE Name = ? OR Name LIKE ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.865528", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 872, "original_content": "SELECT\n                a.Name as Attribute<PERSON><PERSON>,\n                ev.EntityValue as AttributeV<PERSON><PERSON>,\n                ev.NumericValue,\n                ev.ValueUnits,\n                ev.Name as ValueName,\n                eav.CreateDate\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?\n            ORDER BY a.Name", "modified_content": "SELECT\n                1\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?\n            ORDER BY a.Name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.872197", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 872, "original_content": "SELECT\n                a.Name as Attribute<PERSON><PERSON>,\n                ev.EntityValue as AttributeV<PERSON><PERSON>,\n                ev.NumericValue,\n                ev.ValueUnits,\n                ev.Name as ValueName,\n                eav.CreateDate\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?\n            ORDER BY a.Name", "modified_content": "SELECT\n                1\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?\n            ORDER BY a.Name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.875702", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 914, "original_content": "SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            FROM REF_Fact f\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            ORDER BY f.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.877898", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 914, "original_content": "SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            FROM REF_Fact f\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Fact f\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            ORDER BY f.ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.880787", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 926, "original_content": "SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate\n            FROM REF_Evidence e\n            WHERE e.Name LIKE ? OR e.DataSource LIKE ?\n            ORDER BY e.CreateDate DESC", "modified_content": "SELECT 1\n            FROM REF_Evidence e\n            WHERE e.Name LIKE ? OR e.DataSource LIKE ?\n            ORDER BY e.CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.883787", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 926, "original_content": "SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate\n            FROM REF_Evidence e\n            WHERE e.Name LIKE ? OR e.DataSource LIKE ?\n            ORDER BY e.CreateDate DESC", "modified_content": "SELECT 1\n            FROM REF_Evidence e\n            WHERE e.Name LIKE ? OR e.DataSource LIKE ?\n            ORDER BY e.CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.887399", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 991, "original_content": "SELECT Name, ValidityRating, DataSource, CreateDate\n            FROM REF_Fact\n            WHERE Name LIKE ? OR DataSource LIKE ?\n            ORDER BY ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Fact\n            WHERE Name LIKE ? OR DataSource LIKE ?\n            ORDER BY ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.890403", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 991, "original_content": "SELECT Name, ValidityRating, DataSource, CreateDate\n            FROM REF_Fact\n            WHERE Name LIKE ? OR DataSource LIKE ?\n            ORDER BY ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Fact\n            WHERE Name LIKE ? OR DataSource LIKE ?\n            ORDER BY ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.894559", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 1009, "original_content": "SELECT Name, ValidityRating, Opinion, DataSource, CreateDate\n            FROM REF_Opinion\n            WHERE Name LIKE ? OR Opinion LIKE ?\n            ORDER BY ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Opinion\n            WHERE Name LIKE ? OR Opinion LIKE ?\n            ORDER BY ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.897676", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 1009, "original_content": "SELECT Name, ValidityRating, Opinion, DataSource, CreateDate\n            FROM REF_Opinion\n            WHERE Name LIKE ? OR Opinion LIKE ?\n            ORDER BY ValidityRating DESC", "modified_content": "SELECT 1\n            FROM REF_Opinion\n            WHERE Name LIKE ? OR Opinion LIKE ?\n            ORDER BY ValidityRating DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.900104", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 1021, "original_content": "SELECT DISTINCT e.Name, e.CreateDate\n            FROM REF_Entity e\n            WHERE e.Name LIKE 'Session_%'\n            AND EXISTS (\n                SELECT 1 FROM XRF_EntityAttributeValue eav\n                JOIN REF_Attribute a ON eav.AttributeID = a.ID\n                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n                WHERE eav.EntityID = e.ID\n                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)\n            )\n            ORDER BY e.CreateDate DESC", "modified_content": "SELECT 1\n            FROM REF_Entity e\n            WHERE e.Name LIKE 'Session_%'\n            AND EXISTS (\n                SELECT 1 FROM XRF_EntityAttributeValue eav\n                JOIN REF_Attribute a ON eav.AttributeID = a.ID\n                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n                WHERE eav.EntityID = e.ID\n                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)\n            )\n            ORDER BY e.CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.903848", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 1021, "original_content": "SELECT DISTINCT e.Name, e.CreateDate\n            FROM REF_Entity e\n            WHERE e.Name LIKE 'Session_%'\n            AND EXISTS (\n                SELECT 1 FROM XRF_EntityAttributeValue eav\n                JOIN REF_Attribute a ON eav.AttributeID = a.ID\n                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n                WHERE eav.EntityID = e.ID\n                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)\n            )\n            ORDER BY e.CreateDate DESC", "modified_content": "SELECT 1\n            FROM REF_Entity e\n            WHERE e.Name LIKE 'Session_%'\n            AND EXISTS (\n                SELECT 1 FROM XRF_EntityAttributeValue eav\n                JOIN REF_Attribute a ON eav.AttributeID = a.ID\n                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n                WHERE eav.EntityID = e.ID\n                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)\n            )\n            ORDER BY e.CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.906848", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 34, "original_content": "SELECT COLUMN_NAME, DATA_TYPE, IS_NULL<PERSON>LE, COLUMN_DEFAULT\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = REF_EntityValue\n        ORDER BY ORDINAL_POSITION", "modified_content": "SELECT 1\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = REF_EntityValue\n        ORDER BY ORDINAL_POSITION", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_EntityValue'. (207) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:53.914538", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 34, "original_content": "SELECT COLUMN_NAME, DATA_TYPE, IS_NULL<PERSON>LE, COLUMN_DEFAULT\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = REF_EntityValue\n        ORDER BY ORDINAL_POSITION", "modified_content": "SELECT 1\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = REF_EntityValue\n        ORDER BY ORDINAL_POSITION", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_EntityValue'. (207) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:53.925778", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 66, "original_content": "SELECT name, create_date, modify_date\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'\n        ORDER BY name", "modified_content": "SELECT 1\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'\n        ORDER BY name", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name, modify_date, create_date"}, {"timestamp": "2025-08-18T21:35:53.931646", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 66, "original_content": "SELECT name, create_date, modify_date\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'\n        ORDER BY name", "modified_content": "SELECT 1\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'\n        ORDER BY name", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name, modify_date, create_date"}, {"timestamp": "2025-08-18T21:35:53.931646", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 93, "original_content": "SELECT name FROM sys.procedures WHERE name = '{proc_name}'", "modified_content": "SELECT 1 FROM sys.procedures WHERE name = '{proc_name}'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name"}, {"timestamp": "2025-08-18T21:35:53.931646", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 106, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'\n        ORDER BY TABLE_NAME", "modified_content": "SELECT 1 \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'\n        ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE"}, {"timestamp": "2025-08-18T21:35:53.931646", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 106, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'\n        ORDER BY TABLE_NAME", "modified_content": "SELECT 1 \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'\n        ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE"}, {"timestamp": "2025-08-18T21:35:53.949934", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 119, "original_content": "SELECT COUNT(*) as row_count FROM {table_name}", "modified_content": "SELECT 1 FROM {table_name}", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.957268", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_schema.py", "line_number": 9, "original_content": "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME", "modified_content": "SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE"}, {"timestamp": "2025-08-18T21:35:53.961271", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_schema.py", "line_number": 17, "original_content": "SELECT TOP 5 * FROM REF_Entity WHERE Name LIKE 'Session_%'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name LIKE 'Session_%'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:53.962272", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_schema.py", "line_number": 26, "original_content": "SELECT TOP 10 * FROM REF_Entity ORDER BY CreateDate DESC", "modified_content": "SELECT 1 FROM REF_Entity ORDER BY CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:53.962272", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_schema.py", "line_number": 33, "original_content": "SELECT TOP 5 * FROM XRF_EntityAttributeValue", "modified_content": "SELECT 1 FROM XRF_EntityAttributeValue", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:53.978008", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\create_analytics_procedure.py", "line_number": 48, "original_content": "SELECT name, create_date \n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT 1 \n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name, create_date"}, {"timestamp": "2025-08-18T21:35:53.984034", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\create_analytics_procedure.py", "line_number": 48, "original_content": "SELECT name, create_date \n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT 1 \n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name, create_date"}, {"timestamp": "2025-08-18T21:35:53.988206", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\create_schema_procedure.sql", "line_number": 113, "original_content": "SELECT @count = COUNT(*) FROM", "modified_content": "SELECT @count = COUNT(*) FROM", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable \"@count\". (137) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:53.997381", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:53.999380", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.001400", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.005820", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.010255", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 117, "original_content": "SELECT * FROM \\1 ORDER BY", "modified_content": "SELECT 1 FROM \\1 ORDER BY", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '\\\\1'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.016534", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\check_database_tables.py", "line_number": 37, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' \n        AND TABLE_NAME LIKE 'REF_%' \n        ORDER BY TABLE_NAME", "modified_content": "SELECT 1 \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' \n        AND TABLE_NAME LIKE 'REF_%' \n        ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE"}, {"timestamp": "2025-08-18T21:35:54.021527", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\check_database_tables.py", "line_number": 37, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' \n        AND TABLE_NAME LIKE 'REF_%' \n        ORDER BY TABLE_NAME", "modified_content": "SELECT 1 \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' \n        AND TABLE_NAME LIKE 'REF_%' \n        ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE"}, {"timestamp": "2025-08-18T21:35:54.025526", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\check_database_tables.py", "line_number": 77, "original_content": "SELECT TOP 1 * FROM REF_Entity", "modified_content": "SELECT 1 FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.025526", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\check_database_tables.py", "line_number": 87, "original_content": "SELECT TOP 1 * FROM REF_Entity", "modified_content": "SELECT 1 FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.025526", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\database_repair_logger.py", "line_number": 0, "original_content": "Contains 2 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.038123", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\database_repair_logger.py", "line_number": 268, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = 'test'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-18T21:35:54.041139", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\database_repair_logger.py", "line_number": 269, "original_content": "SELECT 1 FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = 'test'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-18T21:35:54.046042", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 31, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = 'test'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-18T21:35:54.048960", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 35, "original_content": "INSERT INTO REF_Fact (EntityID, Name, Value) \n             VALUES (?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Fact WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: "}, {"timestamp": "2025-08-18T21:35:54.053320", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 35, "original_content": "INSERT INTO REF_Fact (EntityID, Name, Value) \n             VALUES (?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Fact WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: "}, {"timestamp": "2025-08-18T21:35:54.057966", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 39, "original_content": "SELECT your option from the menu", "modified_content": "SELECT 1 from the menu", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.060821", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 53, "original_content": "SELECT * FROM users WHERE active = 1", "modified_content": "SELECT 1 FROM users WHERE active = 1", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.063820", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 54, "original_content": "UPDATE users SET status = ? WHERE id = ?", "modified_content": "SELECT 1 FROM users WHERE id = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.068473", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 55, "original_content": "DELETE FROM logs WHERE created_at < ?", "modified_content": "SELECT 1 FROM logs WHERE created_at < ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.074040", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.076351", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Attributes'", "modified_content": "Replaced with 'REF_Attribute'", "rule_applied": "Table name fix: REF_Attributes → REF_Attribute", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.080084", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_EntityValues'", "modified_content": "Replaced with 'REF_EntityValue'", "rule_applied": "Table name fix: REF_EntityValues → REF_EntityValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.082888", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.085948", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.088949", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.088949", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.093908", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'XRF_Entity_Attribute_Value'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.097911", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'XRF_Entity_AttributeValue'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_AttributeValue → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.114506", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 208, "original_content": "SELECT * FROM REF_Entity WHERE Name = \\'test\\'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = \\'test\\'", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\\\'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.117599", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 209, "original_content": "INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)", "modified_content": "SELECT 1 FROM REF_Fact WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: "}, {"timestamp": "2025-08-18T21:35:54.118595", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 209, "original_content": "INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)", "modified_content": "SELECT 1 FROM REF_Fact WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: "}, {"timestamp": "2025-08-18T21:35:54.124198", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 210, "original_content": "UPDATE REF_Entity SET Name = ? WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Entity WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.130064", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 211, "original_content": "DELETE FROM REF_Entity WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Entity WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.134717", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 212, "original_content": "SELECT COUNT(*) FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID", "modified_content": "SELECT 1 FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.137617", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 218, "original_content": "SELECT your option FROM the menu", "modified_content": "SELECT 1 FROM the menu", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.142502", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 228, "original_content": "SELECT * FROM", "modified_content": "SELECT * FROM", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'FROM'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.147513", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 230, "original_content": "SELECT name FROM users WHERE active = 1 ORDER BY name", "modified_content": "SELECT 1 FROM users WHERE active = 1 ORDER BY name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.154185", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.157477", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.157477", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.163466", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.166466", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.171709", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 109, "original_content": "SELECT 1 FROM {table_name} WHERE 1=0", "modified_content": "SELECT 1 FROM {table_name} WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.176375", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 120, "original_content": "SELECT 1 FROM {table_name} WHERE {where_clause}", "modified_content": "SELECT 1 FROM {table_name} WHERE {where_clause}", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.180218", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 122, "original_content": "SELECT 1 FROM {table_name} WHERE 1=0", "modified_content": "SELECT 1 FROM {table_name} WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.182217", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 133, "original_content": "SELECT 1 FROM {table_name} WHERE {where_clause}", "modified_content": "SELECT 1 FROM {table_name} WHERE {where_clause}", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.187709", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 135, "original_content": "SELECT 1 FROM {table_name} WHERE 1=0", "modified_content": "SELECT 1 FROM {table_name} WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.190782", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 142, "original_content": "SELECT 1 FROM {table_name} WHERE 1=0", "modified_content": "SELECT 1 FROM {table_name} WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.196952", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 250, "original_content": "SELECT 1 FROM {tables[0]} WHERE 1=0", "modified_content": "SELECT 1 FROM {tables[0]} WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.203979", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_refined_detection.py", "line_number": 39, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = 'test'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-18T21:35:54.209480", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_refined_detection.py", "line_number": 42, "original_content": "SELECT your option from the menu", "modified_content": "SELECT 1 from the menu", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.213481", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_refined_detection.py", "line_number": 45, "original_content": "INSERT INTO REF_Fact (EntityID, Name, Value) \n    VALUES (?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Fact WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: "}, {"timestamp": "2025-08-18T21:35:54.213481", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_refined_detection.py", "line_number": 45, "original_content": "INSERT INTO REF_Fact (EntityID, Name, Value) \n    VALUES (?, ?, ?)", "modified_content": "SELECT 1 FROM REF_Fact WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: "}, {"timestamp": "2025-08-18T21:35:54.213481", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_refined_detection.py", "line_number": 54, "original_content": "UPDATE REF_Entity SET Name = ? WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Entity WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.227827", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_schema_validator.py", "line_number": 36, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE'\n        ORDER BY TABLE_NAME", "modified_content": "SELECT 1 \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE'\n        ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE"}, {"timestamp": "2025-08-18T21:35:54.237243", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_schema_validator.py", "line_number": 36, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE'\n        ORDER BY TABLE_NAME", "modified_content": "SELECT 1 \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE'\n        ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE"}, {"timestamp": "2025-08-18T21:35:54.243360", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_database_schema_references.py", "line_number": 111, "original_content": "SELECT TABLE_NAME \n            FROM INFORMATION_SCHEMA.TABLES \n            WHERE TABLE_TYPE = 'BASE TABLE'\n            ORDER BY TABLE_NAME", "modified_content": "SELECT 1 \n            FROM INFORMATION_SCHEMA.TABLES \n            WHERE TABLE_TYPE = 'BASE TABLE'\n            ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE"}, {"timestamp": "2025-08-18T21:35:54.257463", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_database_schema_references.py", "line_number": 111, "original_content": "SELECT TABLE_NAME \n            FROM INFORMATION_SCHEMA.TABLES \n            WHERE TABLE_TYPE = 'BASE TABLE'\n            ORDER BY TABLE_NAME", "modified_content": "SELECT 1 \n            FROM INFORMATION_SCHEMA.TABLES \n            WHERE TABLE_TYPE = 'BASE TABLE'\n            ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE"}, {"timestamp": "2025-08-18T21:35:54.268566", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_database_schema_references.py", "line_number": 131, "original_content": "SELECT COLUMN_NAME, DATA_TYPE \n                FROM INFORMATION_SCHEMA.COLUMNS \n                WHERE TABLE_NAME = '{table_name}'\n                ORDER BY ORDINAL_POSITION", "modified_content": "SELECT 1 \n                FROM INFORMATION_SCHEMA.COLUMNS \n                WHERE TABLE_NAME = '{table_name}'\n                ORDER BY ORDINAL_POSITION", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, DATA_TYPE, COLUMN_NAME"}, {"timestamp": "2025-08-18T21:35:54.269652", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_database_schema_references.py", "line_number": 131, "original_content": "SELECT COLUMN_NAME, DATA_TYPE \n                FROM INFORMATION_SCHEMA.COLUMNS \n                WHERE TABLE_NAME = '{table_name}'\n                ORDER BY ORDINAL_POSITION", "modified_content": "SELECT 1 \n                FROM INFORMATION_SCHEMA.COLUMNS \n                WHERE TABLE_NAME = '{table_name}'\n                ORDER BY ORDINAL_POSITION", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, DATA_TYPE, COLUMN_NAME"}, {"timestamp": "2025-08-18T21:35:54.286291", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_stored_procedures.py", "line_number": 12, "original_content": "SELECT \n            name,\n            create_date,\n            modify_date\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT \n            1\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name, modify_date, create_date"}, {"timestamp": "2025-08-18T21:35:54.286291", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_stored_procedures.py", "line_number": 12, "original_content": "SELECT \n            name,\n            create_date,\n            modify_date\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT \n            1\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name, modify_date, create_date"}, {"timestamp": "2025-08-18T21:35:54.298743", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_stored_procedures.py", "line_number": 83, "original_content": "SELECT name FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT 1 FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name"}, {"timestamp": "2025-08-18T21:35:54.313747", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_stored_procedures.py", "line_number": 83, "original_content": "SELECT name FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT 1 FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name"}, {"timestamp": "2025-08-18T21:35:54.346299", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 45, "original_content": "SELECT your option from the menu", "modified_content": "SELECT 1 from the menu", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.350662", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 66, "original_content": "SELECT * FROM REF_Entity", "modified_content": "SELECT 1 FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.353661", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 67, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = 'test'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-18T21:35:54.358169", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 68, "original_content": "SELECT COUNT(*) FROM REF_Entity", "modified_content": "SELECT 1 FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: COUNT"}, {"timestamp": "2025-08-18T21:35:54.363027", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 71, "original_content": "INSERT INTO REF_Entity (Name, Value) VALUES (?, ?)", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.367939", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 72, "original_content": "INSERT INTO REF_Fact (EntityID, Name, Value) VALUES (1, 'test', 'value')", "modified_content": "SELECT 1 FROM REF_Fact WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: "}, {"timestamp": "2025-08-18T21:35:54.372819", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 75, "original_content": "UPDATE REF_Entity SET Name = ? WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Entity WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.376749", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 76, "original_content": "UPDATE REF_Entity SET Status = 'active' WHERE Name = 'test'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = 'test'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-18T21:35:54.378750", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 79, "original_content": "DELETE FROM REF_Entity WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Entity WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.386816", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 80, "original_content": "DELETE FROM REF_Entity WHERE CreatedDate < '2023-01-01'", "modified_content": "SELECT 1 FROM REF_Entity WHERE CreatedDate < '2023-01-01'", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.391323", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 90, "original_content": "SELECT e.ID, e.Name, f.Value\n    FROM REF_Entity e\n    JOIN REF_Fact f ON e.ID = f.EntityID\n    WHERE e.Status = 'active'\n    ORDER BY e.Name", "modified_content": "SELECT 1\n    FROM REF_Entity e\n    JOIN REF_Fact f ON e.ID = f.EntityID\n    WHERE e.Status = 'active'\n    ORDER BY e.Name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)\")"}, {"timestamp": "2025-08-18T21:35:54.397805", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 90, "original_content": "SELECT e.ID, e.Name, f.Value\n    FROM REF_Entity e\n    JOIN REF_Fact f ON e.ID = f.EntityID\n    WHERE e.Status = 'active'\n    ORDER BY e.Name", "modified_content": "SELECT 1\n    FROM REF_Entity e\n    JOIN REF_Fact f ON e.ID = f.EntityID\n    WHERE e.Status = 'active'\n    ORDER BY e.Name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)\")"}, {"timestamp": "2025-08-18T21:35:54.401808", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 99, "original_content": "INSERT INTO REF_Entity (Name, Description, Status, CreatedDate)\n    VALUES \n        ('Entity1', 'First entity', 'active', GETDATE()),\n        ('Entity2', 'Second entity', 'inactive', GETDATE()),\n        ('Entity3', 'Third entity', 'active', GETDATE())", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.402881", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 99, "original_content": "INSERT INTO REF_Entity (Name, Description, Status, CreatedDate)\n    VALUES \n        ('Entity1', 'First entity', 'active', GETDATE()),\n        ('Entity2', 'Second entity', 'inactive', GETDATE()),\n        ('Entity3', 'Third entity', 'active', GETDATE())", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.402881", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 108, "original_content": "UPDATE REF_Entity \n    SET Status = 'archived',\n        ModifiedDate = GETDATE()\n    WHERE CreatedDate < DATEADD(year, -1, GETDATE())\n        AND Status = 'inactive'", "modified_content": "SELECT 1 FROM REF_Entity WHERE CreatedDate < DATEADD(year, -1, GETDATE())\n        AND Status = 'inactive'", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)\")"}, {"timestamp": "2025-08-18T21:35:54.416855", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 108, "original_content": "UPDATE REF_Entity \n    SET Status = 'archived',\n        ModifiedDate = GETDATE()\n    WHERE CreatedDate < DATEADD(year, -1, GETDATE())\n        AND Status = 'inactive'", "modified_content": "SELECT 1 FROM REF_Entity WHERE CreatedDate < DATEADD(year, -1, GETDATE())\n        AND Status = 'inactive'", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)\")"}, {"timestamp": "2025-08-18T21:35:54.420992", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 117, "original_content": "DELETE FROM REF_Fact \n    WHERE EntityID IN (\n        SELECT ID FROM REF_Entity \n        WHERE Status = 'deleted'\n    )", "modified_content": "SELECT 1 FROM REF_Fact WHERE EntityID IN (\n        SELECT ID FROM REF_Entity \n        WHERE Status = 'deleted'\n    )", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)\")"}, {"timestamp": "2025-08-18T21:35:54.427941", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 117, "original_content": "DELETE FROM REF_Fact \n    WHERE EntityID IN (\n        SELECT ID FROM REF_Entity \n        WHERE Status = 'deleted'\n    )", "modified_content": "SELECT 1 FROM REF_Fact WHERE EntityID IN (\n        SELECT ID FROM REF_Entity \n        WHERE Status = 'deleted'\n    )", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)\")"}, {"timestamp": "2025-08-18T21:35:54.430940", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 135, "original_content": "SELECT \n        e.ID,\n        e.Name,\n        e.Description,\n        \n        f.Value,\n        f.CreatedDate\n        \n    FROM REF_Entity e\n    \n    LEFT JOIN REF_Fact f \n        ON e.ID = f.EntityID\n        \n    WHERE e.Status = 'active'\n        AND f.Value IS NOT NULL\n        \n    ORDER BY \n        e.Name ASC,\n        f.CreatedDate DESC", "modified_content": "SELECT \n        1\n        \n    FROM REF_Entity e\n    \n    LEFT JOIN REF_Fact f \n        ON e.ID = f.EntityID\n        \n    WHERE e.Status = 'active'\n        AND f.Value IS NOT NULL\n        \n    ORDER BY \n        e.Name ASC,\n        f.CreatedDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name '<PERSON>tityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207)\")"}, {"timestamp": "2025-08-18T21:35:54.439675", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 135, "original_content": "SELECT \n        e.ID,\n        e.Name,\n        e.Description,\n        \n        f.Value,\n        f.CreatedDate\n        \n    FROM REF_Entity e\n    \n    LEFT JOIN REF_Fact f \n        ON e.ID = f.EntityID\n        \n    WHERE e.Status = 'active'\n        AND f.Value IS NOT NULL\n        \n    ORDER BY \n        e.Name ASC,\n        f.CreatedDate DESC", "modified_content": "SELECT \n        1\n        \n    FROM REF_Entity e\n    \n    LEFT JOIN REF_Fact f \n        ON e.ID = f.EntityID\n        \n    WHERE e.Status = 'active'\n        AND f.Value IS NOT NULL\n        \n    ORDER BY \n        e.Name ASC,\n        f.CreatedDate DESC", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name '<PERSON>tityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207)\")"}, {"timestamp": "2025-08-18T21:35:54.440675", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 158, "original_content": "INSERT INTO REF_Entity \n    (\n        Name,\n        Description,\n        Status,\n        CreatedDate\n    )\n    VALUES \n    (\n        ?,\n        ?,\n        'active',\n        GETDATE()\n    )", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.440675", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 158, "original_content": "INSERT INTO REF_Entity \n    (\n        Name,\n        Description,\n        Status,\n        CreatedDate\n    )\n    VALUES \n    (\n        ?,\n        ?,\n        'active',\n        GETDATE()\n    )", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.440675", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 194, "original_content": "SELECT 'This contains SELECT keyword' FROM REF_Entity", "modified_content": "SELECT 1 FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: contains, SELECT, This, keyword"}, {"timestamp": "2025-08-18T21:35:54.456927", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 195, "original_content": "INSERT INTO REF_Entity (Name) VALUES ('UPDATE this later')", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, this, Columns: "}, {"timestamp": "2025-08-18T21:35:54.462589", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 198, "original_content": "SELECT * FROM REF_Entity  -- This is a comment with SELECT\n    WHERE Name = 'test'  /* Another comment with INSERT */", "modified_content": "SELECT 1 FROM REF_Entity  -- This is a comment with SELECT\n    WHERE Name = 'test'  /* Another comment with INSERT */", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-18T21:35:54.465013", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 198, "original_content": "SELECT * FROM REF_Entity  -- This is a comment with SELECT\n    WHERE Name = 'test'  /* Another comment with INSERT */", "modified_content": "SELECT 1 FROM REF_Entity  -- This is a comment with SELECT\n    WHERE Name = 'test'  /* Another comment with INSERT */", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-18T21:35:54.469307", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 205, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = 'test'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-18T21:35:54.475108", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 208, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'It\\\\'s a test'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = 'It\\\\'s a test'", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 's'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Unclosed quotation mark after the character string ''. (105)\")"}, {"timestamp": "2025-08-18T21:35:54.479231", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 211, "original_content": "SELECT \n        e1.ID as Entity1_ID,\n        e1.Name as Entity1_Name,\n        e2.ID as Entity2_ID,\n        e2.Name as Entity2_Name,\n        f1.Value as Fact1_Value,\n        f2.Value as Fact2_Value\n    FROM REF_Entity e1\n    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID\n    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID\n    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID\n    WHERE e1.Status = 'active'\n        AND f1.Type = 'primary'\n        AND (e2.Status IS NULL OR e2.Status = 'active')\n    ORDER BY e1.Name, e2.Name\n    LIMIT 100", "modified_content": "SELECT \n        1\n    FROM REF_Entity e1\n    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID\n    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID\n    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID\n    WHERE e1.Status = 'active'\n        AND f1.Type = 'primary'\n        AND (e2.Status IS NULL OR e2.Status = 'active')\n    ORDER BY e1.Name, e2.Name\n    LIMIT 100", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'LIMIT'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.486380", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 211, "original_content": "SELECT \n        e1.ID as Entity1_ID,\n        e1.Name as Entity1_Name,\n        e2.ID as Entity2_ID,\n        e2.Name as Entity2_Name,\n        f1.Value as Fact1_Value,\n        f2.Value as Fact2_Value\n    FROM REF_Entity e1\n    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID\n    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID\n    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID\n    WHERE e1.Status = 'active'\n        AND f1.Type = 'primary'\n        AND (e2.Status IS NULL OR e2.Status = 'active')\n    ORDER BY e1.Name, e2.Name\n    LIMIT 100", "modified_content": "SELECT \n        1\n    FROM REF_Entity e1\n    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID\n    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID\n    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID\n    WHERE e1.Status = 'active'\n        AND f1.Type = 'primary'\n        AND (e2.Status IS NULL OR e2.Status = 'active')\n    ORDER BY e1.Name, e2.Name\n    LIMIT 100", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'LIMIT'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.492343", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 236, "original_content": "SELECT * FROM users WHERE id = ?", "modified_content": "SELECT 1 FROM users WHERE id = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.496638", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 237, "original_content": "INSERT INTO users (name, email) VALUES (?, ?)", "modified_content": "SELECT 1 FROM users WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.497638", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 238, "original_content": "UPDATE users SET email = ? WHERE id = ?", "modified_content": "SELECT 1 FROM users WHERE id = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.509069", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 239, "original_content": "DELETE FROM users WHERE id = ?", "modified_content": "SELECT 1 FROM users WHERE id = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.514587", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 243, "original_content": "SELECT COUNT(*) FROM transactions WHERE DATE(created_at) = CURDATE()", "modified_content": "SELECT 1 FROM transactions WHERE DATE(created_at) = CURDATE()", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]'DATE' is not a recognized built-in function name. (195) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.519269", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 244, "original_content": "SELECT SUM(amount) FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())", "modified_content": "SELECT 1 FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]'CURDATE' is not a recognized built-in function name. (195) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.520773", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 269, "original_content": "SELECT * FROM REF_Entity", "modified_content": "SELECT 1 FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.520773", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 285, "original_content": "SELECT * FROM", "modified_content": "SELECT * FROM", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'FROM'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.534802", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 286, "original_content": "INSERT INTO REF_Entity", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.535802", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 287, "original_content": "UPDATE REF_Entity SET", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.551441", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 291, "original_content": "SELECT INSERT UPDATE DELETE FROM WHERE", "modified_content": "SELECT 1 FROM WHERE", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'WHERE'. (156) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.555641", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 305, "original_content": "SELECT COUNT(*) FROM REF_Entity WHERE Status = 'test'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Status = 'test'", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.561021", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 309, "original_content": "SELECT \n        'Test completed successfully' as message,\n        COUNT(*) as total_entities\n    FROM REF_Entity", "modified_content": "SELECT \n        1\n    FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: total_entities, successfully, COUNT, message, Test, completed"}, {"timestamp": "2025-08-18T21:35:54.563021", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 309, "original_content": "SELECT \n        'Test completed successfully' as message,\n        COUNT(*) as total_entities\n    FROM REF_Entity", "modified_content": "SELECT \n        1\n    FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: total_entities, successfully, COUNT, message, Test, completed"}, {"timestamp": "2025-08-18T21:35:54.563021", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\simple_test.py", "line_number": 21, "original_content": "SELECT * FROM REF_Entity", "modified_content": "SELECT 1 FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.563021", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\simple_test.py", "line_number": 22, "original_content": "INSERT INTO REF_Entity VALUES (?, ?)", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.582288", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\simple_test.py", "line_number": 23, "original_content": "UPDATE REF_Entity SET Name = ? WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Entity WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.587364", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\simple_test.py", "line_number": 32, "original_content": "SELECT * FROM REF_Entity \nWHERE Status = 'active'", "modified_content": "SELECT 1 FROM REF_Entity \nWHERE Status = 'active'", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.594185", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\simple_test.py", "line_number": 32, "original_content": "SELECT * FROM REF_Entity \nWHERE Status = 'active'", "modified_content": "SELECT 1 FROM REF_Entity \nWHERE Status = 'active'", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.599750", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 27, "original_content": "SELECT * FROM REF_Entity", "modified_content": "SELECT 1 FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.603360", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 28, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = 'test'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-18T21:35:54.613593", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 29, "original_content": "SELECT COUNT(*) FROM REF_Entity", "modified_content": "SELECT 1 FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: COUNT"}, {"timestamp": "2025-08-18T21:35:54.617667", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 30, "original_content": "INSERT INTO REF_Entity (Name, Value) VALUES (?, ?)", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.617667", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 31, "original_content": "INSERT INTO REF_Fact (EntityID, Name, Value) VALUES (1, 'test', 'value')", "modified_content": "SELECT 1 FROM REF_Fact WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: "}, {"timestamp": "2025-08-18T21:35:54.630313", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 32, "original_content": "UPDATE REF_Entity SET Name = ? WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Entity WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.635764", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 33, "original_content": "UPDATE REF_Entity SET Status = 'active' WHERE Name = 'test'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = 'test'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-18T21:35:54.641085", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 34, "original_content": "DELETE FROM REF_Entity WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Entity WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.646804", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 35, "original_content": "DELETE FROM REF_Entity WHERE CreatedDate < '2023-01-01'", "modified_content": "SELECT 1 FROM REF_Entity WHERE CreatedDate < '2023-01-01'", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.650927", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 40, "original_content": "SELECT 'This contains SELECT keyword' FROM REF_Entity", "modified_content": "SELECT 1 FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: contains, SELECT, This, keyword"}, {"timestamp": "2025-08-18T21:35:54.655751", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 41, "original_content": "INSERT INTO REF_Entity (Name) VALUES ('UPDATE this later')", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, this, Columns: "}, {"timestamp": "2025-08-18T21:35:54.665235", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 42, "original_content": "SELECT * FROM REF_Entity WHERE Name = \\\"test\\\"", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = \\\"test\\\"", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\\\'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.667757", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 43, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = 'test'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-18T21:35:54.679086", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 44, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'It\\\\'s a test'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name = 'It\\\\'s a test'", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 's'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Unclosed quotation mark after the character string ''. (105)\")"}, {"timestamp": "2025-08-18T21:35:54.685001", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 47, "original_content": "SELECT * FROM users WHERE id = ?", "modified_content": "SELECT 1 FROM users WHERE id = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.690642", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 48, "original_content": "INSERT INTO users (name, email) VALUES (?, ?)", "modified_content": "SELECT 1 FROM users WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.696836", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 49, "original_content": "UPDATE users SET email = ? WHERE id = ?", "modified_content": "SELECT 1 FROM users WHERE id = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.702954", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 50, "original_content": "DELETE FROM users WHERE id = ?", "modified_content": "SELECT 1 FROM users WHERE id = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.710571", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 51, "original_content": "SELECT COUNT(*) FROM transactions WHERE DATE(created_at) = CURDATE()", "modified_content": "SELECT 1 FROM transactions WHERE DATE(created_at) = CURDATE()", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]'DATE' is not a recognized built-in function name. (195) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.713656", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 52, "original_content": "SELECT SUM(amount) FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())", "modified_content": "SELECT 1 FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]'CURDATE' is not a recognized built-in function name. (195) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.721702", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 67, "original_content": "SELECT your option from the menu", "modified_content": "SELECT 1 from the menu", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.721702", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 72, "original_content": "SELECT * FROM", "modified_content": "SELECT * FROM", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'FROM'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.732402", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 73, "original_content": "INSERT INTO REF_Entity", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.737986", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 74, "original_content": "UPDATE REF_Entity SET", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.737986", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 194, "original_content": "SELECT \n    e.ID,\n    e.Name\n\nFROM REF_Entity e\n\nWHERE e.Status = 'active'\n\nORDER BY e.Name", "modified_content": "SELECT \n    1\n\nFROM REF_Entity e\n\nWHERE e.Status = 'active'\n\nORDER BY e.Name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.752316", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 194, "original_content": "SELECT \n    e.ID,\n    e.Name\n\nFROM REF_Entity e\n\nWHERE e.Status = 'active'\n\nORDER BY e.Name", "modified_content": "SELECT \n    1\n\nFROM REF_Entity e\n\nWHERE e.Status = 'active'\n\nORDER BY e.Name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.755657", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 210, "original_content": "SELECT * FROM users WHERE name = '<PERSON>'", "modified_content": "SELECT 1 FROM users WHERE name = '<PERSON>'", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.763670", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 218, "original_content": "SELECT * FROM REF_Entity", "modified_content": "SELECT 1 FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.768965", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 219, "original_content": "SELECT * FROM REF_Entity WHERE ID = ?", "modified_content": "SELECT 1 FROM REF_Entity WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.780640", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_dashboard_db.py", "line_number": 11, "original_content": "SELECT * FROM REF_Entity WHERE Name LIKE 'Session_%'", "modified_content": "SELECT 1 FROM REF_Entity WHERE Name LIKE 'Session_%'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.789435", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_healing_pipeline.py", "line_number": 257, "original_content": "SELECT COUNT(*) as count FROM REF_Fact", "modified_content": "SELECT 1 FROM REF_Fact", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: COUNT, count"}, {"timestamp": "2025-08-18T21:35:54.796294", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 0, "original_content": "Contains 3 instances of 'XRF_Entity_Attribute_Value'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.802760", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 79, "original_content": "SELECT \n                name,\n                create_date,\n                modify_date,\n                type_desc\n            FROM sys.objects \n            WHERE type = 'P' \n            AND name IN ({})\n            ORDER BY name", "modified_content": "SELECT \n                1\n            FROM sys.objects \n            WHERE type = 'P' \n            AND name IN ({})\n            ORDER BY name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.802760", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 79, "original_content": "SELECT \n                name,\n                create_date,\n                modify_date,\n                type_desc\n            FROM sys.objects \n            WHERE type = 'P' \n            AND name IN ({})\n            ORDER BY name", "modified_content": "SELECT \n                1\n            FROM sys.objects \n            WHERE type = 'P' \n            AND name IN ({})\n            ORDER BY name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.814181", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 129, "original_content": "SELECT TOP 5 ID, Name FROM REF_Entity ORDER BY ID", "modified_content": "SELECT 1 FROM REF_Entity ORDER BY ID", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name, ID"}, {"timestamp": "2025-08-18T21:35:54.815180", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 135, "original_content": "INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)\n                OUTPUT INSERTED.ID, INSERTED.Name\n                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.815180", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 135, "original_content": "INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)\n                OUTPUT INSERTED.ID, INSERTED.Name\n                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())", "modified_content": "SELECT 1 FROM REF_Entity WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-18T21:35:54.833225", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 360, "original_content": "SELECT name FROM sys.objects\n            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'", "modified_content": "SELECT 1 FROM sys.objects\n            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name, type"}, {"timestamp": "2025-08-18T21:35:54.839532", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 360, "original_content": "SELECT name FROM sys.objects\n            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'", "modified_content": "SELECT 1 FROM sys.objects\n            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: sys, Columns: name, type"}, {"timestamp": "2025-08-18T21:35:54.849206", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 393, "original_content": "SELECT TABLE_NAME, TABLE_TYPE\n                FROM INFORMATION_SCHEMA.TABLES\n                WHERE TABLE_TYPE = 'BASE TABLE'\n                AND TABLE_NAME LIKE 'REF_%'\n                ORDER BY TABLE_NAME", "modified_content": "SELECT 1\n                FROM INFORMATION_SCHEMA.TABLES\n                WHERE TABLE_TYPE = 'BASE TABLE'\n                AND TABLE_NAME LIKE 'REF_%'\n                ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE"}, {"timestamp": "2025-08-18T21:35:54.853178", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 393, "original_content": "SELECT TABLE_NAME, TABLE_TYPE\n                FROM INFORMATION_SCHEMA.TABLES\n                WHERE TABLE_TYPE = 'BASE TABLE'\n                AND TABLE_NAME LIKE 'REF_%'\n                ORDER BY TABLE_NAME", "modified_content": "SELECT 1\n                FROM INFORMATION_SCHEMA.TABLES\n                WHERE TABLE_TYPE = 'BASE TABLE'\n                AND TABLE_NAME LIKE 'REF_%'\n                ORDER BY TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE"}, {"timestamp": "2025-08-18T21:35:54.862208", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_mcp_database.py", "line_number": 180, "original_content": "SELECT COUNT(*) as total_rows FROM [{schema_name}].[{table_name}]", "modified_content": "SELECT 1 FROM [{schema_name}].[{table_name}]", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:54.869152", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 0, "original_content": "Contains 6 instances of 'REF_EntityValues'", "modified_content": "Replaced with 'REF_EntityValue'", "rule_applied": "Table name fix: REF_EntityValues → REF_EntityValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-18T21:35:54.875214", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 29, "original_content": "SELECT COLUMN_NAME, DAT<PERSON>_TYPE, IS_NULLABLE\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = 'REF_EntityValue'\n        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')\n        ORDER BY COLUMN_NAME", "modified_content": "SELECT 1\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = 'REF_EntityValue'\n        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')\n        ORDER BY COLUMN_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, IS_NULLABLE, DATA_TYPE, COLUMN_NAME"}, {"timestamp": "2025-08-18T21:35:54.875214", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 29, "original_content": "SELECT COLUMN_NAME, DAT<PERSON>_TYPE, IS_NULLABLE\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = 'REF_EntityValue'\n        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')\n        ORDER BY COLUMN_NAME", "modified_content": "SELECT 1\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = 'REF_EntityValue'\n        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')\n        ORDER BY COLUMN_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, IS_NULLABLE, DATA_TYPE, COLUMN_NAME"}, {"timestamp": "2025-08-18T21:35:54.895357", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 48, "original_content": "SELECT \n            COUNT(*) as TotalRows,\n            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,\n            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows\n        FROM REF_EntityValue", "modified_content": "SELECT \n            1\n        FROM REF_EntityValue", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: NOT, WHEN, COUNT, NumericRows, NumericValue, IS, THEN, ValueUnits, CASE, UnitsRows, END, NULL, TotalRows"}, {"timestamp": "2025-08-18T21:35:54.896394", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 48, "original_content": "SELECT \n            COUNT(*) as TotalRows,\n            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,\n            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows\n        FROM REF_EntityValue", "modified_content": "SELECT \n            1\n        FROM REF_EntityValue", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: NOT, WHEN, COUNT, NumericRows, NumericValue, IS, THEN, ValueUnits, CASE, UnitsRows, END, NULL, TotalRows"}, {"timestamp": "2025-08-18T21:35:54.907615", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 66, "original_content": "SELECT TOP 5\n            Name,\n            EntityValue,\n            NumericValue,\n            ValueUnits\n        FROM REF_EntityValue \n        WHERE NumericValue IS NOT NULL\n        ORDER BY CreateDate DESC", "modified_content": "SELECT 1\n        FROM REF_EntityValue \n        WHERE NumericValue IS NOT NULL\n        ORDER BY CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, NumericValue, ValueUnits, Name"}, {"timestamp": "2025-08-18T21:35:54.914251", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 66, "original_content": "SELECT TOP 5\n            Name,\n            EntityValue,\n            NumericValue,\n            ValueUnits\n        FROM REF_EntityValue \n        WHERE NumericValue IS NOT NULL\n        ORDER BY CreateDate DESC", "modified_content": "SELECT 1\n        FROM REF_EntityValue \n        WHERE NumericValue IS NOT NULL\n        ORDER BY CreateDate DESC", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, NumericValue, ValueUnits, Name"}, {"timestamp": "2025-08-18T21:35:54.919232", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 149, "original_content": "SELECT EntityValue, NumericValue, ValueUnits\n            FROM REF_EntityValue \n            WHERE ID = ?", "modified_content": "SELECT 1\n            FROM REF_EntityValue \n            WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.926944", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 149, "original_content": "SELECT EntityValue, NumericValue, ValueUnits\n            FROM REF_EntityValue \n            WHERE ID = ?", "modified_content": "SELECT 1\n            FROM REF_EntityValue \n            WHERE ID = ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.936809", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_stored_procedures.py", "line_number": 56, "original_content": "SELECT name \n        FROM sys.objects \n        WHERE type = 'P' \n        AND name IN ({})", "modified_content": "SELECT 1 \n        FROM sys.objects \n        WHERE type = 'P' \n        AND name IN ({})", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.940005", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_stored_procedures.py", "line_number": 56, "original_content": "SELECT name \n        FROM sys.objects \n        WHERE type = 'P' \n        AND name IN ({})", "modified_content": "SELECT 1 \n        FROM sys.objects \n        WHERE type = 'P' \n        AND name IN ({})", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.954772", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_server.py", "line_number": 276, "original_content": "SELECT TOP 20 \n                FactID, FactType, FactText, ValidityRating, \n                CreatedDate, LastUpdated, Source\n            FROM REF_FACT \n            WHERE FactText LIKE ? \n            AND ValidityRating >= ?", "modified_content": "SELECT 1\n            FROM REF_FACT \n            WHERE FactText LIKE ? \n            AND ValidityRating >= ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.961509", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_server.py", "line_number": 276, "original_content": "SELECT TOP 20 \n                FactID, FactType, FactText, ValidityRating, \n                CreatedDate, LastUpdated, Source\n            FROM REF_FACT \n            WHERE FactText LIKE ? \n            AND ValidityRating >= ?", "modified_content": "SELECT 1\n            FROM REF_FACT \n            WHERE FactText LIKE ? \n            AND ValidityRating >= ?", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:54.972305", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 139, "original_content": "SELECT \n                TABLE_SCHEMA,\n                TABLE_NAME,\n                TABLE_TYPE\n            FROM INFORMATION_SCHEMA.TABLES \n            WHERE TABLE_TYPE = 'BASE TABLE'\n            ORDER BY TABLE_SCHEMA, TABLE_NAME", "modified_content": "SELECT \n                1\n            FROM INFORMATION_SCHEMA.TABLES \n            WHERE TABLE_TYPE = 'BASE TABLE'\n            ORDER BY TABLE_SCHEMA, TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE"}, {"timestamp": "2025-08-18T21:35:54.973306", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 139, "original_content": "SELECT \n                TABLE_SCHEMA,\n                TABLE_NAME,\n                TABLE_TYPE\n            FROM INFORMATION_SCHEMA.TABLES \n            WHERE TABLE_TYPE = 'BASE TABLE'\n            ORDER BY TABLE_SCHEMA, TABLE_NAME", "modified_content": "SELECT \n                1\n            FROM INFORMATION_SCHEMA.TABLES \n            WHERE TABLE_TYPE = 'BASE TABLE'\n            ORDER BY TABLE_SCHEMA, TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE"}, {"timestamp": "2025-08-18T21:35:54.989607", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 161, "original_content": "SELECT \n                TABLE_SCHEMA,\n                TABLE_NAME\n            FROM INFORMATION_SCHEMA.VIEWS\n            ORDER BY TABLE_SCHEMA, TABLE_NAME", "modified_content": "SELECT \n                1\n            FROM INFORMATION_SCHEMA.VIEWS\n            ORDER BY TABLE_SCHEMA, TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_SCHEMA, TABLE_NAME"}, {"timestamp": "2025-08-18T21:35:54.994157", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 161, "original_content": "SELECT \n                TABLE_SCHEMA,\n                TABLE_NAME\n            FROM INFORMATION_SCHEMA.VIEWS\n            ORDER BY TABLE_SCHEMA, TABLE_NAME", "modified_content": "SELECT \n                1\n            FROM INFORMATION_SCHEMA.VIEWS\n            ORDER BY TABLE_SCHEMA, TABLE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_SCHEMA, TABLE_NAME"}, {"timestamp": "2025-08-18T21:35:55.010838", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 181, "original_content": "SELECT \n                ROUTINE_SCHEMA,\n                ROUTINE_NAME,\n                RO<PERSON>INE_TYPE\n            FROM INFORMATION_SCHEMA.ROUTINES\n            WHERE ROUTINE_TYPE = 'PROCEDURE'\n            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME", "modified_content": "SELECT \n                1\n            FROM INFORMATION_SCHEMA.ROUTINES\n            WHERE ROUTINE_TYPE = 'PROCEDURE'\n            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: ROUTINE_NAME, ROUTINE_TYPE, ROUTINE_SCHEMA"}, {"timestamp": "2025-08-18T21:35:55.020062", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 181, "original_content": "SELECT \n                ROUTINE_SCHEMA,\n                ROUTINE_NAME,\n                RO<PERSON>INE_TYPE\n            FROM INFORMATION_SCHEMA.ROUTINES\n            WHERE ROUTINE_TYPE = 'PROCEDURE'\n            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME", "modified_content": "SELECT \n                1\n            FROM INFORMATION_SCHEMA.ROUTINES\n            WHERE ROUTINE_TYPE = 'PROCEDURE'\n            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: ROUTINE_NAME, ROUTINE_TYPE, ROUTINE_SCHEMA"}, {"timestamp": "2025-08-18T21:35:55.022429", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 203, "original_content": "SELECT \n                ROUTINE_SCHEMA,\n                ROUTINE_NAME,\n                RO<PERSON>INE_TYPE\n            FROM INFORMATION_SCHEMA.ROUTINES\n            WHERE ROUTINE_TYPE = 'FUNCTION'\n            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME", "modified_content": "SELECT \n                1\n            FROM INFORMATION_SCHEMA.ROUTINES\n            WHERE ROUTINE_TYPE = 'FUNCTION'\n            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: ROUTINE_NAME, ROUTINE_TYPE, ROUTINE_SCHEMA"}, {"timestamp": "2025-08-18T21:35:55.036512", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 203, "original_content": "SELECT \n                ROUTINE_SCHEMA,\n                ROUTINE_NAME,\n                RO<PERSON>INE_TYPE\n            FROM INFORMATION_SCHEMA.ROUTINES\n            WHERE ROUTINE_TYPE = 'FUNCTION'\n            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME", "modified_content": "SELECT \n                1\n            FROM INFORMATION_SCHEMA.ROUTINES\n            WHERE ROUTINE_TYPE = 'FUNCTION'\n            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: INFORMATION_SCHEMA, Columns: ROUTINE_NAME, ROUTINE_TYPE, ROUTINE_SCHEMA"}, {"timestamp": "2025-08-18T21:35:55.047600", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 255, "original_content": "SELECT \n                COLUMN_NAME,\n                DATA_TYPE,\n                IS_NULL<PERSON>LE,\n                COLUMN_DEFAULT,\n                CHARACTER_MAXIMUM_LENGTH,\n                NUMERIC_PRECISION,\n                NUMERIC_SCALE,\n                ORDINAL_POSITION\n            FROM INFORMATION_SCHEMA.COLUMNS\n            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?\n            ORDER BY ORDINAL_POSITION", "modified_content": "SELECT \n                1\n            FROM INFORMATION_SCHEMA.COLUMNS\n            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?\n            ORDER BY ORDINAL_POSITION", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:55.049327", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 255, "original_content": "SELECT \n                COLUMN_NAME,\n                DATA_TYPE,\n                IS_NULL<PERSON>LE,\n                COLUMN_DEFAULT,\n                CHARACTER_MAXIMUM_LENGTH,\n                NUMERIC_PRECISION,\n                NUMERIC_SCALE,\n                ORDINAL_POSITION\n            FROM INFORMATION_SCHEMA.COLUMNS\n            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?\n            ORDER BY ORDINAL_POSITION", "modified_content": "SELECT \n                1\n            FROM INFORMATION_SCHEMA.COLUMNS\n            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?\n            ORDER BY ORDINAL_POSITION", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:55.061234", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 287, "original_content": "SELECT\n                i.name as index_name,\n                i.type_desc as index_type,\n                i.is_unique,\n                i.is_primary_key,\n                STRING_AGG(c.name, ', ') WITHIN GROUP (ORDER BY ic.key_ordinal) as columns\n            FROM sys.indexes i\n            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id\n            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id\n            INNER JOIN sys.tables t ON i.object_id = t.object_id\n            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id\n            WHERE t.name = ? AND s.name = ?\n            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key\n            ORDER BY i.name", "modified_content": "SELECT\n                1\n            FROM sys.indexes i\n            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id\n            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id\n            INNER JOIN sys.tables t ON i.object_id = t.object_id\n            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id\n            WHERE t.name = ? AND s.name = ?\n            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key\n            ORDER BY i.name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:55.064145", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 287, "original_content": "SELECT\n                i.name as index_name,\n                i.type_desc as index_type,\n                i.is_unique,\n                i.is_primary_key,\n                STRING_AGG(c.name, ', ') WITHIN GROUP (ORDER BY ic.key_ordinal) as columns\n            FROM sys.indexes i\n            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id\n            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id\n            INNER JOIN sys.tables t ON i.object_id = t.object_id\n            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id\n            WHERE t.name = ? AND s.name = ?\n            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key\n            ORDER BY i.name", "modified_content": "SELECT\n                1\n            FROM sys.indexes i\n            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id\n            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id\n            INNER JOIN sys.tables t ON i.object_id = t.object_id\n            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id\n            WHERE t.name = ? AND s.name = ?\n            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key\n            ORDER BY i.name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:55.073465", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 318, "original_content": "SELECT\n                fk.name as foreign_key_name,\n                OBJECT_SCHEMA_NAME(fk.parent_object_id) as schema_name,\n                OBJECT_NAME(fk.parent_object_id) as table_name,\n                COL_NAME(fkc.parent_object_id, fkc.parent_column_id) as column_name,\n                OBJECT_SCHEMA_NAME(fk.referenced_object_id) as referenced_schema,\n                OBJECT_NAME(fk.referenced_object_id) as referenced_table,\n                COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) as referenced_column\n            FROM sys.foreign_keys fk\n            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id\n            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?\n            ORDER BY fk.name", "modified_content": "SELECT\n                1\n            FROM sys.foreign_keys fk\n            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id\n            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?\n            ORDER BY fk.name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:55.081830", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 318, "original_content": "SELECT\n                fk.name as foreign_key_name,\n                OBJECT_SCHEMA_NAME(fk.parent_object_id) as schema_name,\n                OBJECT_NAME(fk.parent_object_id) as table_name,\n                COL_NAME(fkc.parent_object_id, fkc.parent_column_id) as column_name,\n                OBJECT_SCHEMA_NAME(fk.referenced_object_id) as referenced_schema,\n                OBJECT_NAME(fk.referenced_object_id) as referenced_table,\n                COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) as referenced_column\n            FROM sys.foreign_keys fk\n            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id\n            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?\n            ORDER BY fk.name", "modified_content": "SELECT\n                1\n            FROM sys.foreign_keys fk\n            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id\n            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?\n            ORDER BY fk.name", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-18T21:35:55.088972", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 348, "original_content": "SELECT COUNT(*) as row_count FROM [{schema_name}].[{table_name}]", "modified_content": "SELECT 1 FROM [{schema_name}].[{table_name}]", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:55.095088", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 439, "original_content": "SELECT TOP {limit} * FROM [{schema_name}].[{table_name}]", "modified_content": "SELECT 1 FROM [{schema_name}].[{table_name}]", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:55.125703", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\manifest.py", "line_number": 258, "original_content": "Select strings (presumably filenames) from 'self.files' that\n        match 'pattern', a Unix-style wildcard (glob) pattern.\n\n        Patterns are not quite the same as implemented by the 'fnmatch'\n        module: '*' and '?'  match non-special characters, where \"special\"\n        is platform-dependent: slash on Unix; colon, slash, and backslash on\n        DOS/Windows; and colon on Mac OS.\n\n        If 'anchor' is true (the default), then the pattern match is more\n        stringent: \"*.py\" will match \"foo.py\" but not \"foo/bar.py\".  If\n        'anchor' is false, both of these will match.\n\n        If 'prefix' is supplied, then only filenames starting with 'prefix'\n        (itself a pattern) and ending with 'pattern', with anything in between\n        them, will match.  'anchor' is ignored in this case.\n\n        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and\n        'pattern' is assumed to be either a string containing a regex or a\n        regex object -- no translation is done, the regex is just compiled\n        and used as-is.\n\n        Selected strings will be added to self.files.\n\n        Return True if files are found.", "modified_content": "Select 1 from 'self.files' that\n        match 'pattern', a Unix-style wildcard (glob) pattern.\n\n        Patterns are not quite the same as implemented by the 'fnmatch'\n        module: '*' and '?'  match non-special characters, where \"special\"\n        is platform-dependent: slash on Unix; colon, slash, and backslash on\n        DOS/Windows; and colon on Mac OS.\n\n        If 'anchor' is true (the default), then the pattern match is more\n        stringent: \"*.py\" will match \"foo.py\" but not \"foo/bar.py\".  If\n        'anchor' is false, both of these will match.\n\n        If 'prefix' is supplied, then only filenames starting with 'prefix'\n        (itself a pattern) and ending with 'pattern', with anything in between\n        them, will match.  'anchor' is ignored in this case.\n\n        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and\n        'pattern' is assumed to be either a string containing a regex or a\n        regex object -- no translation is done, the regex is just compiled\n        and used as-is.\n\n        Selected strings will be added to self.files.\n\n        Return True if files are found.", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'self.files'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)\")"}, {"timestamp": "2025-08-18T21:35:55.130707", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\manifest.py", "line_number": 258, "original_content": "Select strings (presumably filenames) from 'self.files' that\n        match 'pattern', a Unix-style wildcard (glob) pattern.\n\n        Patterns are not quite the same as implemented by the 'fnmatch'\n        module: '*' and '?'  match non-special characters, where", "modified_content": "Select 1 from 'self.files' that\n        match 'pattern', a Unix-style wildcard (glob) pattern.\n\n        Patterns are not quite the same as implemented by the 'fnmatch'\n        module: '*' and '?'  match non-special characters, where", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'self.files'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:55.152777", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 208, "original_content": "Update just the width, return a copy.\n\n        Args:\n            width (int): New width (sets both min_width and max_width)\n\n        Returns:\n            ~ConsoleOptions: New console options instance.", "modified_content": "SELECT 1 FROM just WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:55.161906", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 208, "original_content": "Update just the width, return a copy.\n\n        Args:\n            width (int): New width (sets both min_width and max_width)\n\n        Returns:\n            ~ConsoleOptions: New console options instance.", "modified_content": "SELECT 1 FROM just WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:55.168385", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 244, "original_content": "Update the width and height, and return a copy.\n\n        Args:\n            width (int): New width (sets both min_width and max_width).\n            height (int): New height.\n\n        Returns:\n            ~ConsoleOptions: New console options instance.", "modified_content": "SELECT 1 FROM the WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:55.175193", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 244, "original_content": "Update the width and height, and return a copy.\n\n        Args:\n            width (int): New width (sets both min_width and max_width).\n            height (int): New height.\n\n        Returns:\n            ~ConsoleOptions: New console options instance.", "modified_content": "SELECT 1 FROM the WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:55.178382", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 1789, "original_content": "Update the screen at a given offset.\n\n        Args:\n            renderable (RenderableType): A Rich renderable.\n            region (Region, optional): Region of screen to update, or None for entire screen. Defaults to None.\n            x (int, optional): x offset. Defaults to 0.\n            y (int, optional): y offset. Defaults to 0.\n\n        Raises:\n            errors.NoAltScreen: If the Console isn't in alt screen mode.", "modified_content": "SELECT 1 FROM the WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:55.187593", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 1789, "original_content": "Update the screen at a given offset.\n\n        Args:\n            renderable (RenderableType): A Rich renderable.\n            region (Region, optional): Region of screen to update, or None for entire screen. Defaults to None.\n            x (int, optional): x offset. Defaults to 0.\n            y (int, optional): y offset. Defaults to 0.\n\n        Raises:\n            errors.NoAltScreen: If the Console isn't in alt screen mode.", "modified_content": "SELECT 1 FROM the WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:55.196545", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 1819, "original_content": "Update lines of the screen at a given offset.\n\n        Args:\n            lines (List[List[Segment]]): Rendered lines (as produced by :meth:`~rich.Console.render_lines`).\n            x (int, optional): x offset (column no). Defaults to 0.\n            y (int, optional): y offset (column no). Defaults to 0.\n\n        Raises:\n            errors.NoAltScreen: If the Console isn't in alt screen mode.", "modified_content": "SELECT 1 FROM lines WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:55.201999", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 1819, "original_content": "Update lines of the screen at a given offset.\n\n        Args:\n            lines (List[List[Segment]]): Rendered lines (as produced by :meth:`~rich.Console.render_lines`).\n            x (int, optional): x offset (column no). Defaults to 0.\n            y (int, optional): y offset (column no). Defaults to 0.\n\n        Raises:\n            errors.NoAltScreen: If the Console isn't in alt screen mode.", "modified_content": "SELECT 1 FROM lines WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:55.216988", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress.py", "line_number": 1413, "original_content": "Update information associated with a task.\n\n        Args:\n            task_id (TaskID): Task id (returned by add_task).\n            total (float, optional): Updates task.total if not None.\n            completed (float, optional): Updates task.completed if not None.\n            advance (float, optional): Add a value to task.completed if not None.\n            description (str, optional): Change task description if not None.\n            visible (bool, optional): Set visible flag if not None.\n            refresh (bool): Force a refresh of progress information. Default is False.\n            **fields (Any): Additional data fields required for rendering.", "modified_content": "SELECT 1 FROM information WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:55.223870", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress.py", "line_number": 1413, "original_content": "Update information associated with a task.\n\n        Args:\n            task_id (TaskID): Task id (returned by add_task).\n            total (float, optional): Updates task.total if not None.\n            completed (float, optional): Updates task.completed if not None.\n            advance (float, optional): Add a value to task.completed if not None.\n            description (str, optional): Change task description if not None.\n            visible (bool, optional): Set visible flag if not None.\n            refresh (bool): Force a refresh of progress information. Default is False.\n            **fields (Any): Additional data fields required for rendering.", "modified_content": "SELECT 1 FROM information WHERE 1=0", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:55.300108", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py", "line_number": 203, "original_content": "Select strings (presumably filenames) from 'self.files' that\n        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns\n        are not quite the same as implemented by the 'fnmatch' module: '*'\n        and '?'  match non-special characters, where \"special\" is platform-\n        dependent: slash on Unix; colon, slash, and backslash on\n        DOS/Windows; and colon on Mac OS.\n\n        If 'anchor' is true (the default), then the pattern match is more\n        stringent: \"*.py\" will match \"foo.py\" but not \"foo/bar.py\".  If\n        'anchor' is false, both of these will match.\n\n        If 'prefix' is supplied, then only filenames starting with 'prefix'\n        (itself a pattern) and ending with 'pattern', with anything in between\n        them, will match.  'anchor' is ignored in this case.\n\n        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and\n        'pattern' is assumed to be either a string containing a regex or a\n        regex object -- no translation is done, the regex is just compiled\n        and used as-is.\n\n        Selected strings will be added to self.files.\n\n        Return True if files are found, False otherwise.", "modified_content": "Select 1 from 'self.files' that\n        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns\n        are not quite the same as implemented by the 'fnmatch' module: '*'\n        and '?'  match non-special characters, where \"special\" is platform-\n        dependent: slash on Unix; colon, slash, and backslash on\n        DOS/Windows; and colon on Mac OS.\n\n        If 'anchor' is true (the default), then the pattern match is more\n        stringent: \"*.py\" will match \"foo.py\" but not \"foo/bar.py\".  If\n        'anchor' is false, both of these will match.\n\n        If 'prefix' is supplied, then only filenames starting with 'prefix'\n        (itself a pattern) and ending with 'pattern', with anything in between\n        them, will match.  'anchor' is ignored in this case.\n\n        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and\n        'pattern' is assumed to be either a string containing a regex or a\n        regex object -- no translation is done, the regex is just compiled\n        and used as-is.\n\n        Selected strings will be added to self.files.\n\n        Return True if files are found, False otherwise.", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'self.files'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)\")"}, {"timestamp": "2025-08-18T21:35:55.300108", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py", "line_number": 203, "original_content": "Select strings (presumably filenames) from 'self.files' that\n        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns\n        are not quite the same as implemented by the 'fnmatch' module: '*'\n        and '?'  match non-special characters, where", "modified_content": "Select 1 from 'self.files' that\n        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns\n        are not quite the same as implemented by the 'fnmatch' module: '*'\n        and '?'  match non-special characters, where", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'self.files'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:55.317220", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py", "line_number": 349, "original_content": "Select entry points from self that match the\n        given parameters (typically group and/or name).", "modified_content": "Select 1 from self that match the\n        given parameters (typically group and/or name).", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'match'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-18T21:35:55.326476", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py", "line_number": 349, "original_content": "Select entry points from self that match the\n        given parameters (typically group and/or name).", "modified_content": "Select 1 from self that match the\n        given parameters (typically group and/or name).", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'match'. (102) (SQLExecDirectW)\")"}], "last_updated": "2025-08-18T21:35:55.326613", "end_time": "2025-08-18T21:35:55.341207", "summary": {"session_id": "20250818_213553", "total_entries": 276, "operations": {"sql_test": 255, "repair": 21}, "statuses": {"success": 128, "failed": 148}, "sql_validation": {"total_tests": 255, "passed": 107, "failed": 148, "success_rate": 41.96078431372549}, "log_files": {"detailed": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\database_repair_logs\\repair_session_20250818_213553.json", "summary": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\database_repair_logs\\repair_summary_20250818_213553.csv", "validation": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\database_repair_logs\\sql_validation_20250818_213553.txt"}}}