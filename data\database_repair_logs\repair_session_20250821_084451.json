{"session_id": "20250821_084451", "start_time": "2025-08-21T08:44:51.847047", "entries": [{"timestamp": "2025-08-21T08:44:52.167135", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 109, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n               COUNT(e.ID) as EvidenceCount,\n               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage\n        FROM REF_Fact f\n        LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n        WHERE f.DataSource LIKE '%Self-Healer%'", "modified_content": "SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n               COUNT(e.ID) as EvidenceCount,\n               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage\n        FROM REF_Fact f\n        LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n        WHERE f.DataSource LIKE '%Self-Healer%') AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Column 'REF_Fact.ID' is invalid in the select list because it is not contained in either an aggregate function or the GROUP BY clause. (8120) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.182537", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 109, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n               COUNT(e.ID) as EvidenceCount,\n               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage\n        FROM REF_Fact f\n        LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n        WHERE f.DataSource LIKE '%Self-Healer%'", "modified_content": "SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n               COUNT(e.ID) as EvidenceCount,\n               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage\n        FROM REF_Fact f\n        LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n        WHERE f.DataSource LIKE '%Self-Healer%') AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Column 'REF_Fact.ID' is invalid in the select list because it is not contained in either an aggregate function or the GROUP BY clause. (8120) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.186123", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 194, "original_content": "INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)\n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_Evidence", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, Columns: FactID, DataSource, Evidence, Name"}, {"timestamp": "2025-08-21T08:44:52.188094", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 194, "original_content": "INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)\n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_Evidence", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, Columns: FactID, DataSource, Evidence, Name"}, {"timestamp": "2025-08-21T08:44:52.193099", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 206, "original_content": "SELECT ValidityRating FROM REF_Fact WHERE ID = ?", "modified_content": "SELECT TOP 1 * FROM (SELECT ValidityRating FROM REF_Fact WHERE ID IS NOT NULL) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: ID, ValidityRating"}, {"timestamp": "2025-08-21T08:44:52.195103", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 218, "original_content": "SELECT COUNT(*) as EvidenceCount FROM REF_Evidence WHERE FactID = ?", "modified_content": "SELECT TOP 1 * FROM (SELECT COUNT(*) as EvidenceCount FROM REF_Evidence WHERE FactID IS NOT NULL) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, Columns: FactID"}, {"timestamp": "2025-08-21T08:44:52.200766", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 226, "original_content": "UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?", "modified_content": "SELECT TOP 0 * FROM REF_Fact", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: ID, ValidityRating"}, {"timestamp": "2025-08-21T08:44:52.201767", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 260, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n                   'fact' as KnowledgeType\n            FROM REF_Fact f\n            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)\n            AND f.DataSource LIKE '%Self-Healer%'\n            AND f.ValidityRating >= ?\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n                   'fact' as KnowledgeType\n            FROM REF_Fact f\n            WHERE (f.Name LIKE '%' OR f.DataSource LIKE '%')\n            AND f.DataSource LIKE '%Self-Healer%'\n            AND f.ValidityRating >= 0) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: fact, CreateDate, ValidityRating, ID, DataSource, Name"}, {"timestamp": "2025-08-21T08:44:52.201767", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 260, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n                   'fact' as KnowledgeType\n            FROM REF_Fact f\n            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)\n            AND f.DataSource LIKE '%Self-Healer%'\n            AND f.ValidityRating >= ?\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n                   'fact' as KnowledgeType\n            FROM REF_Fact f\n            WHERE (f.Name LIKE '%' OR f.DataSource LIKE '%')\n            AND f.DataSource LIKE '%Self-Healer%'\n            AND f.ValidityRating >= 0) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: fact, CreateDate, ValidityRating, ID, DataSource, Name"}, {"timestamp": "2025-08-21T08:44:52.201767", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 276, "original_content": "SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,\n                   'opinion' as KnowledgeType\n            FROM REF_Opinion o\n            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)\n            AND o.DataSource LIKE '%Self-Healer%'\n            AND o.ValidityRating >= ?\n            ORDER BY o.ValidityRating DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,\n                   'opinion' as KnowledgeType\n            FROM REF_Opinion o\n            WHERE (o.Name LIKE '%' OR o.Opinion LIKE '%' OR o.DataSource LIKE '%')\n            AND o.DataSource LIKE '%Self-Healer%'\n            AND o.ValidityRating >= 0) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ValidityRating, opinion, ID, DataSource, Opinion, Name"}, {"timestamp": "2025-08-21T08:44:52.213888", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 276, "original_content": "SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,\n                   'opinion' as KnowledgeType\n            FROM REF_Opinion o\n            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)\n            AND o.DataSource LIKE '%Self-Healer%'\n            AND o.ValidityRating >= ?\n            ORDER BY o.ValidityRating DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,\n                   'opinion' as KnowledgeType\n            FROM REF_Opinion o\n            WHERE (o.Name LIKE '%' OR o.Opinion LIKE '%' OR o.DataSource LIKE '%')\n            AND o.DataSource LIKE '%Self-Healer%'\n            AND o.ValidityRating >= 0) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ValidityRating, opinion, ID, DataSource, Opinion, Name"}, {"timestamp": "2025-08-21T08:44:52.217936", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 292, "original_content": "SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,\n                   f.ValidityRating, 'evidence' as KnowledgeType\n            FROM REF_Evidence e\n            JOIN REF_Fact f ON e.FactID = f.ID\n            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)\n            AND e.DataSource LIKE '%Self-Healer%'\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,\n                   f.ValidityRating, 'evidence' as KnowledgeType\n            FROM REF_Evidence e\n            JOIN REF_Fact f ON e.FactID = f.ID\n            WHERE (e.Name LIKE '%' OR e.Evidence LIKE '%' OR e.DataSource LIKE '%')\n            AND e.DataSource LIKE '%Self-Healer%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ValidityRating, FactID, ID, DataSource, Evidence, evidence, Name"}, {"timestamp": "2025-08-21T08:44:52.219021", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 292, "original_content": "SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,\n                   f.ValidityRating, 'evidence' as KnowledgeType\n            FROM REF_Evidence e\n            JOIN REF_Fact f ON e.FactID = f.ID\n            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)\n            AND e.DataSource LIKE '%Self-Healer%'\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,\n                   f.ValidityRating, 'evidence' as KnowledgeType\n            FROM REF_Evidence e\n            JOIN REF_Fact f ON e.FactID = f.ID\n            WHERE (e.Name LIKE '%' OR e.Evidence LIKE '%' OR e.DataSource LIKE '%')\n            AND e.DataSource LIKE '%Self-Healer%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ValidityRating, FactID, ID, DataSource, Evidence, evidence, Name"}, {"timestamp": "2025-08-21T08:44:52.219021", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 329, "original_content": "SELECT \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END as Category,\n            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness\n        FROM REF_Fact f\n        WHERE f.DataSource LIKE '%Self-Healer%'\n        GROUP BY \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END", "modified_content": "SELECT TOP 1 * FROM (SELECT \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END as Category,\n            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness\n        FROM REF_Fact f\n        WHERE f.DataSource LIKE '%Self-Healer%'\n        GROUP BY \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource"}, {"timestamp": "2025-08-21T08:44:52.219021", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\api\\knowledge_endpoints.py", "line_number": 329, "original_content": "SELECT \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END as Category,\n            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness\n        FROM REF_Fact f\n        WHERE f.DataSource LIKE '%Self-Healer%'\n        GROUP BY \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END", "modified_content": "SELECT TOP 1 * FROM (SELECT \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END as Category,\n            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness\n        FROM REF_Fact f\n        WHERE f.DataSource LIKE '%Self-Healer%'\n        GROUP BY \n            CASE \n                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'\n                WHEN f.Name LIKE '%Database%' THEN 'Database'\n                WHEN f.Name LIKE '%Network%' THEN 'Network'\n                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'\n                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'\n                ELSE 'Other'\n            END) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource"}, {"timestamp": "2025-08-21T08:44:52.219021", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 241, "original_content": "INSERT INTO REF_Fact (Name, ValidityRating, DataSource) \n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_Fact", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource, Name"}, {"timestamp": "2025-08-21T08:44:52.235235", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 241, "original_content": "INSERT INTO REF_Fact (Name, ValidityRating, DataSource) \n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_Fact", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource, Name"}, {"timestamp": "2025-08-21T08:44:52.238275", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 267, "original_content": "INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)\n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_Evidence", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, Columns: FactID, DataSource, Evidence, Name"}, {"timestamp": "2025-08-21T08:44:52.240356", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 267, "original_content": "INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)\n        OUTPUT INSERTED.ID\n        VALUES (?, ?, ?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_Evidence", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, Columns: FactID, DataSource, Evidence, Name"}, {"timestamp": "2025-08-21T08:44:52.243503", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 374, "original_content": "SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ?\n            GROUP BY f.ID, f.ValidityRating", "modified_content": "SELECT TOP 1 * FROM (SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE '%'\n            GROUP BY f.ID, f.ValidityRating) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: FactID, ID, ValidityRating, Name"}, {"timestamp": "2025-08-21T08:44:52.245640", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 374, "original_content": "SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ?\n            GROUP BY f.ID, f.ValidityRating", "modified_content": "SELECT TOP 1 * FROM (SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE '%'\n            GROUP BY f.ID, f.ValidityRating) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: FactID, ID, ValidityRating, Name"}, {"timestamp": "2025-08-21T08:44:52.245640", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 396, "original_content": "UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?", "modified_content": "SELECT TOP 0 * FROM REF_Fact", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: ID, ValidityRating"}, {"timestamp": "2025-08-21T08:44:52.249177", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 410, "original_content": "SELECT ID FROM REF_Entity WHERE Name = ?", "modified_content": "SELECT TOP 1 * FROM (SELECT ID FROM REF_Entity WHERE Name IS NOT NULL) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: ID, Name"}, {"timestamp": "2025-08-21T08:44:52.249177", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 419, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            FROM REF_Fact f\n            WHERE f.DataSource LIKE ?\n            ORDER BY f.CreateDate DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            FROM REF_Fact f\n            WHERE f.DataSource LIKE '%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: CreateDate, ValidityRating, ID, DataSource, Name"}, {"timestamp": "2025-08-21T08:44:52.249177", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 419, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            FROM REF_Fact f\n            WHERE f.DataSource LIKE ?\n            ORDER BY f.CreateDate DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            FROM REF_Fact f\n            WHERE f.DataSource LIKE '%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: CreateDate, ValidityRating, ID, DataSource, Name"}, {"timestamp": "2025-08-21T08:44:52.249177", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 430, "original_content": "SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate\n                FROM REF_Evidence e\n                WHERE e.FactID = ?", "modified_content": "SELECT TOP 1 * FROM (SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate\n                FROM REF_Evidence e\n                WHERE e.FactID IS NOT NULL) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, Columns: CreateDate, FactID, ID, DataSource, Evidence, Name"}, {"timestamp": "2025-08-21T08:44:52.261693", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 430, "original_content": "SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate\n                FROM REF_Evidence e\n                WHERE e.FactID = ?", "modified_content": "SELECT TOP 1 * FROM (SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate\n                FROM REF_Evidence e\n                WHERE e.FactID IS NOT NULL) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, Columns: CreateDate, FactID, ID, DataSource, Evidence, Name"}, {"timestamp": "2025-08-21T08:44:52.264733", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 439, "original_content": "SELECT a.Name as Attribute<PERSON><PERSON>, ev.EntityValue, eav.CreateDate\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?", "modified_content": "SELECT TOP 0 * FROM X", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.266802", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 439, "original_content": "SELECT a.Name as Attribute<PERSON><PERSON>, ev.EntityValue, eav.CreateDate\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?", "modified_content": "SELECT TOP 0 * FROM X", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.271760", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 465, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n                   COUNT(e.ID) as EvidenceCount\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            ORDER BY f.ValidityRating DESC, f.CreateDate DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n                   COUNT(e.ID) as EvidenceCount\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE '%' OR f.DataSource LIKE '%'\n            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ValidityRating, FactID, ID, DataSource, Name"}, {"timestamp": "2025-08-21T08:44:52.273837", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 465, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n                   COUNT(e.ID) as EvidenceCount\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            ORDER BY f.ValidityRating DESC, f.CreateDate DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,\n                   COUNT(e.ID) as EvidenceCount\n            FROM REF_Fact f\n            LEFT JOIN REF_Evidence e ON f.ID = e.FactID\n            WHERE f.Name LIKE '%' OR f.DataSource LIKE '%'\n            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ValidityRating, FactID, ID, DataSource, Name"}, {"timestamp": "2025-08-21T08:44:52.278526", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 477, "original_content": "SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate\n            FROM REF_Opinion o\n            WHERE o.Name LIKE ? OR o.Opinion LIKE ?\n            ORDER BY o.ValidityRating DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate\n            FROM REF_Opinion o\n            WHERE o.Name LIKE '%' OR o.Opinion LIKE '%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ValidityRating, ID, DataSource, Opinion, Name"}, {"timestamp": "2025-08-21T08:44:52.281656", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 477, "original_content": "SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate\n            FROM REF_Opinion o\n            WHERE o.Name LIKE ? OR o.Opinion LIKE ?\n            ORDER BY o.ValidityRating DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate\n            FROM REF_Opinion o\n            WHERE o.Name LIKE '%' OR o.Opinion LIKE '%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ValidityRating, ID, DataSource, Opinion, Name"}, {"timestamp": "2025-08-21T08:44:52.283662", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 552, "original_content": "SELECT ID FROM REF_Category WHERE Name = ?", "modified_content": "SELECT TOP 1 * FROM (SELECT ID FROM REF_Category WHERE Name IS NOT NULL) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Category, Columns: ID, Name"}, {"timestamp": "2025-08-21T08:44:52.283662", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 559, "original_content": "INSERT INTO REF_Category (Name) OUTPUT INSERTED.ID VALUES (?)", "modified_content": "SELECT TOP 0 * FROM REF_Category", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Category, Columns: Name"}, {"timestamp": "2025-08-21T08:44:52.283662", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 566, "original_content": "SELECT ID FROM REF_Entity WHERE Name = ?", "modified_content": "SELECT TOP 1 * FROM (SELECT ID FROM REF_Entity WHERE Name IS NOT NULL) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: ID, Name"}, {"timestamp": "2025-08-21T08:44:52.283662", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 573, "original_content": "INSERT INTO REF_Entity (Name) OUTPUT INSERTED.ID VALUES (?)", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-21T08:44:52.283662", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 580, "original_content": "SELECT ID FROM REF_Attribute WHERE Name = ?", "modified_content": "SELECT TOP 1 * FROM (SELECT ID FROM REF_Attribute WHERE Name IS NOT NULL) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Attribute, Columns: ID, Name"}, {"timestamp": "2025-08-21T08:44:52.283662", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 587, "original_content": "INSERT INTO REF_Attribute (Name) OUTPUT INSERTED.ID VALUES (?)", "modified_content": "SELECT TOP 0 * FROM REF_Attribute", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Attribute, Columns: Name"}, {"timestamp": "2025-08-21T08:44:52.299313", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 594, "original_content": "SELECT ID FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?", "modified_content": "SELECT TOP 1 * FROM (SELECT ID FROM REF_EntityValue WHERE Name IS NOT NULL AND EntityValue IS NOT NULL) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: ID, EntityValue, Name"}, {"timestamp": "2025-08-21T08:44:52.299313", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 602, "original_content": "INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)\n            OUTPUT INSERTED.ID\n            VALUES (?, ?, ?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_EntityValue", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue, EntityValue, Name"}, {"timestamp": "2025-08-21T08:44:52.299313", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 602, "original_content": "INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)\n            OUTPUT INSERTED.ID\n            VALUES (?, ?, ?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_EntityValue", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue, EntityValue, Name"}, {"timestamp": "2025-08-21T08:44:52.308003", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 610, "original_content": "INSERT INTO REF_EntityValue (Name, EntityValue) OUTPUT INSERTED.ID VALUES (?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_EntityValue", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, Name"}, {"timestamp": "2025-08-21T08:44:52.308348", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 620, "original_content": "SELECT ID FROM XRF_EntityAttributeValue\n        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?", "modified_content": "SELECT TOP 0 * FROM X", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.308348", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 620, "original_content": "SELECT ID FROM XRF_EntityAttributeValue\n        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?", "modified_content": "SELECT TOP 0 * FROM X", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.308348", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 630, "original_content": "INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)\n        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)", "modified_content": "SELECT TOP 0 * FROM XRF_EntityAttributeValue", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.319282", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\core\\knowledge_integration.py", "line_number": 630, "original_content": "INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)\n        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)", "modified_content": "SELECT TOP 0 * FROM XRF_EntityAttributeValue", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.327986", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 855, "original_content": "SELECT ID, Name, CreateDate\n            FROM REF_Entity\n            WHERE Name = ? OR Name LIKE ?", "modified_content": "SELECT TOP 1 * FROM (SELECT ID, Name, CreateDate\n            FROM REF_Entity\n            WHERE Name IS NOT NULL OR Name LIKE '%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: ID, CreateDate, Name"}, {"timestamp": "2025-08-21T08:44:52.327986", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 855, "original_content": "SELECT ID, Name, CreateDate\n            FROM REF_Entity\n            WHERE Name = ? OR Name LIKE ?", "modified_content": "SELECT TOP 1 * FROM (SELECT ID, Name, CreateDate\n            FROM REF_Entity\n            WHERE Name IS NOT NULL OR Name LIKE '%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: ID, CreateDate, Name"}, {"timestamp": "2025-08-21T08:44:52.327986", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 872, "original_content": "SELECT\n                a.Name as Attribute<PERSON><PERSON>,\n                ev.EntityValue as AttributeV<PERSON><PERSON>,\n                ev.NumericValue,\n                ev.ValueUnits,\n                ev.Name as ValueName,\n                eav.CreateDate\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?\n            ORDER BY a.Name", "modified_content": "SELECT TOP 0 * FROM X", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.336894", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 872, "original_content": "SELECT\n                a.Name as Attribute<PERSON><PERSON>,\n                ev.EntityValue as AttributeV<PERSON><PERSON>,\n                ev.NumericValue,\n                ev.ValueUnits,\n                ev.Name as ValueName,\n                eav.CreateDate\n            FROM XRF_EntityAttributeValue eav\n            JOIN REF_Attribute a ON eav.AttributeID = a.ID\n            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n            WHERE eav.EntityID = ?\n            ORDER BY a.Name", "modified_content": "SELECT TOP 0 * FROM X", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.341582", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 914, "original_content": "SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            FROM REF_Fact f\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            FROM REF_Fact f\n            WHERE f.Name LIKE '%' OR f.DataSource LIKE '%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource, CreateDate, Name"}, {"timestamp": "2025-08-21T08:44:52.345581", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 914, "original_content": "SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            FROM REF_Fact f\n            WHERE f.Name LIKE ? OR f.DataSource LIKE ?\n            ORDER BY f.ValidityRating DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate\n            FROM REF_Fact f\n            WHERE f.Name LIKE '%' OR f.DataSource LIKE '%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource, CreateDate, Name"}, {"timestamp": "2025-08-21T08:44:52.345581", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 926, "original_content": "SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate\n            FROM REF_Evidence e\n            WHERE e.Name LIKE ? OR e.DataSource LIKE ?\n            ORDER BY e.CreateDate DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate\n            FROM REF_Evidence e\n            WHERE e.Name LIKE '%' OR e.DataSource LIKE '%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, Columns: CreateDate, DataSource, Evidence, Name"}, {"timestamp": "2025-08-21T08:44:52.345581", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 926, "original_content": "SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate\n            FROM REF_Evidence e\n            WHERE e.Name LIKE ? OR e.DataSource LIKE ?\n            ORDER BY e.CreateDate DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate\n            FROM REF_Evidence e\n            WHERE e.Name LIKE '%' OR e.DataSource LIKE '%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Evidence, Columns: CreateDate, DataSource, Evidence, Name"}, {"timestamp": "2025-08-21T08:44:52.345581", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 991, "original_content": "SELECT Name, ValidityRating, DataSource, CreateDate\n            FROM REF_Fact\n            WHERE Name LIKE ? OR DataSource LIKE ?\n            ORDER BY ValidityRating DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT Name, ValidityRating, DataSource, CreateDate\n            FROM REF_Fact\n            WHERE Name LIKE '%' OR DataSource LIKE '%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource, CreateDate, Name"}, {"timestamp": "2025-08-21T08:44:52.345581", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 991, "original_content": "SELECT Name, ValidityRating, DataSource, CreateDate\n            FROM REF_Fact\n            WHERE Name LIKE ? OR DataSource LIKE ?\n            ORDER BY ValidityRating DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT Name, ValidityRating, DataSource, CreateDate\n            FROM REF_Fact\n            WHERE Name LIKE '%' OR DataSource LIKE '%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource, CreateDate, Name"}, {"timestamp": "2025-08-21T08:44:52.361262", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 1009, "original_content": "SELECT Name, ValidityRating, Opinion, DataSource, CreateDate\n            FROM REF_Opinion\n            WHERE Name LIKE ? OR Opinion LIKE ?\n            ORDER BY ValidityRating DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT Name, ValidityRating, Opinion, DataSource, CreateDate\n            FROM REF_Opinion\n            WHERE Name LIKE '%' OR Opinion LIKE '%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ValidityRating, DataSource, Opinion, Name"}, {"timestamp": "2025-08-21T08:44:52.362118", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 1009, "original_content": "SELECT Name, ValidityRating, Opinion, DataSource, CreateDate\n            FROM REF_Opinion\n            WHERE Name LIKE ? OR Opinion LIKE ?\n            ORDER BY ValidityRating DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT Name, ValidityRating, Opinion, DataSource, CreateDate\n            FROM REF_Opinion\n            WHERE Name LIKE '%' OR Opinion LIKE '%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ValidityRating, DataSource, Opinion, Name"}, {"timestamp": "2025-08-21T08:44:52.362118", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 1021, "original_content": "SELECT DISTINCT e.Name, e.CreateDate\n            FROM REF_Entity e\n            WHERE e.Name LIKE 'Session_%'\n            AND EXISTS (\n                SELECT 1 FROM XRF_EntityAttributeValue eav\n                JOIN REF_Attribute a ON eav.AttributeID = a.ID\n                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n                WHERE eav.EntityID = e.ID\n                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)\n            )\n            ORDER BY e.CreateDate DESC", "modified_content": "SELECT TOP 0 * FROM X", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.371137", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\dashboard\\dashboard.py", "line_number": 1021, "original_content": "SELECT DISTINCT e.Name, e.CreateDate\n            FROM REF_Entity e\n            WHERE e.Name LIKE 'Session_%'\n            AND EXISTS (\n                SELECT 1 FROM XRF_EntityAttributeValue eav\n                JOIN REF_Attribute a ON eav.AttributeID = a.ID\n                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID\n                WHERE eav.EntityID = e.ID\n                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)\n            )\n            ORDER BY e.CreateDate DESC", "modified_content": "SELECT TOP 0 * FROM X", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.374459", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 34, "original_content": "SELECT COLUMN_NAME, DATA_TYPE, IS_NULL<PERSON>LE, COLUMN_DEFAULT\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = REF_EntityValue\n        ORDER BY ORDINAL_POSITION", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.378490", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 34, "original_content": "SELECT COLUMN_NAME, DATA_TYPE, IS_NULL<PERSON>LE, COLUMN_DEFAULT\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = REF_EntityValue\n        ORDER BY ORDINAL_POSITION", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.381620", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 66, "original_content": "SELECT name, create_date, modify_date\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'\n        ORDER BY name", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.384011", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 66, "original_content": "SELECT name, create_date, modify_date\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'\n        ORDER BY name", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.388462", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 93, "original_content": "SELECT name FROM sys.procedures WHERE name = '{proc_name}'", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.391463", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 106, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'\n        ORDER BY TABLE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.393062", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 106, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'\n        ORDER BY TABLE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.393062", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_db_state.py", "line_number": 119, "original_content": "SELECT COUNT(*) as row_count FROM {table_name}", "modified_content": "SELECT TOP 1 * FROM (SELECT COUNT(*) as row_count FROM {table_name}) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-21T08:44:52.401629", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_schema.py", "line_number": 9, "original_content": "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.403705", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_schema.py", "line_number": 17, "original_content": "SELECT TOP 5 * FROM REF_Entity WHERE Name LIKE 'Session_%'", "modified_content": "SELECT TOP 1 * FROM (SELECT TOP 5 * FROM REF_Entity WHERE Name LIKE 'Session_%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-21T08:44:52.403705", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_schema.py", "line_number": 26, "original_content": "SELECT TOP 10 * FROM REF_Entity ORDER BY CreateDate DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT TOP 10 * FROM REF_Entity) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-21T08:44:52.403705", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\check_schema.py", "line_number": 33, "original_content": "SELECT TOP 5 * FROM XRF_EntityAttributeValue", "modified_content": "SELECT TOP 0 * FROM X", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.403705", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\create_analytics_procedure.py", "line_number": 48, "original_content": "SELECT name, create_date \n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.420089", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\database\\create_analytics_procedure.py", "line_number": 48, "original_content": "SELECT name, create_date \n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.424124", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Self_Healer\\Documentation\\DB_Admin\\create_schema_procedure.sql", "line_number": 113, "original_content": "SELECT @count = COUNT(*) FROM", "modified_content": "SELECT TOP 1 * FROM (SELECT @count = COUNT(*) FROM) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable \"@count\". (137) (SQLExecDirectW)')"}, {"timestamp": "2025-08-21T08:44:52.436673", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.438755", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.442144", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.444133", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.448705", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\auto_fix_database_references.py", "line_number": 117, "original_content": "SELECT * FROM \\1 ORDER BY", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM \\1 ORDER BY) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '\\\\1'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.453704", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\check_database_tables.py", "line_number": 37, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' \n        AND TABLE_NAME LIKE 'REF_%' \n        ORDER BY TABLE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.457190", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\check_database_tables.py", "line_number": 37, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE' \n        AND TABLE_NAME LIKE 'REF_%' \n        ORDER BY TABLE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.460190", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\check_database_tables.py", "line_number": 77, "original_content": "SELECT TOP 1 * FROM REF_Entity", "modified_content": "SELECT TOP 1 * FROM (SELECT TOP 1 * FROM REF_Entity) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-21T08:44:52.465135", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\check_database_tables.py", "line_number": 87, "original_content": "SELECT TOP 1 * FROM REF_Entity", "modified_content": "SELECT TOP 1 * FROM (SELECT TOP 1 * FROM REF_Entity) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-21T08:44:52.470135", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\database_repair_logger.py", "line_number": 0, "original_content": "Contains 2 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.475977", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\database_repair_logger.py", "line_number": 268, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'test') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: *, Name"}, {"timestamp": "2025-08-21T08:44:52.480685", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\database_repair_logger.py", "line_number": 269, "original_content": "SELECT 1 FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT TOP 1 * FROM (SELECT 1 FROM REF_Entity WHERE Name = 'test') AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.481705", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 31, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'test') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: *, Name"}, {"timestamp": "2025-08-21T08:44:52.481705", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 35, "original_content": "INSERT INTO REF_Fact (EntityID, Name, Value) \n             VALUES (?, ?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_Fact", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: Value, EntityID, Name"}, {"timestamp": "2025-08-21T08:44:52.493413", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 35, "original_content": "INSERT INTO REF_Fact (EntityID, Name, Value) \n             VALUES (?, ?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_Fact", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: Value, EntityID, Name"}, {"timestamp": "2025-08-21T08:44:52.498814", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 39, "original_content": "SELECT your option from the menu", "modified_content": "SELECT TOP 0 * FROM t", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.503733", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 53, "original_content": "SELECT * FROM users WHERE active = 1", "modified_content": "SELECT TOP 0 * FROM us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.507742", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 54, "original_content": "UPDATE users SET status = ? WHERE id = ?", "modified_content": "SELECT TOP 0 * FROM users", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.508742", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\demonstrate_sql_refinement.py", "line_number": 55, "original_content": "DELETE FROM logs WHERE created_at < ?", "modified_content": "SELECT TOP 0 * FROM logs", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'logs'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.517660", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.518660", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Attributes'", "modified_content": "Replaced with 'REF_Attribute'", "rule_applied": "Table name fix: REF_Attributes → REF_Attribute", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.523195", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_EntityValues'", "modified_content": "Replaced with 'REF_EntityValue'", "rule_applied": "Table name fix: REF_EntityValues → REF_EntityValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.525705", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.527103", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.532045", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.535045", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.539845", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'XRF_Entity_Attribute_Value'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.544179", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\enhanced_database_fixer.py", "line_number": 0, "original_content": "Contains 1 instances of 'XRF_Entity_AttributeValue'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_AttributeValue → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.558593", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 208, "original_content": "SELECT * FROM REF_Entity WHERE Name = \\'test\\'", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = \\'test\\') AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\\\'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.562630", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 209, "original_content": "INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_Fact", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: Value, Name"}, {"timestamp": "2025-08-21T08:44:52.563641", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 209, "original_content": "INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_Fact", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: Value, Name"}, {"timestamp": "2025-08-21T08:44:52.563641", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 210, "original_content": "UPDATE REF_Entity SET Name = ? WHERE ID = ?", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: ID, Name"}, {"timestamp": "2025-08-21T08:44:52.563641", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 211, "original_content": "DELETE FROM REF_Entity WHERE ID = ?", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: ID"}, {"timestamp": "2025-08-21T08:44:52.583847", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 212, "original_content": "SELECT COUNT(*) FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID", "modified_content": "SELECT TOP 1 * FROM (SELECT COUNT(*) FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155)\")"}, {"timestamp": "2025-08-21T08:44:52.588079", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 218, "original_content": "SELECT your option FROM the menu", "modified_content": "SELECT TOP 0 * FROM t", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.592798", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 228, "original_content": "SELECT * FROM", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ')'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.597231", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\refined_sql_detector.py", "line_number": 230, "original_content": "SELECT name FROM users WHERE active = 1 ORDER BY name", "modified_content": "SELECT TOP 0 * FROM us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.604756", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Entities'", "modified_content": "Replaced with 'REF_Entity'", "rule_applied": "Table name fix: REF_Entities → REF_Entity", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.606758", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Facts'", "modified_content": "Replaced with 'REF_Fact'", "rule_applied": "Table name fix: REF_Facts → REF_Fact", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.606758", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Opinions'", "modified_content": "Replaced with 'REF_Opinion'", "rule_applied": "Table name fix: REF_Opinions → REF_Opinion", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.615059", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Sources'", "modified_content": "Replaced with 'REF_Source'", "rule_applied": "Table name fix: REF_Sources → REF_Source", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.617059", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\simple_table_validator.py", "line_number": 0, "original_content": "Contains 1 instances of 'REF_Categories'", "modified_content": "Replaced with 'REF_Category'", "rule_applied": "Table name fix: REF_Categories → REF_Category", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:52.623094", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 122, "original_content": "SELECT\\s+(.+?)(?=\\s+FROM)", "modified_content": "SELECT TOP 1 * FROM (SELECT\\s+(.+?)(?=\\s+FROM)) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-21T08:44:52.629064", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 145, "original_content": "DELETE FROM table", "modified_content": "SELECT TOP 0 * FROM table", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'table'. (156) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.633954", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 158, "original_content": "SELECT Name FROM Users", "modified_content": "SELECT TOP 0 * FROM Us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.637419", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 570, "original_content": "SELECT TOP 0 * FROM {table_name}", "modified_content": "SELECT TOP 1 * FROM (SELECT TOP 0 * FROM {table_name}) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-21T08:44:52.642888", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 584, "original_content": "SELECT TOP 1 * FROM ({test_sql}) AS test_query", "modified_content": "SELECT TOP 1 * FROM (SELECT TOP 1 * FROM ({test_sql}) AS test_query) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-21T08:44:52.645896", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 591, "original_content": "SELECT TOP 0 * FROM {table_name}", "modified_content": "SELECT TOP 1 * FROM (SELECT TOP 0 * FROM {table_name}) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-21T08:44:52.652360", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 598, "original_content": "SELECT TOP 0 * FROM {table_name}", "modified_content": "SELECT TOP 1 * FROM (SELECT TOP 0 * FROM {table_name}) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-21T08:44:52.657237", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 605, "original_content": "SELECT TOP 0 * FROM {table_name}", "modified_content": "SELECT TOP 1 * FROM (SELECT TOP 0 * FROM {table_name}) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-21T08:44:52.661405", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 612, "original_content": "SELECT TOP 0 * FROM {table_name}", "modified_content": "SELECT TOP 1 * FROM (SELECT TOP 0 * FROM {table_name}) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-21T08:44:52.663686", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\sql_validator.py", "line_number": 720, "original_content": "SELECT 1 FROM {tables[0]} WHERE 1=0", "modified_content": "SELECT TOP 1 * FROM (SELECT 1 FROM {tables[0]} WHERE 1=0) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-21T08:44:52.673140", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_refined_detection.py", "line_number": 39, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'test') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: *, Name"}, {"timestamp": "2025-08-21T08:44:52.675132", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_refined_detection.py", "line_number": 42, "original_content": "SELECT your option from the menu", "modified_content": "SELECT TOP 0 * FROM t", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.683450", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_refined_detection.py", "line_number": 45, "original_content": "INSERT INTO REF_Fact (EntityID, Name, Value) \n    VALUES (?, ?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_Fact", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: Value, EntityID, Name"}, {"timestamp": "2025-08-21T08:44:52.683450", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_refined_detection.py", "line_number": 45, "original_content": "INSERT INTO REF_Fact (EntityID, Name, Value) \n    VALUES (?, ?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_Fact", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: Value, EntityID, Name"}, {"timestamp": "2025-08-21T08:44:52.683450", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_refined_detection.py", "line_number": 54, "original_content": "UPDATE REF_Entity SET Name = ? WHERE ID = ?", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: ID, Name"}, {"timestamp": "2025-08-21T08:44:52.698497", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_schema_validator.py", "line_number": 36, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE'\n        ORDER BY TABLE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.716513", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_schema_validator.py", "line_number": 36, "original_content": "SELECT TABLE_NAME \n        FROM INFORMATION_SCHEMA.TABLES \n        WHERE TABLE_TYPE = 'BASE TABLE'\n        ORDER BY TABLE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.725830", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_database_schema_references.py", "line_number": 111, "original_content": "SELECT TABLE_NAME \n            FROM INFORMATION_SCHEMA.TABLES \n            WHERE TABLE_TYPE = 'BASE TABLE'\n            ORDER BY TABLE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.726969", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_database_schema_references.py", "line_number": 111, "original_content": "SELECT TABLE_NAME \n            FROM INFORMATION_SCHEMA.TABLES \n            WHERE TABLE_TYPE = 'BASE TABLE'\n            ORDER BY TABLE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.738049", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_database_schema_references.py", "line_number": 131, "original_content": "SELECT COLUMN_NAME, DATA_TYPE \n                FROM INFORMATION_SCHEMA.COLUMNS \n                WHERE TABLE_NAME = '{table_name}'\n                ORDER BY ORDINAL_POSITION", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.742805", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_database_schema_references.py", "line_number": 131, "original_content": "SELECT COLUMN_NAME, DATA_TYPE \n                FROM INFORMATION_SCHEMA.COLUMNS \n                WHERE TABLE_NAME = '{table_name}'\n                ORDER BY ORDINAL_POSITION", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.750676", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_stored_procedures.py", "line_number": 12, "original_content": "SELECT \n            name,\n            create_date,\n            modify_date\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.756062", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_stored_procedures.py", "line_number": 12, "original_content": "SELECT \n            name,\n            create_date,\n            modify_date\n        FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.761160", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_stored_procedures.py", "line_number": 83, "original_content": "SELECT name FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.766735", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\validate_stored_procedures.py", "line_number": 83, "original_content": "SELECT name FROM sys.procedures \n        WHERE name LIKE '%SelfHealer%'\n        ORDER BY name", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.797392", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 45, "original_content": "SELECT your option from the menu", "modified_content": "SELECT TOP 0 * FROM t", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.803398", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 66, "original_content": "SELECT * FROM REF_Entity", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: *"}, {"timestamp": "2025-08-21T08:44:52.808605", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 67, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'test') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: *, Name"}, {"timestamp": "2025-08-21T08:44:52.814088", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 68, "original_content": "SELECT COUNT(*) FROM REF_Entity", "modified_content": "SELECT TOP 1 * FROM (SELECT COUNT(*) FROM REF_Entity) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.814088", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 71, "original_content": "INSERT INTO REF_Entity (Name, Value) VALUES (?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Value, Name"}, {"timestamp": "2025-08-21T08:44:52.821131", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 72, "original_content": "INSERT INTO REF_Fact (EntityID, Name, Value) VALUES (1, 'test', 'value')", "modified_content": "SELECT TOP 0 * FROM REF_Fact", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: Value, EntityID, Name"}, {"timestamp": "2025-08-21T08:44:52.821131", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 75, "original_content": "UPDATE REF_Entity SET Name = ? WHERE ID = ?", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: ID, Name"}, {"timestamp": "2025-08-21T08:44:52.832700", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 76, "original_content": "UPDATE REF_Entity SET Status = 'active' WHERE Name = 'test'", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name, Status"}, {"timestamp": "2025-08-21T08:44:52.837359", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 79, "original_content": "DELETE FROM REF_Entity WHERE ID = ?", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: ID"}, {"timestamp": "2025-08-21T08:44:52.843425", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 80, "original_content": "DELETE FROM REF_Entity WHERE CreatedDate < '2023-01-01'", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: CreatedDate"}, {"timestamp": "2025-08-21T08:44:52.845424", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 90, "original_content": "SELECT e.ID, e.Name, f.Value\n    FROM REF_Entity e\n    JOIN REF_Fact f ON e.ID = f.EntityID\n    WHERE e.Status = 'active'\n    ORDER BY e.Name", "modified_content": "SELECT TOP 1 * FROM (SELECT e.ID, e.Name, f.Value\n    FROM REF_Entity e\n    JOIN REF_Fact f ON e.ID = f.EntityID\n    WHERE e.Status = 'active') AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207)\")"}, {"timestamp": "2025-08-21T08:44:52.845424", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 90, "original_content": "SELECT e.ID, e.Name, f.Value\n    FROM REF_Entity e\n    JOIN REF_Fact f ON e.ID = f.EntityID\n    WHERE e.Status = 'active'\n    ORDER BY e.Name", "modified_content": "SELECT TOP 1 * FROM (SELECT e.ID, e.Name, f.Value\n    FROM REF_Entity e\n    JOIN REF_Fact f ON e.ID = f.EntityID\n    WHERE e.Status = 'active') AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207)\")"}, {"timestamp": "2025-08-21T08:44:52.858624", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 99, "original_content": "INSERT INTO REF_Entity (Name, Description, Status, CreatedDate)\n    VALUES \n        ('Entity1', 'First entity', 'active', GETDATE()),\n        ('Entity2', 'Second entity', 'inactive', GETDATE()),\n        ('Entity3', 'Third entity', 'active', GETDATE())", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Description, CreatedDate, Status, Name"}, {"timestamp": "2025-08-21T08:44:52.859624", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 99, "original_content": "INSERT INTO REF_Entity (Name, Description, Status, CreatedDate)\n    VALUES \n        ('Entity1', 'First entity', 'active', GETDATE()),\n        ('Entity2', 'Second entity', 'inactive', GETDATE()),\n        ('Entity3', 'Third entity', 'active', GETDATE())", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Description, CreatedDate, Status, Name"}, {"timestamp": "2025-08-21T08:44:52.868160", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 108, "original_content": "UPDATE REF_Entity \n    SET Status = 'archived',\n        ModifiedDate = GETDATE()\n    WHERE CreatedDate < DATEADD(year, -1, GETDATE())\n        AND Status = 'inactive'", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: ModifiedDate, CreatedDate, Status"}, {"timestamp": "2025-08-21T08:44:52.873710", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 108, "original_content": "UPDATE REF_Entity \n    SET Status = 'archived',\n        ModifiedDate = GETDATE()\n    WHERE CreatedDate < DATEADD(year, -1, GETDATE())\n        AND Status = 'inactive'", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: ModifiedDate, CreatedDate, Status"}, {"timestamp": "2025-08-21T08:44:52.876709", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 117, "original_content": "DELETE FROM REF_Fact \n    WHERE EntityID IN (\n        SELECT ID FROM REF_Entity \n        WHERE Status = 'deleted'\n    )", "modified_content": "SELECT TOP 0 * FROM REF_Fact", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: EntityID, Status"}, {"timestamp": "2025-08-21T08:44:52.882239", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 117, "original_content": "DELETE FROM REF_Fact \n    WHERE EntityID IN (\n        SELECT ID FROM REF_Entity \n        WHERE Status = 'deleted'\n    )", "modified_content": "SELECT TOP 0 * FROM REF_Fact", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: EntityID, Status"}, {"timestamp": "2025-08-21T08:44:52.882239", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 135, "original_content": "SELECT \n        e.ID,\n        e.Name,\n        e.Description,\n        \n        f.Value,\n        f.CreatedDate\n        \n    FROM REF_Entity e\n    \n    LEFT JOIN REF_Fact f \n        ON e.ID = f.EntityID\n        \n    WHERE e.Status = 'active'\n        AND f.Value IS NOT NULL\n        \n    ORDER BY \n        e.Name ASC,\n        f.CreatedDate DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT \n        e.ID,\n        e.Name,\n        e.Description,\n        \n        f.Value,\n        f.CreatedDate\n        \n    FROM REF_Entity e\n    \n    LEFT JOIN REF_Fact f \n        ON e.ID = f.EntityID\n        \n    WHERE e.Status = 'active'\n        AND f.Value IS NOT NULL) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name '<PERSON>tityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Description'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207)\")"}, {"timestamp": "2025-08-21T08:44:52.893355", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 135, "original_content": "SELECT \n        e.ID,\n        e.Name,\n        e.Description,\n        \n        f.Value,\n        f.CreatedDate\n        \n    FROM REF_Entity e\n    \n    LEFT JOIN REF_Fact f \n        ON e.ID = f.EntityID\n        \n    WHERE e.Status = 'active'\n        AND f.Value IS NOT NULL\n        \n    ORDER BY \n        e.Name ASC,\n        f.CreatedDate DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT \n        e.ID,\n        e.Name,\n        e.Description,\n        \n        f.Value,\n        f.CreatedDate\n        \n    FROM REF_Entity e\n    \n    LEFT JOIN REF_Fact f \n        ON e.ID = f.EntityID\n        \n    WHERE e.Status = 'active'\n        AND f.Value IS NOT NULL) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name '<PERSON>tityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Description'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207)\")"}, {"timestamp": "2025-08-21T08:44:52.897863", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 158, "original_content": "INSERT INTO REF_Entity \n    (\n        Name,\n        Description,\n        Status,\n        CreatedDate\n    )\n    VALUES \n    (\n        ?,\n        ?,\n        'active',\n        GETDATE()\n    )", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Description, CreatedDate, Status, Name"}, {"timestamp": "2025-08-21T08:44:52.902620", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 158, "original_content": "INSERT INTO REF_Entity \n    (\n        Name,\n        Description,\n        Status,\n        CreatedDate\n    )\n    VALUES \n    (\n        ?,\n        ?,\n        'active',\n        GETDATE()\n    )", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Description, CreatedDate, Status, Name"}, {"timestamp": "2025-08-21T08:44:52.907626", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 194, "original_content": "SELECT 'This contains SELECT keyword' FROM REF_Entity", "modified_content": "SELECT TOP 1 * FROM (SELECT 'This contains SELECT keyword' FROM REF_Entity) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.912726", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 195, "original_content": "INSERT INTO REF_Entity (Name) VALUES ('UPDATE this later')", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-21T08:44:52.913726", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 198, "original_content": "SELECT * FROM REF_Entity  -- This is a comment with SELECT\n    WHERE Name = 'test'  /* Another comment with INSERT */", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity  -- This is a comment with SELECT\n    WHERE Name = 'test'  /* Another comment with INSERT */) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: *, with, Name"}, {"timestamp": "2025-08-21T08:44:52.913726", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 198, "original_content": "SELECT * FROM REF_Entity  -- This is a comment with SELECT\n    WHERE Name = 'test'  /* Another comment with INSERT */", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity  -- This is a comment with SELECT\n    WHERE Name = 'test'  /* Another comment with INSERT */) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: *, with, Name"}, {"timestamp": "2025-08-21T08:44:52.913726", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 205, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'test') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: *, Name"}, {"timestamp": "2025-08-21T08:44:52.934472", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 208, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'It\\\\'s a test'", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'It\\\\'s a test') AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 's'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Unclosed quotation mark after the character string ') AS test_query'. (105)\")"}, {"timestamp": "2025-08-21T08:44:52.939478", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 211, "original_content": "SELECT \n        e1.ID as Entity1_ID,\n        e1.Name as Entity1_Name,\n        e2.ID as Entity2_ID,\n        e2.Name as Entity2_Name,\n        f1.Value as Fact1_Value,\n        f2.Value as Fact2_Value\n    FROM REF_Entity e1\n    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID\n    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID\n    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID\n    WHERE e1.Status = 'active'\n        AND f1.Type = 'primary'\n        AND (e2.Status IS NULL OR e2.Status = 'active')\n    ORDER BY e1.Name, e2.Name\n    LIMIT 100", "modified_content": "SELECT TOP 1 * FROM (SELECT \n        e1.ID as Entity1_ID,\n        e1.Name as Entity1_Name,\n        e2.ID as Entity2_ID,\n        e2.Name as Entity2_Name,\n        f1.Value as Fact1_Value,\n        f2.Value as Fact2_Value\n    FROM REF_Entity e1\n    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID\n    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID\n    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID\n    WHERE e1.Status = 'active'\n        AND f1.Type = 'primary'\n        AND (e2.Status IS NULL OR e2.Status = 'active')) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name '<PERSON>tity<PERSON>'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name '<PERSON><PERSON><PERSON>'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Entity<PERSON>'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Type'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207)\")"}, {"timestamp": "2025-08-21T08:44:52.939478", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 211, "original_content": "SELECT \n        e1.ID as Entity1_ID,\n        e1.Name as Entity1_Name,\n        e2.ID as Entity2_ID,\n        e2.Name as Entity2_Name,\n        f1.Value as Fact1_Value,\n        f2.Value as Fact2_Value\n    FROM REF_Entity e1\n    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID\n    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID\n    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID\n    WHERE e1.Status = 'active'\n        AND f1.Type = 'primary'\n        AND (e2.Status IS NULL OR e2.Status = 'active')\n    ORDER BY e1.Name, e2.Name\n    LIMIT 100", "modified_content": "SELECT TOP 1 * FROM (SELECT \n        e1.ID as Entity1_ID,\n        e1.Name as Entity1_Name,\n        e2.ID as Entity2_ID,\n        e2.Name as Entity2_Name,\n        f1.Value as Fact1_Value,\n        f2.Value as Fact2_Value\n    FROM REF_Entity e1\n    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID\n    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID\n    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID\n    WHERE e1.Status = 'active'\n        AND f1.Type = 'primary'\n        AND (e2.Status IS NULL OR e2.Status = 'active')) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name '<PERSON>tity<PERSON>'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name '<PERSON><PERSON><PERSON>'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Entity<PERSON>'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Type'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207)\")"}, {"timestamp": "2025-08-21T08:44:52.952690", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 236, "original_content": "SELECT * FROM users WHERE id = ?", "modified_content": "SELECT TOP 0 * FROM us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.966396", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 237, "original_content": "INSERT INTO users (name, email) VALUES (?, ?)", "modified_content": "SELECT TOP 0 * FROM users", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.972554", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 238, "original_content": "UPDATE users SET email = ? WHERE id = ?", "modified_content": "SELECT TOP 0 * FROM users", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.977951", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 239, "original_content": "DELETE FROM users WHERE id = ?", "modified_content": "SELECT TOP 0 * FROM users", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.984257", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 243, "original_content": "SELECT COUNT(*) FROM transactions WHERE DATE(created_at) = CURDATE()", "modified_content": "SELECT TOP 0 * FROM t", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.989850", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 244, "original_content": "SELECT SUM(amount) FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())", "modified_content": "SELECT TOP 0 * FROM t", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:52.996076", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 269, "original_content": "SELECT * FROM REF_Entity", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: *"}, {"timestamp": "2025-08-21T08:44:53.002417", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 285, "original_content": "SELECT * FROM", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ')'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.006796", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 286, "original_content": "INSERT INTO REF_Entity", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-21T08:44:53.007795", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 287, "original_content": "UPDATE REF_Entity SET", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-21T08:44:53.007795", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 291, "original_content": "SELECT INSERT UPDATE DELETE FROM WHERE", "modified_content": "SELECT TOP 1 * FROM (SELECT INSERT UPDATE DELETE FROM WHERE) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'INSERT'. (156) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.007795", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 305, "original_content": "SELECT COUNT(*) FROM REF_Entity WHERE Status = 'test'", "modified_content": "SELECT TOP 1 * FROM (SELECT COUNT(*) FROM REF_Entity WHERE Status = 'test') AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155)\")"}, {"timestamp": "2025-08-21T08:44:53.028360", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 309, "original_content": "SELECT \n        'Test completed successfully' as message,\n        COUNT(*) as total_entities\n    FROM REF_Entity", "modified_content": "SELECT TOP 1 * FROM (SELECT \n        'Test completed successfully' as message,\n        COUNT(*) as total_entities\n    FROM REF_Entity) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: successfully, Test, completed"}, {"timestamp": "2025-08-21T08:44:53.028360", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\comprehensive_sql_test_file.py", "line_number": 309, "original_content": "SELECT \n        'Test completed successfully' as message,\n        COUNT(*) as total_entities\n    FROM REF_Entity", "modified_content": "SELECT TOP 1 * FROM (SELECT \n        'Test completed successfully' as message,\n        COUNT(*) as total_entities\n    FROM REF_Entity) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: successfully, Test, completed"}, {"timestamp": "2025-08-21T08:44:53.040547", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\simple_test.py", "line_number": 21, "original_content": "SELECT * FROM REF_Entity", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: *"}, {"timestamp": "2025-08-21T08:44:53.044770", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\simple_test.py", "line_number": 22, "original_content": "INSERT INTO REF_Entity VALUES (?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-21T08:44:53.044770", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\simple_test.py", "line_number": 23, "original_content": "UPDATE REF_Entity SET Name = ? WHERE ID = ?", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: ID, Name"}, {"timestamp": "2025-08-21T08:44:53.058999", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\simple_test.py", "line_number": 32, "original_content": "SELECT * FROM REF_Entity \nWHERE Status = 'active'", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity \nWHERE Status = 'active') AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.065722", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\simple_test.py", "line_number": 32, "original_content": "SELECT * FROM REF_Entity \nWHERE Status = 'active'", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity \nWHERE Status = 'active') AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.070741", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 62, "original_content": "SELECT Name, ID FROM Users", "modified_content": "SELECT TOP 0 * FROM Us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.070741", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 71, "original_content": "SELECT u.Name, u.ID FROM Users u", "modified_content": "SELECT TOP 0 * FROM Us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.085538", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 80, "original_content": "SELECT u.Name, u.ID FROM Users AS u", "modified_content": "SELECT TOP 0 * FROM Us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.093125", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 89, "original_content": "SELECT * FROM Products", "modified_content": "SELECT TOP 0 * FROM P", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'P'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.098844", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 98, "original_content": "SELECT * FROM Products WHERE Price > 100", "modified_content": "SELECT TOP 0 * FROM P", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'P'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.103854", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 107, "original_content": "SELECT * FROM Products p WHERE p.Active = 1", "modified_content": "SELECT TOP 0 * FROM P", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'P'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.109297", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 116, "original_content": "SELECT Name, Email FROM Users WHERE Active = 1", "modified_content": "SELECT TOP 0 * FROM Us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.116669", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 125, "original_content": "SELECT u.Name, u.Email FROM Users u WHERE u.Active = 1", "modified_content": "SELECT TOP 0 * FROM Us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.122156", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 135, "original_content": "SELECT u.Name FROM dbo.Users u WHERE u.Status = 'Active'", "modified_content": "SELECT TOP 0 * FROM dbo", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.122156", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 149, "original_content": "SELECT u.Name, p.Title FROM Users u INNER JOIN Posts p ON u.ID = p.UserID", "modified_content": "SELECT TOP 0 * FROM Us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.136033", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 158, "original_content": "SELECT u.Name, p.Title FROM Users u JOIN Posts p ON u.ID = p.UserID", "modified_content": "SELECT TOP 0 * FROM Us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.142255", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 168, "original_content": "SELECT u.Name, p.Title FROM Users u LEFT JOIN Posts p ON u.ID = p.UserID", "modified_content": "SELECT TOP 0 * FROM Us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.147265", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 177, "original_content": "SELECT u.Name, p.Title FROM Users u LEFT OUTER JOIN Posts p ON u.ID = p.UserID", "modified_content": "SELECT TOP 0 * FROM Us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.152241", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 187, "original_content": "SELECT u.Name, p.Title FROM Users u RIGHT JOIN Posts p ON u.ID = p.UserID", "modified_content": "SELECT TOP 0 * FROM Us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.160519", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 197, "original_content": "SELECT u.Name, p.Title FROM Users u FULL JOIN Posts p ON u.ID = p.UserID", "modified_content": "SELECT TOP 0 * FROM Us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.166132", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 207, "original_content": "SELECT u.Name, p.Title, c.Content FROM Users u JOIN Posts p ON u.ID = p.UserID JOIN Comments c ON p.ID = c.PostID", "modified_content": "SELECT TOP 0 * FROM Us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.172201", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 217, "original_content": "SELECT u.Name, p.Title, c.Content FROM Users u LEFT JOIN Posts p ON u.ID = p.UserID INNER JOIN Comments c ON p.ID = c.PostID", "modified_content": "SELECT TOP 0 * FROM Us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.179289", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 227, "original_content": "SELECT u.Name, p.Title FROM Users u, Posts p WHERE u.ID = p.UserID", "modified_content": "SELECT TOP 0 * FROM Us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.185037", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 236, "original_content": "SELECT t1.Name, t2.Title, t3.Content FROM Table1 t1, Table2 t2, Table3 t3 WHERE t1.ID = t2.T1_ID AND t2.ID = t3.T2_ID", "modified_content": "SELECT TOP 0 * FROM Tabl", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '<PERSON>bl'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.187063", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 250, "original_content": "SELECT Department, COUNT(*) as EmployeeCount FROM Employees GROUP BY Department", "modified_content": "SELECT TOP 1 * FROM (SELECT Department, COUNT(*) as EmployeeCount FROM Employees GROUP BY Department) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.197199", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 259, "original_content": "SELECT Department, JobTitle, COUNT(*) as Count FROM Employees GROUP BY Department, JobTitle", "modified_content": "SELECT TOP 1 * FROM (SELECT Department, JobTitle, COUNT(*) as Count FROM Employees GROUP BY Department, JobTitle) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.205144", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 269, "original_content": "SELECT Department, COUNT(*) as EmployeeCount FROM Employees GROUP BY Department HAVING COUNT(*) > 5", "modified_content": "SELECT TOP 1 * FROM (SELECT Department, COUNT(*) as EmployeeCount FROM Employees GROUP BY Department HAVING COUNT(*) > 5) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.211360", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 279, "original_content": "SELECT Name, Salary FROM Employees ORDER BY Salary DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT Name, Salary FROM Employees) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.218526", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 288, "original_content": "SELECT Name, Department, Salary FROM Employees ORDER BY Department ASC, Salary DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT Name, Department, Salary FROM Employees) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.232676", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 298, "original_content": "SELECT e.Department, COUNT(e.ID) as EmployeeCount, AVG(e.Sal<PERSON>) as AvgSalary FROM Employees e WHERE e.Active = 1 GROUP BY e.Department HAVING COUNT(e.ID) > 3 ORDER BY AvgSalary DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT e.Department, COUNT(e.ID) as EmployeeCount, AVG(e.Sal<PERSON>) as AvgSalary FROM Employees e WHERE e.Active = 1 GROUP BY e.Department HAVING COUNT(e.ID) > 3) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.239429", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 308, "original_content": "SELECT d.Name, COUNT(e.ID) as EmployeeCount FROM Departments d LEFT JOIN Employees e ON d.ID = e.DepartmentID GROUP BY d.Name", "modified_content": "SELECT TOP 0 * FROM D", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'D'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.252313", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 318, "original_content": "SELECT f.ID, f.Name, f.ValidityRating, COUNT(e.ID) as EvidenceCount, AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage FROM REF_Fact f LEFT JOIN REF_Evidence e ON f.ID = e.FactID WHERE f.DataSource LIKE '%Self-Healer%' GROUP BY f.ID, f.Name, f.ValidityRating", "modified_content": "SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, COUNT(e.ID) as EvidenceCount, AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage FROM REF_Fact f LEFT JOIN REF_Evidence e ON f.ID = e.FactID WHERE f.DataSource LIKE '%Self-Healer%' GROUP BY f.ID, f.Name, f.ValidityRating) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: ValidityRating, FactID, ID, DataSource, Name"}, {"timestamp": "2025-08-21T08:44:53.260782", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 328, "original_content": "SELECT Name, Salary FROM Employees ORDER BY Salary DESC LIMIT 10", "modified_content": "SELECT TOP 1 * FROM (SELECT Name, Salary FROM Employees) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.268053", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 342, "original_content": "INSERT INTO Users (Name, Email) VALUES ('<PERSON>', '<EMAIL>')", "modified_content": "SELECT TOP 0 * FROM Users", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.275338", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 351, "original_content": "INSERT INTO Users VALUES ('<PERSON>', '<EMAIL>', 1)", "modified_content": "SELECT TOP 0 * FROM Users", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.281735", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 361, "original_content": "INSERT INTO ActiveUsers (Name, Email) SELECT Name, Email FROM Users WHERE Active = 1", "modified_content": "SELECT TOP 0 * FROM ActiveUsers", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'ActiveUsers'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.288895", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 371, "original_content": "INSERT INTO dbo.Users (Name, Email, Status) VALUES ('<PERSON>', '<EMAIL>', 'Active')", "modified_content": "SELECT TOP 0 * FROM dbo", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.288895", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 385, "original_content": "UPDATE Users SET Name = '<PERSON>' WHERE ID = 1", "modified_content": "SELECT TOP 0 * FROM Users", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.302466", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 394, "original_content": "UPDATE Users SET Name = '<PERSON>', Email = '<EMAIL>', Status = 'Active' WHERE ID = 1", "modified_content": "SELECT TOP 0 * FROM Users", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.309251", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 404, "original_content": "UPDATE Users u SET u.Name = '<PERSON>', u.Status = 'Active' WHERE u.ID = 1", "modified_content": "SELECT TOP 0 * FROM Users", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.315863", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 414, "original_content": "UPDATE Users u SET u.Status = 'Inactive' FROM Users u JOIN Departments d ON u.DepartmentID = d.ID WHERE d.Name = 'Closed'", "modified_content": "SELECT TOP 0 * FROM Users", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.322479", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 424, "original_content": "UPDATE dbo.Users SET Status = 'Inactive' WHERE LastLogin < '2023-01-01'", "modified_content": "SELECT TOP 0 * FROM dbo", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.328158", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 438, "original_content": "DELETE FROM Users WHERE ID = 1", "modified_content": "SELECT TOP 0 * FROM Users", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.334110", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 447, "original_content": "DELETE FROM Users WHERE Status = 'Inactive' AND LastLogin < '2023-01-01'", "modified_content": "SELECT TOP 0 * FROM Users", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.340770", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 457, "original_content": "DELETE FROM Users u WHERE u.Status = 'Inactive'", "modified_content": "SELECT TOP 0 * FROM Users", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.348062", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 467, "original_content": "DELETE FROM dbo.Users WHERE Status = 'Deleted'", "modified_content": "SELECT TOP 0 * FROM dbo", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.354703", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 481, "original_content": "SELECT    u.Name   ,   u.Email   FROM    Users   u   WHERE   u.Active   =   1", "modified_content": "SELECT TOP 0 * FROM Us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.360714", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 491, "original_content": "select u.Name, u.Email from Users u where u.Active = 1", "modified_content": "SELECT TOP 0 * FROM Us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.368024", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 501, "original_content": "SELECT rf.ID, rf.Name FROM REF_Fact rf JOIN XRF_Fact_Evidence xfe ON rf.ID = xfe.FactID", "modified_content": "SELECT TOP 1 * FROM (SELECT rf.ID, rf.Name FROM REF_Fact rf JOIN XRF_Fact_Evidence xfe ON rf.ID = xfe.FactID) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_Fact_Evidence'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.375691", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 511, "original_content": "SELECT t1.Field1, t2.Field2 FROM Table1 t1 JOIN Table2 t2 ON t1.ID = t2.Table1ID", "modified_content": "SELECT TOP 0 * FROM Tabl", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '<PERSON>bl'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.381713", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\sql_parser_test_suite.py", "line_number": 521, "original_content": "SELECT employee.Name, department.Title FROM Employees employee JOIN Departments department ON employee.DeptID = department.ID", "modified_content": "SELECT TOP 1 * FROM (SELECT employee.Name, department.Title FROM Employees employee JOIN Departments department ON employee.DeptID = department.ID) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.389697", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 27, "original_content": "SELECT * FROM REF_Entity", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: *"}, {"timestamp": "2025-08-21T08:44:53.392697", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 28, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'test') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: *, Name"}, {"timestamp": "2025-08-21T08:44:53.402911", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 29, "original_content": "SELECT COUNT(*) FROM REF_Entity", "modified_content": "SELECT TOP 1 * FROM (SELECT COUNT(*) FROM REF_Entity) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.407916", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 30, "original_content": "INSERT INTO REF_Entity (Name, Value) VALUES (?, ?)", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Value, Name"}, {"timestamp": "2025-08-21T08:44:53.407916", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 31, "original_content": "INSERT INTO REF_Fact (EntityID, Name, Value) VALUES (1, 'test', 'value')", "modified_content": "SELECT TOP 0 * FROM REF_Fact", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: Value, EntityID, Name"}, {"timestamp": "2025-08-21T08:44:53.407916", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 32, "original_content": "UPDATE REF_Entity SET Name = ? WHERE ID = ?", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: ID, Name"}, {"timestamp": "2025-08-21T08:44:53.428109", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 33, "original_content": "UPDATE REF_Entity SET Status = 'active' WHERE Name = 'test'", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name, Status"}, {"timestamp": "2025-08-21T08:44:53.433135", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 34, "original_content": "DELETE FROM REF_Entity WHERE ID = ?", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: ID"}, {"timestamp": "2025-08-21T08:44:53.433135", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 35, "original_content": "DELETE FROM REF_Entity WHERE CreatedDate < '2023-01-01'", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: CreatedDate"}, {"timestamp": "2025-08-21T08:44:53.433135", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 40, "original_content": "SELECT 'This contains SELECT keyword' FROM REF_Entity", "modified_content": "SELECT TOP 1 * FROM (SELECT 'This contains SELECT keyword' FROM REF_Entity) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.454079", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 41, "original_content": "INSERT INTO REF_Entity (Name) VALUES ('UPDATE this later')", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Name"}, {"timestamp": "2025-08-21T08:44:53.456783", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 42, "original_content": "SELECT * FROM REF_Entity WHERE Name = \\\"test\\\"", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = \\\"test\\\") AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\\\'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.467273", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 43, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'test'", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'test') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: *, Name"}, {"timestamp": "2025-08-21T08:44:53.476034", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 44, "original_content": "SELECT * FROM REF_Entity WHERE Name = 'It\\\\'s a test'", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'It\\\\'s a test') AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 's'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Unclosed quotation mark after the character string ') AS test_query'. (105)\")"}, {"timestamp": "2025-08-21T08:44:53.481653", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 47, "original_content": "SELECT * FROM users WHERE id = ?", "modified_content": "SELECT TOP 0 * FROM us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.490918", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 48, "original_content": "INSERT INTO users (name, email) VALUES (?, ?)", "modified_content": "SELECT TOP 0 * FROM users", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.499338", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 49, "original_content": "UPDATE users SET email = ? WHERE id = ?", "modified_content": "SELECT TOP 0 * FROM users", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.506575", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 50, "original_content": "DELETE FROM users WHERE id = ?", "modified_content": "SELECT TOP 0 * FROM users", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.511589", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 51, "original_content": "SELECT COUNT(*) FROM transactions WHERE DATE(created_at) = CURDATE()", "modified_content": "SELECT TOP 0 * FROM t", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.520656", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 52, "original_content": "SELECT SUM(amount) FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())", "modified_content": "SELECT TOP 0 * FROM t", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.528754", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 67, "original_content": "SELECT your option from the menu", "modified_content": "SELECT TOP 0 * FROM t", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.535959", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 72, "original_content": "SELECT * FROM", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ')'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.540965", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 73, "original_content": "INSERT INTO REF_Entity", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-21T08:44:53.540965", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 74, "original_content": "UPDATE REF_Entity SET", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: "}, {"timestamp": "2025-08-21T08:44:53.556232", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 194, "original_content": "SELECT \n    e.ID,\n    e.Name\n\nFROM REF_Entity e\n\nWHERE e.Status = 'active'\n\nORDER BY e.Name", "modified_content": "SELECT TOP 1 * FROM (SELECT \n    e.ID,\n    e.Name\n\nFROM REF_Entity e\n\nWHERE e.Status = 'active') AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.559152", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 194, "original_content": "SELECT \n    e.ID,\n    e.Name\n\nFROM REF_Entity e\n\nWHERE e.Status = 'active'\n\nORDER BY e.Name", "modified_content": "SELECT TOP 1 * FROM (SELECT \n    e.ID,\n    e.Name\n\nFROM REF_Entity e\n\nWHERE e.Status = 'active') AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.569546", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 210, "original_content": "SELECT * FROM users WHERE name = '<PERSON>'", "modified_content": "SELECT TOP 0 * FROM us", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'us'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.569546", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 218, "original_content": "SELECT * FROM REF_Entity", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: *"}, {"timestamp": "2025-08-21T08:44:53.588370", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\SQL_Scripts\\test_sql_validator.py", "line_number": 219, "original_content": "SELECT * FROM REF_Entity WHERE ID = ?", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE ID IS NOT NULL) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: *, ID"}, {"timestamp": "2025-08-21T08:44:53.596146", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_dashboard_db.py", "line_number": 11, "original_content": "SELECT * FROM REF_Entity WHERE Name LIKE 'Session_%'", "modified_content": "SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name LIKE 'Session_%') AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: *, Name"}, {"timestamp": "2025-08-21T08:44:53.613301", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_healing_pipeline.py", "line_number": 257, "original_content": "SELECT COUNT(*) as count FROM REF_Fact", "modified_content": "SELECT TOP 1 * FROM (SELECT COUNT(*) as count FROM REF_Fact) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Fact, Columns: "}, {"timestamp": "2025-08-21T08:44:53.622114", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 0, "original_content": "Contains 3 instances of 'XRF_Entity_Attribute_Value'", "modified_content": "Replaced with 'XRF_EntityAttributeValue'", "rule_applied": "Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:53.630424", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 79, "original_content": "SELECT \n                name,\n                create_date,\n                modify_date,\n                type_desc\n            FROM sys.objects \n            WHERE type = 'P' \n            AND name IN ({})\n            ORDER BY name", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.637326", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 79, "original_content": "SELECT \n                name,\n                create_date,\n                modify_date,\n                type_desc\n            FROM sys.objects \n            WHERE type = 'P' \n            AND name IN ({})\n            ORDER BY name", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.643456", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 129, "original_content": "SELECT TOP 5 ID, Name FROM REF_Entity ORDER BY ID", "modified_content": "SELECT TOP 1 * FROM (SELECT TOP 5 ID, Name FROM REF_Entity) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: ID, Name"}, {"timestamp": "2025-08-21T08:44:53.649615", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 135, "original_content": "INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)\n                OUTPUT INSERTED.ID, INSERTED.Name\n                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Description, ModifyDate, CreateDate, Name"}, {"timestamp": "2025-08-21T08:44:53.657031", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 135, "original_content": "INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)\n                OUTPUT INSERTED.ID, INSERTED.Name\n                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())", "modified_content": "SELECT TOP 0 * FROM REF_Entity", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_Entity, Columns: Description, ModifyDate, CreateDate, Name"}, {"timestamp": "2025-08-21T08:44:53.658031", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 360, "original_content": "SELECT name FROM sys.objects\n            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.673024", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 360, "original_content": "SELECT name FROM sys.objects\n            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.681042", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 393, "original_content": "SELECT TABLE_NAME, TABLE_TYPE\n                FROM INFORMATION_SCHEMA.TABLES\n                WHERE TABLE_TYPE = 'BASE TABLE'\n                AND TABLE_NAME LIKE 'REF_%'\n                ORDER BY TABLE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.690004", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_knowledgebase_procedures.py", "line_number": 393, "original_content": "SELECT TABLE_NAME, TABLE_TYPE\n                FROM INFORMATION_SCHEMA.TABLES\n                WHERE TABLE_TYPE = 'BASE TABLE'\n                AND TABLE_NAME LIKE 'REF_%'\n                ORDER BY TABLE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.698432", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_mcp_database.py", "line_number": 180, "original_content": "SELECT COUNT(*) as total_rows FROM [{schema_name}].[{table_name}]", "modified_content": "SELECT TOP 1 * FROM (SELECT COUNT(*) as total_rows FROM [{schema_name}].[{table_name}]) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.706768", "operation_type": "repair", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 0, "original_content": "Contains 6 instances of 'REF_EntityValues'", "modified_content": "Replaced with 'REF_EntityValue'", "rule_applied": "Table name fix: REF_EntityValues → REF_EntityValue", "status": "success", "error_message": null, "sql_validation_result": null}, {"timestamp": "2025-08-21T08:44:53.714678", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 29, "original_content": "SELECT COLUMN_NAME, DAT<PERSON>_TYPE, IS_NULLABLE\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = 'REF_EntityValue'\n        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')\n        ORDER BY COLUMN_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.714678", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 29, "original_content": "SELECT COLUMN_NAME, DAT<PERSON>_TYPE, IS_NULLABLE\n        FROM INFORMATION_SCHEMA.COLUMNS \n        WHERE TABLE_NAME = 'REF_EntityValue'\n        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')\n        ORDER BY COLUMN_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.727352", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 48, "original_content": "SELECT \n            COUNT(*) as TotalRows,\n            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,\n            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows\n        FROM REF_EntityValue", "modified_content": "SELECT TOP 1 * FROM (SELECT \n            COUNT(*) as TotalRows,\n            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,\n            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows\n        FROM REF_EntityValue) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue"}, {"timestamp": "2025-08-21T08:44:53.727352", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 48, "original_content": "SELECT \n            COUNT(*) as TotalRows,\n            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,\n            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows\n        FROM REF_EntityValue", "modified_content": "SELECT TOP 1 * FROM (SELECT \n            COUNT(*) as TotalRows,\n            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,\n            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows\n        FROM REF_EntityValue) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue"}, {"timestamp": "2025-08-21T08:44:53.746439", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 66, "original_content": "SELECT TOP 5\n            Name,\n            EntityValue,\n            NumericValue,\n            ValueUnits\n        FROM REF_EntityValue \n        WHERE NumericValue IS NOT NULL\n        ORDER BY CreateDate DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT TOP 5\n            Name,\n            EntityValue,\n            NumericValue,\n            ValueUnits\n        FROM REF_EntityValue \n        WHERE NumericValue IS NOT NULL) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue, EntityValue, Name"}, {"timestamp": "2025-08-21T08:44:53.746439", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 66, "original_content": "SELECT TOP 5\n            Name,\n            EntityValue,\n            NumericValue,\n            ValueUnits\n        FROM REF_EntityValue \n        WHERE NumericValue IS NOT NULL\n        ORDER BY CreateDate DESC", "modified_content": "SELECT TOP 1 * FROM (SELECT TOP 5\n            Name,\n            EntityValue,\n            NumericValue,\n            ValueUnits\n        FROM REF_EntityValue \n        WHERE NumericValue IS NOT NULL) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue, EntityValue, Name"}, {"timestamp": "2025-08-21T08:44:53.746439", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 149, "original_content": "SELECT EntityValue, NumericValue, ValueUnits\n            FROM REF_EntityValue \n            WHERE ID = ?", "modified_content": "SELECT TOP 1 * FROM (SELECT EntityValue, NumericValue, ValueUnits\n            FROM REF_EntityValue \n            WHERE ID IS NOT NULL) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, ID, NumericValue, EntityValue"}, {"timestamp": "2025-08-21T08:44:53.767649", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_numeric_values_enhancement.py", "line_number": 149, "original_content": "SELECT EntityValue, NumericValue, ValueUnits\n            FROM REF_EntityValue \n            WHERE ID = ?", "modified_content": "SELECT TOP 1 * FROM (SELECT EntityValue, NumericValue, ValueUnits\n            FROM REF_EntityValue \n            WHERE ID IS NOT NULL) AS test_query", "rule_applied": "SQL Validation Test", "status": "success", "error_message": null, "sql_validation_result": "SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, ID, NumericValue, EntityValue"}, {"timestamp": "2025-08-21T08:44:53.781637", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_stored_procedures.py", "line_number": 56, "original_content": "SELECT name \n        FROM sys.objects \n        WHERE type = 'P' \n        AND name IN ({})", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.788384", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\tests\\test_stored_procedures.py", "line_number": 56, "original_content": "SELECT name \n        FROM sys.objects \n        WHERE type = 'P' \n        AND name IN ({})", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.805958", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_server.py", "line_number": 276, "original_content": "SELECT TOP 20 \n                FactID, FactType, FactText, ValidityRating, \n                CreatedDate, LastUpdated, Source\n            FROM REF_FACT \n            WHERE FactText LIKE ? \n            AND ValidityRating >= ?", "modified_content": "SELECT TOP 1 * FROM (SELECT TOP 20 \n                FactID, FactType, FactText, ValidityRating, \n                CreatedDate, LastUpdated, Source\n            FROM REF_FACT \n            WHERE FactText LIKE '%' \n            AND ValidityRating >= 0) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'FactText'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Fact<PERSON>'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'FactType'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'FactText'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'LastUpdated'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Source'. (207)\")"}, {"timestamp": "2025-08-21T08:44:53.808552", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_server.py", "line_number": 276, "original_content": "SELECT TOP 20 \n                FactID, FactType, FactText, ValidityRating, \n                CreatedDate, LastUpdated, Source\n            FROM REF_FACT \n            WHERE FactText LIKE ? \n            AND ValidityRating >= ?", "modified_content": "SELECT TOP 1 * FROM (SELECT TOP 20 \n                FactID, FactType, FactText, ValidityRating, \n                CreatedDate, LastUpdated, Source\n            FROM REF_FACT \n            WHERE FactText LIKE '%' \n            AND ValidityRating >= 0) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S22', \"[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'FactText'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Fact<PERSON>'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'FactType'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'FactText'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'LastUpdated'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Source'. (207)\")"}, {"timestamp": "2025-08-21T08:44:53.823389", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 139, "original_content": "SELECT \n                TABLE_SCHEMA,\n                TABLE_NAME,\n                TABLE_TYPE\n            FROM INFORMATION_SCHEMA.TABLES \n            WHERE TABLE_TYPE = 'BASE TABLE'\n            ORDER BY TABLE_SCHEMA, TABLE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.831678", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 139, "original_content": "SELECT \n                TABLE_SCHEMA,\n                TABLE_NAME,\n                TABLE_TYPE\n            FROM INFORMATION_SCHEMA.TABLES \n            WHERE TABLE_TYPE = 'BASE TABLE'\n            ORDER BY TABLE_SCHEMA, TABLE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.838503", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 161, "original_content": "SELECT \n                TABLE_SCHEMA,\n                TABLE_NAME\n            FROM INFORMATION_SCHEMA.VIEWS\n            ORDER BY TABLE_SCHEMA, TABLE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.847403", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 161, "original_content": "SELECT \n                TABLE_SCHEMA,\n                TABLE_NAME\n            FROM INFORMATION_SCHEMA.VIEWS\n            ORDER BY TABLE_SCHEMA, TABLE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.855048", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 181, "original_content": "SELECT \n                ROUTINE_SCHEMA,\n                ROUTINE_NAME,\n                RO<PERSON>INE_TYPE\n            FROM INFORMATION_SCHEMA.ROUTINES\n            WHERE ROUTINE_TYPE = 'PROCEDURE'\n            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.864187", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 181, "original_content": "SELECT \n                ROUTINE_SCHEMA,\n                ROUTINE_NAME,\n                RO<PERSON>INE_TYPE\n            FROM INFORMATION_SCHEMA.ROUTINES\n            WHERE ROUTINE_TYPE = 'PROCEDURE'\n            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.872039", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 203, "original_content": "SELECT \n                ROUTINE_SCHEMA,\n                ROUTINE_NAME,\n                RO<PERSON>INE_TYPE\n            FROM INFORMATION_SCHEMA.ROUTINES\n            WHERE ROUTINE_TYPE = 'FUNCTION'\n            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.876038", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 203, "original_content": "SELECT \n                ROUTINE_SCHEMA,\n                ROUTINE_NAME,\n                RO<PERSON>INE_TYPE\n            FROM INFORMATION_SCHEMA.ROUTINES\n            WHERE ROUTINE_TYPE = 'FUNCTION'\n            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.885256", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 255, "original_content": "SELECT \n                COLUMN_NAME,\n                DATA_TYPE,\n                IS_NULL<PERSON>LE,\n                COLUMN_DEFAULT,\n                CHARACTER_MAXIMUM_LENGTH,\n                NUMERIC_PRECISION,\n                NUMERIC_SCALE,\n                ORDINAL_POSITION\n            FROM INFORMATION_SCHEMA.COLUMNS\n            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?\n            ORDER BY ORDINAL_POSITION", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.895728", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 255, "original_content": "SELECT \n                COLUMN_NAME,\n                DATA_TYPE,\n                IS_NULL<PERSON>LE,\n                COLUMN_DEFAULT,\n                CHARACTER_MAXIMUM_LENGTH,\n                NUMERIC_PRECISION,\n                NUMERIC_SCALE,\n                ORDINAL_POSITION\n            FROM INFORMATION_SCHEMA.COLUMNS\n            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?\n            ORDER BY ORDINAL_POSITION", "modified_content": "SELECT TOP 0 * FROM INFO", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.903646", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 287, "original_content": "SELECT\n                i.name as index_name,\n                i.type_desc as index_type,\n                i.is_unique,\n                i.is_primary_key,\n                STRING_AGG(c.name, ', ') WITHIN GROUP (ORDER BY ic.key_ordinal) as columns\n            FROM sys.indexes i\n            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id\n            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id\n            INNER JOIN sys.tables t ON i.object_id = t.object_id\n            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id\n            WHERE t.name = ? AND s.name = ?\n            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key\n            ORDER BY i.name", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.907624", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 287, "original_content": "SELECT\n                i.name as index_name,\n                i.type_desc as index_type,\n                i.is_unique,\n                i.is_primary_key,\n                STRING_AGG(c.name, ', ') WITHIN GROUP (ORDER BY ic.key_ordinal) as columns\n            FROM sys.indexes i\n            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id\n            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id\n            INNER JOIN sys.tables t ON i.object_id = t.object_id\n            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id\n            WHERE t.name = ? AND s.name = ?\n            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key\n            ORDER BY i.name", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.918074", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 318, "original_content": "SELECT\n                fk.name as foreign_key_name,\n                OBJECT_SCHEMA_NAME(fk.parent_object_id) as schema_name,\n                OBJECT_NAME(fk.parent_object_id) as table_name,\n                COL_NAME(fkc.parent_object_id, fkc.parent_column_id) as column_name,\n                OBJECT_SCHEMA_NAME(fk.referenced_object_id) as referenced_schema,\n                OBJECT_NAME(fk.referenced_object_id) as referenced_table,\n                COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) as referenced_column\n            FROM sys.foreign_keys fk\n            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id\n            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?\n            ORDER BY fk.name", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.918074", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 318, "original_content": "SELECT\n                fk.name as foreign_key_name,\n                OBJECT_SCHEMA_NAME(fk.parent_object_id) as schema_name,\n                OBJECT_NAME(fk.parent_object_id) as table_name,\n                COL_NAME(fkc.parent_object_id, fkc.parent_column_id) as column_name,\n                OBJECT_SCHEMA_NAME(fk.referenced_object_id) as referenced_schema,\n                OBJECT_NAME(fk.referenced_object_id) as referenced_table,\n                COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) as referenced_column\n            FROM sys.foreign_keys fk\n            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id\n            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?\n            ORDER BY fk.name", "modified_content": "SELECT TOP 0 * FROM sys", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.936769", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 348, "original_content": "SELECT COUNT(*) as row_count FROM [{schema_name}].[{table_name}]", "modified_content": "SELECT TOP 1 * FROM (SELECT COUNT(*) as row_count FROM [{schema_name}].[{table_name}]) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:53.938759", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\mcp_database_tool.py", "line_number": 439, "original_content": "SELECT TOP {limit} * FROM [{schema_name}].[{table_name}]", "modified_content": "SELECT TOP 1 * FROM (SELECT TOP {limit} * FROM [{schema_name}].[{table_name}]) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')"}, {"timestamp": "2025-08-21T08:44:53.982411", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\manifest.py", "line_number": 258, "original_content": "Select strings (presumably filenames) from 'self.files' that\n        match 'pattern', a Unix-style wildcard (glob) pattern.\n\n        Patterns are not quite the same as implemented by the 'fnmatch'\n        module: '*' and '?'  match non-special characters, where \"special\"\n        is platform-dependent: slash on Unix; colon, slash, and backslash on\n        DOS/Windows; and colon on Mac OS.\n\n        If 'anchor' is true (the default), then the pattern match is more\n        stringent: \"*.py\" will match \"foo.py\" but not \"foo/bar.py\".  If\n        'anchor' is false, both of these will match.\n\n        If 'prefix' is supplied, then only filenames starting with 'prefix'\n        (itself a pattern) and ending with 'pattern', with anything in between\n        them, will match.  'anchor' is ignored in this case.\n\n        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and\n        'pattern' is assumed to be either a string containing a regex or a\n        regex object -- no translation is done, the regex is just compiled\n        and used as-is.\n\n        Selected strings will be added to self.files.\n\n        Return True if files are found.", "modified_content": "SELECT TOP 1 * FROM (Select strings (presumably filenames) from 'self.files' that\n        match 'pattern', a Unix-style wildcard (glob) pattern.\n\n        Patterns are not quite the same as implemented by the 'fnmatch'\n        module: '*' and '?'  match non-special characters, where \"special\"\n        is platform-dependent: slash on Unix; colon, slash, and backslash on\n        DOS/Windows; and colon on Mac OS.\n\n        If 'anchor' is true (the default), then the pattern match is more\n        stringent: \"*.py\" will match \"foo.py\" but not \"foo/bar.py\".  If\n        'anchor' is false, both of these will match.\n\n        If 'prefix' is supplied, then only filenames starting with 'prefix'\n        (itself a pattern) and ending with 'pattern', with anything in between\n        them, will match.  'anchor' is ignored in this case.\n\n        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and\n        'pattern' is assumed to be either a string containing a regex or a\n        regex object -- no translation is done, the regex is just compiled\n        and used as-is.\n\n        Selected strings will be added to self.files.\n\n        Return True if files are found.) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)\")"}, {"timestamp": "2025-08-21T08:44:53.988447", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\distlib\\manifest.py", "line_number": 258, "original_content": "Select strings (presumably filenames) from 'self.files' that\n        match 'pattern', a Unix-style wildcard (glob) pattern.\n\n        Patterns are not quite the same as implemented by the 'fnmatch'\n        module: '*' and '?'  match non-special characters, where", "modified_content": "SELECT TOP 1 * FROM (Select strings (presumably filenames) from 'self.files' that\n        match 'pattern', a Unix-style wildcard (glob) pattern.\n\n        Patterns are not quite the same as implemented by the 'fnmatch'\n        module: '*' and '?'  match non-special characters, where) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:54.013113", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 208, "original_content": "Update just the width, return a copy.\n\n        Args:\n            width (int): New width (sets both min_width and max_width)\n\n        Returns:\n            ~ConsoleOptions: New console options instance.", "modified_content": "SELECT TOP 0 * FROM just", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:54.027043", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 208, "original_content": "Update just the width, return a copy.\n\n        Args:\n            width (int): New width (sets both min_width and max_width)\n\n        Returns:\n            ~ConsoleOptions: New console options instance.", "modified_content": "SELECT TOP 0 * FROM just", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:54.035309", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 244, "original_content": "Update the width and height, and return a copy.\n\n        Args:\n            width (int): New width (sets both min_width and max_width).\n            height (int): New height.\n\n        Returns:\n            ~ConsoleOptions: New console options instance.", "modified_content": "SELECT TOP 0 * FROM the", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:54.040888", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 244, "original_content": "Update the width and height, and return a copy.\n\n        Args:\n            width (int): New width (sets both min_width and max_width).\n            height (int): New height.\n\n        Returns:\n            ~ConsoleOptions: New console options instance.", "modified_content": "SELECT TOP 0 * FROM the", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:54.052186", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 1789, "original_content": "Update the screen at a given offset.\n\n        Args:\n            renderable (RenderableType): A Rich renderable.\n            region (Region, optional): Region of screen to update, or None for entire screen. Defaults to None.\n            x (int, optional): x offset. Defaults to 0.\n            y (int, optional): y offset. Defaults to 0.\n\n        Raises:\n            errors.NoAltScreen: If the Console isn't in alt screen mode.", "modified_content": "SELECT TOP 0 * FROM the", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:54.059625", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 1789, "original_content": "Update the screen at a given offset.\n\n        Args:\n            renderable (RenderableType): A Rich renderable.\n            region (Region, optional): Region of screen to update, or None for entire screen. Defaults to None.\n            x (int, optional): x offset. Defaults to 0.\n            y (int, optional): y offset. Defaults to 0.\n\n        Raises:\n            errors.NoAltScreen: If the Console isn't in alt screen mode.", "modified_content": "SELECT TOP 0 * FROM the", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:54.068107", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 1819, "original_content": "Update lines of the screen at a given offset.\n\n        Args:\n            lines (List[List[Segment]]): Rendered lines (as produced by :meth:`~rich.Console.render_lines`).\n            x (int, optional): x offset (column no). Defaults to 0.\n            y (int, optional): y offset (column no). Defaults to 0.\n\n        Raises:\n            errors.NoAltScreen: If the Console isn't in alt screen mode.", "modified_content": "SELECT TOP 0 * FROM lines", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:54.075706", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\console.py", "line_number": 1819, "original_content": "Update lines of the screen at a given offset.\n\n        Args:\n            lines (List[List[Segment]]): Rendered lines (as produced by :meth:`~rich.Console.render_lines`).\n            x (int, optional): x offset (column no). Defaults to 0.\n            y (int, optional): y offset (column no). Defaults to 0.\n\n        Raises:\n            errors.NoAltScreen: If the Console isn't in alt screen mode.", "modified_content": "SELECT TOP 0 * FROM lines", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:54.093943", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress.py", "line_number": 1413, "original_content": "Update information associated with a task.\n\n        Args:\n            task_id (TaskID): Task id (returned by add_task).\n            total (float, optional): Updates task.total if not None.\n            completed (float, optional): Updates task.completed if not None.\n            advance (float, optional): Add a value to task.completed if not None.\n            description (str, optional): Change task description if not None.\n            visible (bool, optional): Set visible flag if not None.\n            refresh (bool): Force a refresh of progress information. Default is False.\n            **fields (Any): Additional data fields required for rendering.", "modified_content": "SELECT TOP 0 * FROM information", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:54.098955", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\pip\\_vendor\\rich\\progress.py", "line_number": 1413, "original_content": "Update information associated with a task.\n\n        Args:\n            task_id (TaskID): Task id (returned by add_task).\n            total (float, optional): Updates task.total if not None.\n            completed (float, optional): Updates task.completed if not None.\n            advance (float, optional): Add a value to task.completed if not None.\n            description (str, optional): Change task description if not None.\n            visible (bool, optional): Set visible flag if not None.\n            refresh (bool): Force a refresh of progress information. Default is False.\n            **fields (Any): Additional data fields required for rendering.", "modified_content": "SELECT TOP 0 * FROM information", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:54.195493", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py", "line_number": 203, "original_content": "Select strings (presumably filenames) from 'self.files' that\n        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns\n        are not quite the same as implemented by the 'fnmatch' module: '*'\n        and '?'  match non-special characters, where \"special\" is platform-\n        dependent: slash on Unix; colon, slash, and backslash on\n        DOS/Windows; and colon on Mac OS.\n\n        If 'anchor' is true (the default), then the pattern match is more\n        stringent: \"*.py\" will match \"foo.py\" but not \"foo/bar.py\".  If\n        'anchor' is false, both of these will match.\n\n        If 'prefix' is supplied, then only filenames starting with 'prefix'\n        (itself a pattern) and ending with 'pattern', with anything in between\n        them, will match.  'anchor' is ignored in this case.\n\n        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and\n        'pattern' is assumed to be either a string containing a regex or a\n        regex object -- no translation is done, the regex is just compiled\n        and used as-is.\n\n        Selected strings will be added to self.files.\n\n        Return True if files are found, False otherwise.", "modified_content": "SELECT TOP 1 * FROM (Select strings (presumably filenames) from 'self.files' that\n        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns\n        are not quite the same as implemented by the 'fnmatch' module: '*'\n        and '?'  match non-special characters, where \"special\" is platform-\n        dependent: slash on Unix; colon, slash, and backslash on\n        DOS/Windows; and colon on Mac OS.\n\n        If 'anchor' is true (the default), then the pattern match is more\n        stringent: \"*.py\" will match \"foo.py\" but not \"foo/bar.py\".  If\n        'anchor' is false, both of these will match.\n\n        If 'prefix' is supplied, then only filenames starting with 'prefix'\n        (itself a pattern) and ending with 'pattern', with anything in between\n        them, will match.  'anchor' is ignored in this case.\n\n        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and\n        'pattern' is assumed to be either a string containing a regex or a\n        regex object -- no translation is done, the regex is just compiled\n        and used as-is.\n\n        Selected strings will be added to self.files.\n\n        Return True if files are found, False otherwise.) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)\")"}, {"timestamp": "2025-08-21T08:44:54.204156", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py", "line_number": 203, "original_content": "Select strings (presumably filenames) from 'self.files' that\n        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns\n        are not quite the same as implemented by the 'fnmatch' module: '*'\n        and '?'  match non-special characters, where", "modified_content": "SELECT TOP 1 * FROM (Select strings (presumably filenames) from 'self.files' that\n        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns\n        are not quite the same as implemented by the 'fnmatch' module: '*'\n        and '?'  match non-special characters, where) AS test_query", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42000', \"[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:54.217012", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py", "line_number": 349, "original_content": "Select entry points from self that match the\n        given parameters (typically group and/or name).", "modified_content": "SELECT TOP 0 * FROM s", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 's'. (208) (SQLExecDirectW)\")"}, {"timestamp": "2025-08-21T08:44:54.222424", "operation_type": "sql_test", "file_path": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\n8n_builder\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py", "line_number": 349, "original_content": "Select entry points from self that match the\n        given parameters (typically group and/or name).", "modified_content": "SELECT TOP 0 * FROM s", "rule_applied": "SQL Validation Test", "status": "failed", "error_message": null, "sql_validation_result": "FAILED - ('42S02', \"[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 's'. (208) (SQLExecDirectW)\")"}], "last_updated": "2025-08-21T08:44:54.225406", "end_time": "2025-08-21T08:44:54.247123", "summary": {"session_id": "20250821_084451", "total_entries": 325, "operations": {"sql_test": 304, "repair": 21}, "statuses": {"failed": 185, "success": 140}, "sql_validation": {"total_tests": 304, "passed": 119, "failed": 185, "success_rate": 39.14473684210527}, "log_files": {"detailed": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\database_repair_logs\\repair_session_20250821_084451.json", "summary": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\database_repair_logs\\repair_summary_20250821_084451.csv", "validation": "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\database_repair_logs\\sql_validation_20250821_084451.txt"}}}