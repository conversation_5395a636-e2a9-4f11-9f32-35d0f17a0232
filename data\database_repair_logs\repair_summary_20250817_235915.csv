Timestamp,Operation,File,Line,Status,Rule Applied,SQL Validation,Error
2025-08-17T23:59:16.019912,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\example_usage.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.034562,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\example_usage.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'core'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.037536,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\example_usage.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.039536,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\example_usage.py,243,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'example_usage'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.042954,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Start_SelfHealer.py,2,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.045204,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Start_SelfHealer.py,3,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.047708,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\__init__.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'successful'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.049705,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\__init__.py,31,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'core'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.053507,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\__init__.py,32,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'core'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.055507,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\__init__.py,33,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'core'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.057508,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\__init__.py,34,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'core'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.060015,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\__init__.py,35,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'core'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.062877,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\__init__.py,36,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'core'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.065702,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.065702,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.071033,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.074152,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.074152,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pydantic'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.080434,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,110,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ','. (102) (SQLExecDirectW)"")",
2025-08-17T23:59:16.087589,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,113,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-17T23:59:16.090147,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,114,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-17T23:59:16.097756,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,186,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'knowledge'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.101759,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,195,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-17T23:59:16.101759,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,206,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ValidityRating, ID",
2025-08-17T23:59:16.112519,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,218,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: COUNT, EvidenceCount, FactID",
2025-08-17T23:59:16.115401,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,261,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ','. (102) (SQLExecDirectW)"")",
2025-08-17T23:59:16.118403,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,263,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-17T23:59:16.118403,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,277,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ','. (102) (SQLExecDirectW)"")",
2025-08-17T23:59:16.123717,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,279,success,SQL Validation Test,"SUCCESS - Tables: REF_Opinion, Columns: ",
2025-08-17T23:59:16.126717,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,293,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ','. (102) (SQLExecDirectW)"")",
2025-08-17T23:59:16.127484,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,295,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-17T23:59:16.127484,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,296,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-17T23:59:16.133895,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,340,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-17T23:59:16.142461,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.142461,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.151795,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.155410,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.155410,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.167725,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.174242,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py,25,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.182173,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py,66,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'structured'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.185172,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py,340,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'error'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.185172,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py,577,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'cache'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.192481,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py,2,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'log'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.193483,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.193483,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.200632,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.204353,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.208162,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'watchdog'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.221231,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'watchdog'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.231487,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py,28,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.241517,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py,29,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.242381,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py,329,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'application'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.242381,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py,390,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'log'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.242381,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py,479,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.255093,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py,508,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'error'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.262974,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py,707,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'memory'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.268422,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py,752,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'line'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.268422,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py,837,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'log'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.277385,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\generic_types.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.279386,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\generic_types.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.283184,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\generic_types.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.285760,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\generic_types.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'enum'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.292107,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.295262,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.299069,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.306188,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.311212,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'enum'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.324177,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,30,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'project_adapter'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.329539,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,31,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'error_monitor'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.336606,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,32,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'context_analyzer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.352310,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,33,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'solution_generator'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.356312,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,34,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'solution_validator'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.357313,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,35,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'learning_engine'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.367240,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,36,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'knowledge_integration'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.373066,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,42,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.376065,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,68,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'Optional'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Unclosed quotation mark after the character string 'Dict[str, Any] = None'. (105)"")",
2025-08-17T23:59:16.377068,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,132,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'file'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:16.399498,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,221,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.424070,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,370,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'analysis'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.428599,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,512,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'system'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.428599,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,517,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'every'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.428599,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py,542,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'all'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:16.428599,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_database_wrapper.py,7,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.454751,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_database_wrapper.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.466233,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.470832,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.489080,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.491080,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.497482,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.499481,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,26,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.499481,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,242,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-17T23:59:16.499481,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,268,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-17T23:59:16.499481,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,366,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'solution'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.534980,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,375,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""f.ID"" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""f.ValidityRating"" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""e.ID"" could not be bound. (4104)')",
2025-08-17T23:59:16.534980,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,376,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-17T23:59:16.534980,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,377,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-17T23:59:16.534980,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,402,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'solution'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.551479,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,410,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name, ID",
2025-08-17T23:59:16.552536,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,420,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""f.ID"" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""f.Name"" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""f.ValidityRating"" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""f.DataSource"" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""f.CreateDate"" could not be bound. (4104)')",
2025-08-17T23:59:16.563111,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,421,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-17T23:59:16.563111,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,431,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""e.ID"" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""e.Name"" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""e.Evidence"" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""e.DataSource"" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""e.CreateDate"" could not be bound. (4104)')",
2025-08-17T23:59:16.580685,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,432,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-17T23:59:16.580685,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,440,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""a.Name"" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""ev.EntityValue"" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""eav.CreateDate"" could not be bound. (4104)')",
2025-08-17T23:59:16.590135,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,441,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.593132,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,442,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-17T23:59:16.595132,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,443,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:16.595132,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,466,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ','. (102) (SQLExecDirectW)"")",
2025-08-17T23:59:16.605213,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,468,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-17T23:59:16.617169,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,469,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-17T23:59:16.617169,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,478,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""o.ID"" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""o.Name"" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""o.ValidityRating"" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""o.Opinion"" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""o.DataSource"" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""o.CreateDate"" could not be bound. (4104)')",
2025-08-17T23:59:16.633610,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,479,success,SQL Validation Test,"SUCCESS - Tables: REF_Opinion, Columns: ",
2025-08-17T23:59:16.642279,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,552,success,SQL Validation Test,"SUCCESS - Tables: REF_Category, Columns: Name, ID",
2025-08-17T23:59:16.646560,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,566,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name, ID",
2025-08-17T23:59:16.648564,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,580,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: Name, ID",
2025-08-17T23:59:16.648564,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,594,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, Name, ID",
2025-08-17T23:59:16.648564,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,603,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:16.648564,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,621,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.664697,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,631,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.664697,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py,2,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'healing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.676067,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.681833,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.682834,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.682834,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.714171,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.733020,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.737919,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.737919,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py,79,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'learning'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.746629,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py,97,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'healing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.748634,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py,248,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'error'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.748634,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py,364,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'patterns'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.759886,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py,445,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'patterns'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.764396,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py,577,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'disk'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.779422,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\project_adapter.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.779422,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\project_adapter.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.805931,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\project_adapter.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.805931,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\project_adapter.py,94,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'YAML'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.819311,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_generator.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.819311,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_generator.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.830201,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_generator.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.831340,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_generator.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.839738,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_generator.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'enum'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.841738,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_generator.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.841738,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_generator.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.853097,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_generator.py,25,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.853097,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_validator.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.863590,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_validator.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.868566,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_validator.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.870566,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_validator.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.870566,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_validator.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'enum'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.881641,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_validator.py,25,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.885916,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_validator.py,26,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.890648,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_validator.py,27,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.899598,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.899598,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.908785,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.912290,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.919881,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'websockets'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.925170,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.933286,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.933286,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.946411,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,27,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.948412,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,28,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.957562,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,29,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.957562,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,193,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'log'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.967409,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,643,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'every'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.968215,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,684,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'KnowledgeBase'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.977809,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,720,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'KnowledgeBase'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.980808,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,723,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.988813,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,751,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'KnowledgeBase'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.991970,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,766,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'KnowledgeBase'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:16.999293,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,769,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.004621,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,813,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'KnowledgeBase'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.004621,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,817,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'KnowledgeBase'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.014078,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,838,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.018659,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,856,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'ID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Name'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreateDate'. (207)"")",
2025-08-17T23:59:17.024862,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,857,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:17.034300,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,880,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.038762,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,881,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-17T23:59:17.041541,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,882,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:17.049010,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,915,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""f.Name"" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""f.ValidityRating"" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""f.DataSource"" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""f.CreateDate"" could not be bound. (4104)')",
2025-08-17T23:59:17.053985,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,916,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-17T23:59:17.058989,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,927,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""e.Name"" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""e.Evidence"" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""e.DataSource"" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""e.CreateDate"" could not be bound. (4104)')",
2025-08-17T23:59:17.064562,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,928,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-17T23:59:17.066121,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,972,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.066121,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,992,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Name'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'ValidityRating'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'DataSource'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreateDate'. (207)"")",
2025-08-17T23:59:17.080530,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,993,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-17T23:59:17.080530,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1010,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Name'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'ValidityRating'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Opinion'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'DataSource'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreateDate'. (207)"")",
2025-08-17T23:59:17.090575,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1011,success,SQL Validation Test,"SUCCESS - Tables: REF_Opinion, Columns: ",
2025-08-17T23:59:17.095581,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1022,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""e.Name"" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""e.CreateDate"" could not be bound. (4104)')",
2025-08-17T23:59:17.101332,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1023,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:17.103332,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1026,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.112571,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1027,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-17T23:59:17.113103,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1028,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:17.121244,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1104,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fact'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.128103,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1227,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'every'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.133625,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.140556,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,35,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'COLUMN_NAME'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'DATA_TYPE'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'IS_NULLABLE'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'COLUMN_DEFAULT'. (207)"")",
2025-08-17T23:59:17.145767,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,36,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.145767,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,67,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'name'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'create_date'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'modify_date'. (207)"")",
2025-08-17T23:59:17.157875,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,68,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.163566,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,93,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.169510,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,107,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'TABLE_NAME'. (207) (SQLExecDirectW)"")",
2025-08-17T23:59:17.175586,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,108,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.182411,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,2,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.187714,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.192933,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,17,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:17.192933,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,26,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:17.205801,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,33,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.211229,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py,2,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.216919,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py,27,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:17.217920,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py,49,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'name'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'create_date'. (207)"")",
2025-08-17T23:59:17.229096,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py,50,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.236433,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_simple_session_procedure.py,2,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.238373,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_simple_session_procedure.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.249438,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_simple_session_procedure.py,28,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable ""@Limit"". (137) (SQLExecDirectW)')",
2025-08-17T23:59:17.249438,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_simple_session_procedure.py,36,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:17.260477,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,5,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.272554,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'auto'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.273553,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,35,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME",
2025-08-17T23:59:17.305228,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,39,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: name, type",
2025-08-17T23:59:17.305228,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,42,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_Entity'. (207) (SQLExecDirectW)"")",
2025-08-17T23:59:17.320302,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,66,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.328893,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,87,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.333766,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,89,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""ku.TABLE_NAME"" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier ""ku.COLUMN_NAME"" could not be bound. (4104)')",
2025-08-17T23:59:17.333766,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,90,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.347501,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,91,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.348508,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,101,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.359007,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,102,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.365942,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,104,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.366946,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,133,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.377014,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,134,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.380695,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,147,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.391552,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,148,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.397788,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,150,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.397788,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,159,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.413443,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,160,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.424500,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'auto'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.434039,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.445086,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,45,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name, ID",
2025-08-17T23:59:17.445086,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,48,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-17T23:59:17.461771,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,63,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.467412,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,64,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-17T23:59:17.473091,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,65,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:17.479659,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,76,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.486024,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,102,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: Name, ValidityRating",
2025-08-17T23:59:17.487682,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,120,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-17T23:59:17.487682,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,132,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.503725,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,170,success,SQL Validation Test,"SUCCESS - Tables: REF_Opinion, Columns: ",
2025-08-17T23:59:17.503725,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,182,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.517502,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,222,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable ""@Limit"". (137) (SQLExecDirectW)')",
2025-08-17T23:59:17.517502,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,234,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-17T23:59:17.527019,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,235,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-17T23:59:17.534644,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,249,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.539793,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,296,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable ""@Limit"". (137) (SQLExecDirectW)')",
2025-08-17T23:59:17.546387,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,303,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-17T23:59:17.548850,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,313,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable ""@Limit"". (137) (SQLExecDirectW)')",
2025-08-17T23:59:17.559294,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,321,success,SQL Validation Test,"SUCCESS - Tables: REF_Opinion, Columns: ",
2025-08-17T23:59:17.568665,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,333,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable ""@Limit"". (137) (SQLExecDirectW)')",
2025-08-17T23:59:17.571668,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,341,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-17T23:59:17.581645,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql,342,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-17T23:59:17.589205,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.592768,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,51,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable ""@Limit"". (137) (SQLExecDirectW)')",
2025-08-17T23:59:17.602223,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,66,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:17.602223,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,67,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.616464,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,68,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-17T23:59:17.619262,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,70,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:17.631627,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,72,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.635621,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,73,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-17T23:59:17.642761,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,75,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:17.648562,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,87,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.657521,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,119,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable ""@SessionID"". (137) (SQLExecDirectW)')",
2025-08-17T23:59:17.663534,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,123,success,SQL Validation Test,"SUCCESS - Tables: , Columns: ",
2025-08-17T23:59:17.670096,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,135,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:17.679241,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,136,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.680240,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,137,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-17T23:59:17.680240,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,138,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:17.700471,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,149,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.705184,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,204,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:17.713745,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,205,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.718779,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,206,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-17T23:59:17.718779,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,208,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:17.734303,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,210,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.740549,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,211,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-17T23:59:17.740549,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,213,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:17.756191,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,225,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.761162,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,262,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable ""@Limit"". (137) (SQLExecDirectW)')",
2025-08-17T23:59:17.769299,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,277,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:17.773893,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,278,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.784333,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,279,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-17T23:59:17.784333,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,281,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:17.802984,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,283,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.804983,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,284,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-17T23:59:17.814573,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql,286,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:17.821711,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'CamelCase'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.834175,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql,20,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'references'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:17.836186,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql,22,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'any'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:17.847507,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql,37,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.847507,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql,46,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.863873,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql,55,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.863873,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql,64,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.877027,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql,73,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.877027,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql,82,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.892559,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql,93,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'EXISTING'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.892559,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql,98,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'S_SYS_SelfHealer_RecentSessions_P'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.908211,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql,102,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'S_SYS_SelfHealer_SessionAnalytics_P'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.919350,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql,122,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.924110,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql,132,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.924110,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql,134,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'any'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:17.942201,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\get_knowledgebase_schema.py,7,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.955329,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\get_knowledgebase_schema.py,26,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.963401,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\get_knowledgebase_schema.py,27,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.970787,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\get_knowledgebase_schema.py,33,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.975521,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\get_knowledgebase_schema.py,77,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:17.986159,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\get_knowledgebase_schema.py,220,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'Column'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:17.986159,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\install_and_test.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.001556,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\install_and_test.py,28,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.009143,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\install_and_test.py,121,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'get_knowledgebase_schema'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.020023,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'procedures'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.024391,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,34,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.033584,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,46,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.033584,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,64,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Duration'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.048932,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,65,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:18.048932,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,77,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'if'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:18.064954,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,81,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Success'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.064954,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,82,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:18.083274,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,97,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'if'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:18.086277,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,101,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Simple'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.097725,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,102,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:18.097725,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,115,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'if'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:18.114218,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,119,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Millisecond'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.116730,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,120,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:18.128818,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,132,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'if'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:18.134935,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,143,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '10'. (102) (SQLExecDirectW)"")",
2025-08-17T23:59:18.142508,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,149,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:18.146672,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,156,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'stored'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.156737,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql,157,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.166779,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,5,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'stored'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.173613,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'SESSION'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.179685,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,28,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.189363,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,61,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:18.200431,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,62,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.208162,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,63,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-17T23:59:18.213127,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,65,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:18.224131,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,67,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.227493,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,68,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-17T23:59:18.238280,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,70,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:18.243625,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,80,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'RECENT'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.243625,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,86,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.260944,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,96,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable ""@Limit"". (137) (SQLExecDirectW)')",
2025-08-17T23:59:18.269114,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,125,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:18.269114,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,126,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.285661,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,127,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-17T23:59:18.285661,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,129,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:18.305230,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,131,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.306231,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,132,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-17T23:59:18.314747,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql,134,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:18.333441,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\check_healer_status.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.339438,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\check_healer_status.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.350498,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_error_criteria.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.354403,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_error_criteria.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.364863,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_error_criteria.py,29,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.364863,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_self_healer.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.384024,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_self_healer.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.392470,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_self_healer.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.398562,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_self_healer.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.404595,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_self_healer_flow.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.417972,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_self_healer_flow.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.424016,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_self_healer_flow.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.429738,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_self_healer_flow.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.442128,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\fix_healer_sync.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.442128,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\force_error_detection.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.456477,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\force_error_detection.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.466825,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\force_error_detection.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.476918,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\force_error_detection.py,135,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'log'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.487657,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_documentation.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.488567,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_documentation.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.504035,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_documentation.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.513188,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_documentation.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.520863,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_documentation.py,40,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.520863,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_naming_convention.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.536760,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_naming_convention.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.550225,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_naming_convention.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.550225,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_naming_convention.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '_public'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.572158,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_project_files.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.583626,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_project_files.py,25,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.591846,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_project_files.py,26,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.591846,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_project_files.py,27,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.611188,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_project_files.py,81,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Python'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.617068,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_project_files.py,794,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Archive'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.617068,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_project_files.py,800,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.633313,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_script_paths.py,5,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'root'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.643255,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_script_paths.py,7,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'root'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.643255,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_script_paths.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Scripts'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.658886,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_script_paths.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.673658,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_script_paths.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.678727,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_script_paths.py,160,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'root'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.678727,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_script_paths.py,207,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.696975,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_venv_files.py,5,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.710601,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_venv_files.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.715607,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_venv_files.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.725686,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_workspace_folders.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.739265,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_workspace_folders.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.751069,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_workspace_folders.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.760573,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_workspace_folders.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.770593,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Fix table name: REF_Facts → REF_Fact,,
2025-08-17T23:59:18.778484,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Fix table name: REF_Opinions → REF_Opinion,,
2025-08-17T23:59:18.786437,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Fix table name: REF_Sources → REF_Source,,
2025-08-17T23:59:18.794441,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Fix table name: REF_Categories → REF_Category,,
2025-08-17T23:59:18.798943,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.810322,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.819865,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.829136,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.845378,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,118,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'spacing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.858339,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_script_paths.py,4,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'their'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.864769,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_script_paths.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.875614,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_script_paths.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.875614,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_script_paths.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.906169,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_script_paths.py,32,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.916432,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.927827,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.936345,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,38,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'TABLE_NAME'. (207) (SQLExecDirectW)"")",
2025-08-17T23:59:18.944540,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,39,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.954048,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,77,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:18.964626,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,87,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:18.975322,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\clean_commit_messages.py,7,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.984419,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\clean_commit_messages.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:18.992477,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\clean_commit_messages.py,88,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'gitignore'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.014481,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\clean_commit_messages.py,93,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'to'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:19.027992,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\compare_readme_files.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.032051,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\compare_readme_files.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.048212,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\compare_readme_files.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.051907,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\comprehensive_repo_scan.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.064469,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\comprehensive_repo_scan.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.072865,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\comprehensive_repo_scan.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.085910,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\comprehensive_repo_scan.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.085910,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\config_backup.py,1,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.100878,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\config_backup.py,4,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.115841,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\config_backup.py,5,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pydantic'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.127870,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\create_documentation_consolidation_plan.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.136721,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\create_documentation_consolidation_plan.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.169770,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\create_documentation_consolidation_plan.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.180862,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\create_documentation_consolidation_plan.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.198890,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\create_documentation_consolidation_plan.py,67,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.208122,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,0,success,Fix table name: REF_Entities → REF_Entity,,
2025-08-17T23:59:19.220732,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.232304,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.241994,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.251128,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.256346,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,145,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'log'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.268479,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,262,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:19.278296,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,268,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-17T23:59:19.278296,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,269,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-17T23:59:19.294182,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\debug_analyze_files.py,5,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.302751,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\debug_analyze_files.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.316514,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\delete_obsolete_files.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.323818,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\delete_obsolete_files.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.340590,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\delete_obsolete_files.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.346458,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\delete_obsolete_files.py,25,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.360559,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\delete_obsolete_files.py,140,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'cross'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:19.367599,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\delete_obsolete_files.py,157,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'analysis'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.367599,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components.py,6,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.392747,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.399313,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.408259,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.417959,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components.py,77,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.429172,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components.py,129,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'scanning'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.433178,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components2.py,6,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.451942,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components2.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.465156,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components2.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.487798,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components2.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.519331,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components2.py,77,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.541488,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components2.py,129,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'scanning'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.553977,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\dev_publish.py,5,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'public'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:19.564797,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\dev_publish.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.571509,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\dev_publish.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.581699,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\dev_publish.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.598770,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\dev_publish.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.611193,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\dev_publish.py,28,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'N8N_Builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.621618,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: REF_Entities → REF_Entity,,
2025-08-17T23:59:19.628292,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: REF_Attributes → REF_Attribute,,
2025-08-17T23:59:19.637327,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: REF_EntityValues → REF_EntityValue,,
2025-08-17T23:59:19.645661,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue,,
2025-08-17T23:59:19.645661,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: XRF_Entity_AttributeValue → XRF_EntityAttributeValue,,
2025-08-17T23:59:19.662213,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: REF_Facts → REF_Fact,,
2025-08-17T23:59:19.671032,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: REF_Opinions → REF_Opinion,,
2025-08-17T23:59:19.674263,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: REF_Sources → REF_Source,,
2025-08-17T23:59:19.687584,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: REF_Categories → REF_Category,,
2025-08-17T23:59:19.702000,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.711310,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.716828,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.729303,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Scripts'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.741048,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,25,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Scripts'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.741048,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\execute_separation.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.760491,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\execute_separation.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.770841,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\execute_separation.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.786475,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\execute_separation.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.798132,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\final_documentation_validation.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.811874,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\final_documentation_validation.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.815732,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\final_documentation_validation.py,25,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.836379,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\final_documentation_validation.py,26,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.861943,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\final_documentation_validation.py,292,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.876534,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_analyze_files.py,6,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.890622,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_analyze_files.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.896107,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_analyze_files.py,75,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.907331,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_analyze_files.py,76,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.917336,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_analyze_files.py,290,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'backup'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:19.928309,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_healer_logging.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.937813,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_script_references.py,7,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'within'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.947857,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_script_references.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.959125,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_script_references.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.970243,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_script_references.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.970243,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_script_references.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:19.990028,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\generate_consolidation_report.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.005531,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\generate_consolidation_report.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.017621,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\generate_consolidation_report.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.024545,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\generate_consolidation_report.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.036500,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\generate_consolidation_report.py,121,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'cross'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:20.048907,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\generate_consolidation_report.py,158,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.048907,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\generate_process_flow.py,3,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.072012,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\github_repository_setup.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.086012,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\github_repository_setup.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.089970,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\github_repository_setup.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.114574,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\github_repository_setup.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.117446,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\github_repository_setup.py,368,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'README'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.133739,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\github_repository_setup.py,370,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'API'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.152028,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\improve_documentation.py,6,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'ngrok'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.162016,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\improve_documentation.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.173046,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\improve_documentation.py,25,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.178292,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\improve_documentation.py,26,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.194192,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\improve_documentation.py,27,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.206787,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\improve_documentation.py,35,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'all'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:20.222414,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\improve_documentation.py,36,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'ngrok'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.231260,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\improve_documentation.py,251,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'GETTING_STARTED'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.246713,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py,5,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '_public'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.252141,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py,6,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'analyze_naming_convention'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.267301,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.273591,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.286638,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.300446,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.300446,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py,26,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '_public'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.325579,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py,117,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sync'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.338790,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py,193,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '_public'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.345100,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\prepare_public_release.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.361553,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\prepare_public_release.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.367430,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\prepare_public_release.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.378922,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\prepare_public_release.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.392294,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\prepare_public_release.py,226,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.392294,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\pre_commit_cleanup.py,6,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.409775,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\pre_commit_cleanup.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.429071,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\pre_commit_cleanup.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.438586,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\pre_commit_cleanup.py,267,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'here'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.449789,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\pre_execution_verification.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.469540,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\pre_execution_verification.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.472007,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\pre_execution_verification.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.491717,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\pre_execution_verification.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.500720,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\project_cleanup_manager.py,6,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.517062,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\project_cleanup_manager.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.524113,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\project_cleanup_manager.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.539868,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\project_cleanup_manager.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.553786,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\quick_test_research.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.565344,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\quick_test_research.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.571781,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\restart_n8n_clean.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.581809,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\safe_analyze_project_files.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.604229,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\safe_analyze_project_files.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.618066,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\safe_cleanup.py,4,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.621593,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sanitize_documentation.py,5,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'documentation'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.641535,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sanitize_documentation.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.648045,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sanitize_documentation.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.659204,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sanitize_documentation.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.675128,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sanitize_documentation.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.687097,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\scan_md_for_private_refs.py,7,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.690786,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\scan_md_for_private_refs.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.710440,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\scan_md_for_private_refs.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.718427,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\setup_log_rotation.py,6,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.729351,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\setup_log_rotation.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.741784,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\setup_log_rotation.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.754216,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\setup_log_rotation.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.769223,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\setup_log_rotation.py,132,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.769223,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\setup_log_rotation.py,154,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'N8N'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.791907,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\setup_log_rotation.py,155,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.802782,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\shutdown.py,7,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.812554,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\shutdown.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.829028,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\shutdown.py,134,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.843672,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_database_fixer.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.854131,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_database_fixer.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.865790,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Fix table name: REF_Entities → REF_Entity,,
2025-08-17T23:59:20.880942,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Fix table name: REF_Facts → REF_Fact,,
2025-08-17T23:59:20.887262,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Fix table name: REF_Opinions → REF_Opinion,,
2025-08-17T23:59:20.896407,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Fix table name: REF_Sources → REF_Source,,
2025-08-17T23:59:20.908380,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Fix table name: REF_Categories → REF_Category,,
2025-08-17T23:59:20.922844,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.925150,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.941709,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.955312,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.967470,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.987212,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Scripts'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:20.987212,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,49,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'SQL'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.002856,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,70,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '='. (102) (SQLExecDirectW)"")",
2025-08-17T23:59:21.028814,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,72,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '='. (102) (SQLExecDirectW)"")",
2025-08-17T23:59:21.032488,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,98,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '='. (102) (SQLExecDirectW)"")",
2025-08-17T23:59:21.053007,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\streamlined_documentation_cleanup.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.053007,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\streamlined_documentation_cleanup.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.073881,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\streamlined_documentation_cleanup.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.093007,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\streamlined_documentation_cleanup.py,25,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.096013,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\streamlined_documentation_cleanup.py,227,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'cross'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:21.116335,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_detection_simple.py,4,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.122519,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_detection_simple.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.143285,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_enhanced_sync.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.148865,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_enhanced_sync.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.165940,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_enhanced_sync.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.175224,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_enhanced_sync.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.191659,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_llm_connection.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.209387,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_llm_connection.py,33,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'N8N'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.218200,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_llm_connection.py,76,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.232169,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_logging.py,3,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.248159,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_mcp_research.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.259811,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_mcp_research.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.270447,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_mcp_research.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.282029,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_safe_cleanup.py,6,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.346420,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_safe_cleanup.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.361618,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_safe_cleanup.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Scripts'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.374187,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.379725,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.415011,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py,37,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'TABLE_NAME'. (207) (SQLExecDirectW)"")",
2025-08-17T23:59:21.461929,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py,38,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.477961,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_self_healer_real_world.py,6,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'detection'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.490384,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_self_healer_real_world.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.493388,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_self_healer_real_world.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.565954,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_self_healer_real_world.py,54,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'backup'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:21.579909,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_self_healer_real_world.py,58,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'backup'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:21.610415,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_self_healer_real_world.py,100,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.625178,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_verification_systems.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.633174,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_verification_systems.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.643361,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_verification_systems.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.656932,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_verification_systems.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.674014,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_verification_systems.py,153,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.674014,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_verification_systems.py,154,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.700171,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_workflow_generator.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.713874,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_workflow_generator.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.719766,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_workflow_generator.py,77,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.739839,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_workflow_generator.py,135,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.755629,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py,3,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Script'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.769300,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.773351,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.794305,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.801734,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.822004,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py,47,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'action'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.835746,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py,97,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'references'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:21.836202,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py,203,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.860421,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py,220,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'completed'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.866776,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py,225,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'log'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.879223,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py,232,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'folder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.893576,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.904150,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.924158,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.932781,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,26,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.944871,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,112,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'TABLE_NAME'. (207) (SQLExecDirectW)"")",
2025-08-17T23:59:21.965826,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,113,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:21.984862,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,132,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'COLUMN_NAME'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'DATA_TYPE'. (207)"")",
2025-08-17T23:59:21.991008,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,133,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.005468,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,181,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.018559,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,203,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.047989,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_links.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.057735,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_links.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.072225,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_links.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.081828,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_links.py,53,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.095049,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.116007,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py,26,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.120511,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py,27,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.145100,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py,28,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.156330,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py,82,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'markdown'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.171385,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py,91,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'markdown'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.178853,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py,286,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'to'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:22.192116,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py,335,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'all'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:22.204733,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py,394,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'tunnel'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.225483,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,2,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.241593,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.259362,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,52,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable ""@Limit"". (137) (SQLExecDirectW)')",
2025-08-17T23:59:22.272885,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,60,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:22.286564,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,84,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: name",
2025-08-17T23:59:22.299111,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\verification_pipeline.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.318706,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\verification_pipeline.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.331616,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\verification_pipeline.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.342687,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\verification_pipeline.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.357058,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_documentation.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.372546,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_documentation.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.380395,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_documentation.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.394065,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_documentation.py,34,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.413912,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_project_files.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.424696,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_project_files.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.440320,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_project_files.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.459507,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_project_files.py,75,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Python'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.466302,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_project_files.py,788,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Archive'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.491393,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_project_files.py,794,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.508079,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_script_paths.py,5,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'root'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.520917,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_script_paths.py,7,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'root'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.526283,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_script_paths.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Scripts'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.546986,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_script_paths.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.560797,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_script_paths.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.569575,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_script_paths.py,160,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'root'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.592770,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_script_paths.py,207,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.606302,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_venv_files.py,6,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.616690,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_venv_files.py,7,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.627519,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_workspace_folders.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.648327,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_workspace_folders.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.657197,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_workspace_folders.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.677668,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\clean_commit_messages.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.679666,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\clean_commit_messages.py,82,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'gitignore'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.705280,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\clean_commit_messages.py,87,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'to'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:22.714849,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\compare_readme_files.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.727353,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\compare_readme_files.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.747591,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\comprehensive_repo_scan.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.761703,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\comprehensive_repo_scan.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.768516,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\comprehensive_repo_scan.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.783199,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\create_documentation_consolidation_plan.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.805861,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\create_documentation_consolidation_plan.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.813676,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\create_documentation_consolidation_plan.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.843173,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\create_documentation_consolidation_plan.py,61,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.860483,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\debug_analyze_files.py,7,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.867491,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\delete_obsolete_files.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.880208,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\delete_obsolete_files.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.896388,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\delete_obsolete_files.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.910402,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\delete_obsolete_files.py,134,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'cross'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:22.928098,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\delete_obsolete_files.py,151,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'analysis'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.954510,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\dev_publish.py,5,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'public'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:22.969004,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\dev_publish.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.982635,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\dev_publish.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:22.988340,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\dev_publish.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.015355,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\dev_publish.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'N8N_Builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.019661,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\execute_separation.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.034940,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\execute_separation.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.052298,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\execute_separation.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.069900,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\final_documentation_validation.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.082858,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\final_documentation_validation.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.097567,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\final_documentation_validation.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.112329,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\final_documentation_validation.py,286,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.128846,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\fix_analyze_files.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.148384,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\fix_analyze_files.py,69,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.167228,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\fix_analyze_files.py,70,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.174014,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\fix_analyze_files.py,284,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'backup'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:23.195566,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\fix_script_references.py,7,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'within'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.213011,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\fix_script_references.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.220103,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\fix_script_references.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.242098,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\fix_script_references.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.248048,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\generate_consolidation_report.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.270234,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\generate_consolidation_report.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.276599,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\generate_consolidation_report.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.297645,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\generate_consolidation_report.py,115,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'cross'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:23.332055,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\generate_consolidation_report.py,152,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.347347,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\github_repository_setup.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.367432,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\github_repository_setup.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.376495,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\github_repository_setup.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.397663,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\github_repository_setup.py,362,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'README'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.404205,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\github_repository_setup.py,364,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'API'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.426320,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\improve_documentation.py,6,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'ngrok'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.435084,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\improve_documentation.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.454925,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\improve_documentation.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.467418,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\improve_documentation.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.477788,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\improve_documentation.py,29,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'all'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:23.489845,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\improve_documentation.py,30,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'ngrok'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.514653,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\improve_documentation.py,245,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'GETTING_STARTED'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.523219,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\migrate_naming_convention.py,5,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '_public'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.540316,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\migrate_naming_convention.py,6,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'analyze_naming_convention'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.551445,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\migrate_naming_convention.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.567482,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\migrate_naming_convention.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.587602,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\migrate_naming_convention.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.598757,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\migrate_naming_convention.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '_public'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.618207,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\migrate_naming_convention.py,111,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sync'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.636506,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\migrate_naming_convention.py,187,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '_public'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.653083,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\prepare_public_release.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.670139,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\prepare_public_release.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.685189,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\prepare_public_release.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.702658,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\prepare_public_release.py,220,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.708793,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\pre_commit_cleanup.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.728402,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\pre_commit_cleanup.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.746320,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\pre_commit_cleanup.py,261,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'here'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.753686,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\pre_execution_verification.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.767536,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\pre_execution_verification.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.795913,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\pre_execution_verification.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.807078,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\project_cleanup_manager.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.825066,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\project_cleanup_manager.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.840208,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\project_cleanup_manager.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.860429,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\sanitize_documentation.py,5,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'documentation'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.867337,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\sanitize_documentation.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.891756,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\sanitize_documentation.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.903353,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\sanitize_documentation.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.923479,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\scan_md_for_private_refs.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.940580,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\scan_md_for_private_refs.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.955517,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\setup_log_rotation.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.973215,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\setup_log_rotation.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:23.988993,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\setup_log_rotation.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.007691,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\setup_log_rotation.py,126,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.022443,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\setup_log_rotation.py,148,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'N8N'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.037941,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\setup_log_rotation.py,149,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.061566,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\shutdown.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.080317,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\shutdown.py,128,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.083448,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\streamlined_documentation_cleanup.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.111527,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\streamlined_documentation_cleanup.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.123826,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\streamlined_documentation_cleanup.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.139677,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\streamlined_documentation_cleanup.py,221,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'cross'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:24.158654,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_detection_simple.py,5,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.175373,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_enhanced_sync.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.182595,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_enhanced_sync.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.229631,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_enhanced_sync.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.249235,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_safe_cleanup.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.266268,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_safe_cleanup.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Scripts'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.279900,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_verification_systems.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.298518,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_verification_systems.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.305673,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_verification_systems.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.331045,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_verification_systems.py,147,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.348911,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_verification_systems.py,148,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.365743,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py,3,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Script'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.374451,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.393689,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.403137,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.425307,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py,41,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'action'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.442111,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py,91,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'references'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:24.458084,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py,197,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.467623,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py,214,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'completed'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.489481,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py,219,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'log'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.507428,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py,226,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'folder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.523728,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_links.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.524254,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_links.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.555943,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_links.py,47,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.571345,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_quality.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.586609,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_quality.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.607227,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_quality.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.622026,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_quality.py,76,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'markdown'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.634889,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_quality.py,85,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'markdown'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.652808,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_quality.py,280,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'to'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:24.667764,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_quality.py,329,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'all'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:24.678474,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_quality.py,388,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'tunnel'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.704170,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\verification_pipeline.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.708577,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\verification_pipeline.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.736762,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\verification_pipeline.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.748622,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py,5,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.770479,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py,6,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.777767,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py,7,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.802096,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.817551,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.833367,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.838744,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.864542,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.878467,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.895676,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.904861,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.927699,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\investigate_healer_disconnect.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.932546,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\investigate_healer_disconnect.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.961136,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\investigate_healer_disconnect.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.968159,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\investigate_healer_disconnect.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:24.992273,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\quick_healer_check.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.011029,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\quick_healer_check.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.032687,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.047796,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.071026,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'test_system_health'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.085440,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'test_stored_procedures'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.099104,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py,100,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.111526,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py,101,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.130326,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py,102,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.148493,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py,112,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.172787,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py,133,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.178548,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py,166,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'integration'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.204793,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py,175,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'timestamp'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.210800,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_actual_response.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.239981,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_actual_response.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.259066,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_actual_response.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.274946,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_actual_response.py,28,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.289351,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_actual_response.py,34,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.299905,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_actual_response.py,46,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.328969,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_actual_response.py,51,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.339487,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_actual_response.py,52,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.365100,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_integration.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.382888,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_integration.py,61,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.399224,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_integration.py,125,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.408819,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_integration.py,196,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.423803,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_integration.py,231,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.444413,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_integration.py,232,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.468033,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_self_healer_flow.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.486224,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_self_healer_flow.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.493806,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_self_healer_flow.py,41,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.520640,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_db.py,2,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.531139,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_db.py,11,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:25.552576,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_db.py,35,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.569875,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_improvements.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.586500,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_improvements.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.602143,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_integration.py,2,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.617411,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_integration.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.637546,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_integration.py,70,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.651320,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dependencies.py,6,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.677625,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dependencies.py,7,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pydantic'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.696279,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dependencies.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dotenv'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.711013,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dependencies.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'rich'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.733821,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py,4,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.750283,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.768592,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.773937,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.802037,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.806920,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.834598,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py,26,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.851740,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py,27,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.868758,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py,33,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.892808,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py,34,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Webhook'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.906089,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_env.py,3,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dotenv'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.930928,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_env.py,5,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.946422,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_env.py,7,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pydantic'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.966970,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_filesystem_utilities.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.973161,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_filesystem_utilities.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:25.991135,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_filesystem_utilities.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.019033,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_filesystem_utilities.py,104,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.031365,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_filesystem_utilities.py,453,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'setUp'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.053421,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_filesystem_utilities.py,514,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.069962,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_filesystem_utilities.py,549,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'filename'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.086916,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_filesystem_utilities.py,613,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'creation'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.107925,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.113550,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.139931,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.162736,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,28,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.180593,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,33,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.197119,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,34,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.211409,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,39,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.228747,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,40,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.254730,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,166,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.272736,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,168,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.279676,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,172,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.303911,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,177,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.314521,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,178,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.339916,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,183,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.351033,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,184,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.368830,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,291,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'LLM'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.394572,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,298,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'response'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.399625,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py,367,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'successfully'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.429969,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.448853,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.457115,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.485473,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py,249,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.491629,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py,257,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: COUNT, count",
2025-08-17T23:59:26.520486,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_integration.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.538401,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_integration.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.555306,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_integration.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.573001,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_integration.py,25,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'unittest'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.590399,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_integration.py,32,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.604375,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_integration.py,33,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.673790,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_iteration_methods.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.738025,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_iteration_methods.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.784107,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_iteration_methods.py,24,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'file'. (156) (SQLExecDirectW)"")",
2025-08-17T23:59:26.799033,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_iteration_methods.py,294,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'scratch'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.820561,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_iteration_methods.py,385,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Inventory'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.830515,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_iteration_methods.py,394,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Inventory'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.851052,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_iteration_methods.py,395,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Inventory'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.868815,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,0,success,Fix table name: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue,,
2025-08-17T23:59:26.891916,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.909734,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.928093,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.946610,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,57,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'connection'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.961398,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,85,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:26.978677,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,129,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name, ID",
2025-08-17T23:59:26.992300,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,136,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-17T23:59:27.018313,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,361,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: name",
2025-08-17T23:59:27.033316,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,394,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'TABLE_NAME'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'TABLE_TYPE'. (207)"")",
2025-08-17T23:59:27.054927,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,395,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.069508,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_live_self_healer.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.082433,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_live_self_healer.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.117048,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_live_self_healer.py,71,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.141736,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_log_rotation.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.158287,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_log_rotation.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.178734,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_log_rotation.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.194457,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_database.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.212490,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_database.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.222485,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_research.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.238917,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_research.py,28,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.270737,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_research.py,29,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.280982,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_research.py,30,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.304760,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_research.py,116,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.320461,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_research.py,157,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.331301,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_research.py,158,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.362742,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_n8n_builder_unit.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'unittest'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.369032,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_n8n_builder_unit.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.408170,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_n8n_builder_unit.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.439865,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_n8n_builder_unit.py,19,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.458989,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_n8n_builder_unit.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.469076,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,0,success,Fix table name: REF_EntityValues → REF_EntityValue,,
2025-08-17T23:59:27.492785,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.508578,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,30,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'COLUMN_NAME'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'DATA_TYPE'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'IS_NULLABLE'. (207)"")",
2025-08-17T23:59:27.524855,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,31,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.535376,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,53,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:27.557334,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,67,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '5'. (102) (SQLExecDirectW)"")",
2025-08-17T23:59:27.579967,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,72,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:27.601274,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,132,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.616741,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,150,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityValue'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'NumericValue'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'ValueUnits'. (207)"")",
2025-08-17T23:59:27.636236,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,151,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-17T23:59:27.649597,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,179,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'procedures'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.677477,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_api.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.695767,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_api.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.706383,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_api.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.731336,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_api.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.749838,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_api.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.772417,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_cleanup.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.794381,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_cleanup.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.816310,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_cleanup.py,18,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Scripts'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.832290,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_management.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.852006,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_management.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.870683,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_management.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.894337,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_management.py,316,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dict'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.902616,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_research_quality.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.931332,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_research_quality.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.946699,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_research_quality.py,63,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.971828,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_research_quality.py,71,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'inventory'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:27.990485,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_research_quality.py,98,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.009196,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_research_quality.py,99,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.029687,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_fix.py,10,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.048706,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_fix.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.068895,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_fix.py,38,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.087227,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_fix.py,76,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.097051,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_fix.py,128,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.116059,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_integration.py,11,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.148842,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_integration.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.166540,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_integration.py,42,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'core'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.184821,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_integration.py,111,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'every'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.205128,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_integration.py,125,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dashboard'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.220835,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_rescan.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.245446,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_rescan.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.264480,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_smart_logging.py,8,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.281576,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_smart_logging.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.304465,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.322328,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.338152,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py,57,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'name'. (207) (SQLExecDirectW)"")",
2025-08-17T23:59:28.361964,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py,58,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.385097,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_system_health.py,13,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.398835,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_system_health.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.423829,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_system_health.py,15,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.446796,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_system_health.py,20,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.460817,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_system_health.py,21,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.486257,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_system_health.py,22,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.504498,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_system_health.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.527056,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_version_management.py,14,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.532570,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_version_management.py,16,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.569717,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_version_management.py,17,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.586088,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_version_management.py,23,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.615500,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_version_management.py,24,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)"")",
2025-08-17T23:59:28.633144,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_version_management.py,190,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)"")",
