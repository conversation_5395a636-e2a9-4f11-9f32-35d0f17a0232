Timestamp,Operation,File,Line,Status,Rule Applied,SQL Validation,Error
2025-08-18T00:07:10.689206,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,109,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, REF_Fact, Columns: FLOAT, CreateDate, DataSource, e, CAST, OverallAverage, AVG, Name, EvidenceCount, f, COUNT, ID, OVER, ValidityRating",
2025-08-18T00:07:10.692827,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,109,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, REF_Fact, Columns: FLOAT, CreateDate, DataSource, e, CAST, OverallAverage, AVG, Name, EvidenceCount, f, COUNT, ID, OVER, ValidityRating",
2025-08-18T00:07:10.694329,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,194,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-18T00:07:10.696333,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,194,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-18T00:07:10.698333,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,206,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.699835,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,218,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.701838,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,226,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.703864,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,260,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.706315,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,260,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.710314,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,276,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.713314,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,276,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.715096,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,292,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.717095,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,292,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.720489,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,329,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: END, AVG, Name, Configuration, ELSE, CAST, THEN, f, JSON_Parsing, Database, LIKE, CASE, Other, Workflow, JSON, AvgEffectiveness, WHEN, Network, Category, FLOAT, ValidityRating",
2025-08-18T00:07:10.722488,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,329,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: END, AVG, Name, Configuration, ELSE, CAST, THEN, f, JSON_Parsing, Database, LIKE, CASE, Other, Workflow, JSON, AvgEffectiveness, WHEN, Network, Category, FLOAT, ValidityRating",
2025-08-18T00:07:10.732544,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,241,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-18T00:07:10.734777,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,241,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-18T00:07:10.736777,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,267,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-18T00:07:10.738776,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,267,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-18T00:07:10.772879,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,374,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.803711,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,374,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.848334,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,396,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.897904,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,410,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.913101,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,419,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.915980,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,419,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.918979,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,430,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.921408,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,430,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.924413,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,439,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.926412,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,439,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.929413,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,465,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.932413,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,465,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.934415,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,477,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.937459,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,477,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.940394,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,552,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.942393,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,559,success,SQL Validation Test,"SUCCESS - Tables: REF_Category, Columns: ",
2025-08-18T00:07:10.946394,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,566,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.949393,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,573,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T00:07:10.951393,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,580,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.954461,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,587,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-18T00:07:10.957092,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,594,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.960091,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,602,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-18T00:07:10.963095,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,602,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-18T00:07:10.965095,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,610,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-18T00:07:10.968095,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,620,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.970094,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,620,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.974095,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,630,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-18T00:07:10.977674,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,630,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-18T00:07:10.985872,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,855,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.988872,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,855,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.992592,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,872,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:10.995215,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,872,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.004598,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,914,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.036864,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,914,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.084210,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,926,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.112213,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,926,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.129779,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,991,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.156310,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,991,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.160610,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1009,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.163571,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1009,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.177543,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1021,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.185118,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1021,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.195746,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,34,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_EntityValue'. (207) (SQLExecDirectW)"")",
2025-08-18T00:07:11.199867,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,34,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_EntityValue'. (207) (SQLExecDirectW)"")",
2025-08-18T00:07:11.210658,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,66,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: name, create_date, modify_date",
2025-08-18T00:07:11.221038,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,66,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: name, create_date, modify_date",
2025-08-18T00:07:11.225500,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,93,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: name",
2025-08-18T00:07:11.231212,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,106,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T00:07:11.234216,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,106,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T00:07:11.239260,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,119,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.246259,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,9,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T00:07:11.249813,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,17,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T00:07:11.256403,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,26,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T00:07:11.259404,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,33,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-18T00:07:11.271327,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py,48,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: name, create_date",
2025-08-18T00:07:11.315900,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py,48,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: name, create_date",
2025-08-18T00:07:11.337272,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,113,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable ""@count"". (137) (SQLExecDirectW)')",
2025-08-18T00:07:11.386250,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Fix table name: REF_Facts → REF_Fact,,
2025-08-18T00:07:11.388695,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Fix table name: REF_Opinions → REF_Opinion,,
2025-08-18T00:07:11.391158,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Fix table name: REF_Sources → REF_Source,,
2025-08-18T00:07:11.393963,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Fix table name: REF_Categories → REF_Category,,
2025-08-18T00:07:11.414639,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,117,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '\\1'. (102) (SQLExecDirectW)"")",
2025-08-18T00:07:11.427291,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,37,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T00:07:11.431294,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,37,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T00:07:11.436298,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,77,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T00:07:11.439297,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,87,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T00:07:11.445298,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,0,success,Fix table name: REF_Entities → REF_Entity,,
2025-08-18T00:07:11.449298,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,268,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-18T00:07:11.453349,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,269,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-18T00:07:11.459991,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: REF_Entities → REF_Entity,,
2025-08-18T00:07:11.462991,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: REF_Attributes → REF_Attribute,,
2025-08-18T00:07:11.464990,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: REF_EntityValues → REF_EntityValue,,
2025-08-18T00:07:11.468990,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue,,
2025-08-18T00:07:11.470991,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: XRF_Entity_AttributeValue → XRF_EntityAttributeValue,,
2025-08-18T00:07:11.474991,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: REF_Facts → REF_Fact,,
2025-08-18T00:07:11.476990,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: REF_Opinions → REF_Opinion,,
2025-08-18T00:07:11.479990,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: REF_Sources → REF_Source,,
2025-08-18T00:07:11.482993,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Fix table name: REF_Categories → REF_Category,,
2025-08-18T00:07:11.573806,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,208,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\'. (102) (SQLExecDirectW)"")",
2025-08-18T00:07:11.580192,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,209,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-18T00:07:11.584207,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,209,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-18T00:07:11.588096,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,210,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.606907,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,211,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.621799,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,212,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW)"")",
2025-08-18T00:07:11.658798,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,218,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-18T00:07:11.681780,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,228,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'FROM'. (102) (SQLExecDirectW)"")",
2025-08-18T00:07:11.687019,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,230,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-18T00:07:11.694223,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Fix table name: REF_Entities → REF_Entity,,
2025-08-18T00:07:11.698788,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Fix table name: REF_Facts → REF_Fact,,
2025-08-18T00:07:11.701788,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Fix table name: REF_Opinions → REF_Opinion,,
2025-08-18T00:07:11.704788,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Fix table name: REF_Sources → REF_Source,,
2025-08-18T00:07:11.707787,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Fix table name: REF_Categories → REF_Category,,
2025-08-18T00:07:11.712788,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,109,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.716838,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,120,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.721838,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,122,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.725838,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,133,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.729838,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,135,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.733881,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,142,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.737881,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,250,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T00:07:11.758921,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py,36,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T00:07:11.764254,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py,36,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T00:07:11.815140,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,111,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T00:07:11.827177,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,111,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T00:07:11.844213,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,131,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: COLUMN_NAME, TABLE_NAME, DATA_TYPE",
2025-08-18T00:07:11.864148,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,131,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: COLUMN_NAME, TABLE_NAME, DATA_TYPE",
2025-08-18T00:07:11.887871,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,12,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: name, create_date, modify_date",
2025-08-18T00:07:11.909592,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,12,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: name, create_date, modify_date",
2025-08-18T00:07:11.937395,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,83,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: name",
2025-08-18T00:07:11.960032,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,83,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: name",
2025-08-18T00:07:11.996507,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_db.py,11,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T00:07:12.047989,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py,257,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: count, COUNT",
2025-08-18T00:07:12.072030,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,0,success,Fix table name: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue,,
2025-08-18T00:07:12.086419,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,79,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T00:07:12.106369,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,79,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T00:07:12.141177,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,129,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name",
2025-08-18T00:07:12.177756,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,135,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T00:07:12.183911,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,135,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T00:07:12.198303,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,360,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: name, type",
2025-08-18T00:07:12.203102,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,360,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: name, type",
2025-08-18T00:07:12.212252,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,393,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T00:07:12.217287,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,393,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T00:07:12.224100,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_database.py,180,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)"")",
2025-08-18T00:07:12.230543,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,0,success,Fix table name: REF_EntityValues → REF_EntityValue,,
2025-08-18T00:07:12.258784,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,29,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: COLUMN_NAME, IS_NULLABLE, TABLE_NAME, DATA_TYPE",
2025-08-18T00:07:12.281916,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,29,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: COLUMN_NAME, IS_NULLABLE, TABLE_NAME, DATA_TYPE",
2025-08-18T00:07:12.310603,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,48,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: NULL, IS, WHEN, UnitsRows, TotalRows, NumericValue, END, CASE, ValueUnits, THEN, COUNT, NumericRows, NOT",
2025-08-18T00:07:12.325839,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,48,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: NULL, IS, WHEN, UnitsRows, TotalRows, NumericValue, END, CASE, ValueUnits, THEN, COUNT, NumericRows, NOT",
2025-08-18T00:07:12.380249,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,66,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue, Name, EntityValue",
2025-08-18T00:07:12.427290,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,66,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue, Name, EntityValue",
2025-08-18T00:07:12.432628,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,149,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:12.445848,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,149,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T00:07:12.471344,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py,56,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T00:07:12.507088,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py,56,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
