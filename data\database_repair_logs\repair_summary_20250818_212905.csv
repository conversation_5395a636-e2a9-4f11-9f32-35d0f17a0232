Timestamp,Operation,File,Line,Status,Rule Applied,SQL Validation,Error
2025-08-18T21:29:05.250202,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,109,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: e, f, FLOAT, OverallAverage, CreateDate, OVER, EvidenceCount, COUNT, ValidityRating, Name, ID, DataSource, CAST, AVG",
2025-08-18T21:29:05.251900,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,109,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: e, f, FLOAT, OverallAverage, CreateDate, OVER, EvidenceCount, COUNT, ValidityRating, Name, ID, DataSource, CAST, AVG",
2025-08-18T21:29:05.255194,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,194,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-18T21:29:05.257195,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,194,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-18T21:29:05.259196,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,206,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.261195,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,218,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.263199,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,226,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.265199,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,260,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.267198,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,260,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.269383,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,276,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.271383,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,276,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.273568,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,292,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.275851,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,292,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.278832,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,329,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: FLOAT, LIKE, CASE, THEN, Other, f, JSON, Category, ValidityRating, Configuration, AvgEffectiveness, ELSE, END, Name, AVG, Workflow, Database, CAST, WHEN, JSON_Parsing, Network",
2025-08-18T21:29:05.280832,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,329,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: FLOAT, LIKE, CASE, THEN, Other, f, JSON, Category, ValidityRating, Configuration, AvgEffectiveness, ELSE, END, Name, AVG, Workflow, Database, CAST, WHEN, JSON_Parsing, Network",
2025-08-18T21:29:05.285831,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,241,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-18T21:29:05.288533,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,241,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-18T21:29:05.290536,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,267,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-18T21:29:05.292636,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,267,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: ",
2025-08-18T21:29:05.295123,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,374,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.301127,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,374,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.303439,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,396,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.305443,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,410,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.308014,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,419,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.311014,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,419,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.313899,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,430,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.316902,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,430,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.318902,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,439,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.321095,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,439,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.323247,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,465,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.325635,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,465,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.329039,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,477,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.331044,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,477,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.333343,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,552,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.336964,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,559,success,SQL Validation Test,"SUCCESS - Tables: REF_Category, Columns: ",
2025-08-18T21:29:05.339337,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,566,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.340738,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,573,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:05.344714,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,580,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.346719,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,587,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ",
2025-08-18T21:29:05.349719,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,594,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.351718,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,602,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-18T21:29:05.353744,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,602,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-18T21:29:05.356640,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,610,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ",
2025-08-18T21:29:05.360705,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,620,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.366706,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,620,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.368706,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,630,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:05.372140,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,630,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:05.380144,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,855,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.383144,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,855,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.385696,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,872,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.387965,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,872,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.390807,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,914,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.393355,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,914,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.397359,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,926,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.400359,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,926,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.402358,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,991,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.405660,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,991,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.409662,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1009,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.413016,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1009,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.416020,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1021,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.419497,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1021,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.424500,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,34,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_EntityValue'. (207) (SQLExecDirectW)"")",
2025-08-18T21:29:05.427862,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,34,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_EntityValue'. (207) (SQLExecDirectW)"")",
2025-08-18T21:29:05.434122,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,66,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: create_date, modify_date, name",
2025-08-18T21:29:05.440122,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,66,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: create_date, modify_date, name",
2025-08-18T21:29:05.443123,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,93,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: name",
2025-08-18T21:29:05.445501,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,106,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T21:29:05.448561,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,106,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T21:29:05.452853,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,119,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.456852,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,9,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T21:29:05.459934,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,17,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:05.462683,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,26,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:05.466047,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,33,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:05.474386,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py,48,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: create_date, name",
2025-08-18T21:29:05.479894,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py,48,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: create_date, name",
2025-08-18T21:29:05.484980,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,113,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable ""@count"". (137) (SQLExecDirectW)')",
2025-08-18T21:29:05.493216,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Table name fix: REF_Facts → REF_Fact,,
2025-08-18T21:29:05.496219,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Table name fix: REF_Opinions → REF_Opinion,,
2025-08-18T21:29:05.499219,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Table name fix: REF_Sources → REF_Source,,
2025-08-18T21:29:05.501220,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Table name fix: REF_Categories → REF_Category,,
2025-08-18T21:29:05.505164,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,117,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '\\1'. (102) (SQLExecDirectW)"")",
2025-08-18T21:29:05.509269,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,37,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T21:29:05.512713,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,37,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T21:29:05.516275,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,77,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:05.519872,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,87,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:05.523941,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,0,success,Table name fix: REF_Entities → REF_Entity,,
2025-08-18T21:29:05.527023,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,268,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-18T21:29:05.531026,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,269,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-18T21:29:05.536464,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,31,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-18T21:29:05.539564,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,35,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-18T21:29:05.542564,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,35,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-18T21:29:05.546613,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,39,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:05.552394,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,53,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:05.555395,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,54,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.559808,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,55,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.565130,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Entities → REF_Entity,,
2025-08-18T21:29:05.567130,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Attributes → REF_Attribute,,
2025-08-18T21:29:05.570130,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_EntityValues → REF_EntityValue,,
2025-08-18T21:29:05.575180,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Facts → REF_Fact,,
2025-08-18T21:29:05.588325,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Opinions → REF_Opinion,,
2025-08-18T21:29:05.592972,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Sources → REF_Source,,
2025-08-18T21:29:05.595974,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Categories → REF_Category,,
2025-08-18T21:29:05.600973,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue,,
2025-08-18T21:29:05.605064,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: XRF_Entity_AttributeValue → XRF_EntityAttributeValue,,
2025-08-18T21:29:05.618111,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,208,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\'. (102) (SQLExecDirectW)"")",
2025-08-18T21:29:05.621611,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,209,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-18T21:29:05.625863,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,209,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-18T21:29:05.629862,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,210,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.633869,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,211,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.637870,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,212,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW)"")",
2025-08-18T21:29:05.642157,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,218,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:05.646392,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,228,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'FROM'. (102) (SQLExecDirectW)"")",
2025-08-18T21:29:05.650397,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,230,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:05.657092,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Table name fix: REF_Entities → REF_Entity,,
2025-08-18T21:29:05.661098,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Table name fix: REF_Facts → REF_Fact,,
2025-08-18T21:29:05.663947,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Table name fix: REF_Opinions → REF_Opinion,,
2025-08-18T21:29:05.666953,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Table name fix: REF_Sources → REF_Source,,
2025-08-18T21:29:05.670263,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Table name fix: REF_Categories → REF_Category,,
2025-08-18T21:29:05.675465,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,109,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.680706,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,120,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.685582,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,122,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.689777,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,133,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.694313,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,135,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.698313,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,142,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.705311,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,250,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.710918,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py,39,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-18T21:29:05.716662,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py,42,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:05.720661,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py,45,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-18T21:29:05.724661,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py,45,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-18T21:29:05.728740,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py,54,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.735739,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py,36,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T21:29:05.740741,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py,36,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T21:29:05.749827,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,111,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T21:29:05.755827,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,111,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T21:29:05.760828,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,131,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: DATA_TYPE, COLUMN_NAME, TABLE_NAME",
2025-08-18T21:29:05.765835,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,131,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: DATA_TYPE, COLUMN_NAME, TABLE_NAME",
2025-08-18T21:29:05.776834,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,12,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: create_date, modify_date, name",
2025-08-18T21:29:05.785905,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,12,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: create_date, modify_date, name",
2025-08-18T21:29:05.794407,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,83,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: name",
2025-08-18T21:29:05.803473,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,83,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: name",
2025-08-18T21:29:05.825706,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,45,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:05.829884,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,66,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:05.833884,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,67,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-18T21:29:05.837932,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,68,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: COUNT",
2025-08-18T21:29:05.842933,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,71,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:05.846597,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,72,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-18T21:29:05.851602,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,75,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.856648,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,76,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-18T21:29:05.860648,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,79,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.865582,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,80,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207) (SQLExecDirectW)"")",
2025-08-18T21:29:05.872879,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,90,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)"")",
2025-08-18T21:29:05.883469,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,90,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)"")",
2025-08-18T21:29:05.890626,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,99,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:05.896626,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,99,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:05.904133,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,108,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)"")",
2025-08-18T21:29:05.910641,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,108,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)"")",
2025-08-18T21:29:05.917414,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,117,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)"")",
2025-08-18T21:29:05.922199,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,117,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)"")",
2025-08-18T21:29:05.928298,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,135,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207)"")",
2025-08-18T21:29:05.933297,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,135,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207)"")",
2025-08-18T21:29:05.938599,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,158,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:05.943661,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,158,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:05.948661,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,194,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: keyword, SELECT, contains, This",
2025-08-18T21:29:05.952632,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,195,success,SQL Validation Test,"SUCCESS - Tables: this, REF_Entity, Columns: ",
2025-08-18T21:29:05.957631,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,198,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-18T21:29:05.962681,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,198,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-18T21:29:05.967796,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,205,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-18T21:29:05.972874,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,208,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 's'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Unclosed quotation mark after the character string ''. (105)"")",
2025-08-18T21:29:05.978436,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,211,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'LIMIT'. (102) (SQLExecDirectW)"")",
2025-08-18T21:29:05.983440,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,211,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'LIMIT'. (102) (SQLExecDirectW)"")",
2025-08-18T21:29:05.988644,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,236,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:05.994299,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,237,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:05.999298,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,238,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.004298,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,239,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.010368,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,243,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]'DATE' is not a recognized built-in function name. (195) (SQLExecDirectW)"")",
2025-08-18T21:29:06.015401,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,244,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]'CURDATE' is not a recognized built-in function name. (195) (SQLExecDirectW)"")",
2025-08-18T21:29:06.019400,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,269,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:06.025098,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,285,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'FROM'. (102) (SQLExecDirectW)"")",
2025-08-18T21:29:06.029257,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,286,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:06.034118,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,287,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:06.039386,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,291,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'WHERE'. (156) (SQLExecDirectW)"")",
2025-08-18T21:29:06.045390,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,305,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)"")",
2025-08-18T21:29:06.050435,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,309,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: COUNT, Test, message, completed, successfully, total_entities",
2025-08-18T21:29:06.055647,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,309,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: COUNT, Test, message, completed, successfully, total_entities",
2025-08-18T21:29:06.061812,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py,21,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:06.067334,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py,22,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:06.073351,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py,23,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.078352,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py,32,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)"")",
2025-08-18T21:29:06.083802,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py,32,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)"")",
2025-08-18T21:29:06.089783,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,27,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:06.094004,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,28,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-18T21:29:06.099061,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,29,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: COUNT",
2025-08-18T21:29:06.105061,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,30,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:06.110085,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,31,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-18T21:29:06.115864,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,32,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.120867,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,33,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-18T21:29:06.125938,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,34,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.131939,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,35,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207) (SQLExecDirectW)"")",
2025-08-18T21:29:06.137464,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,40,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: keyword, SELECT, contains, This",
2025-08-18T21:29:06.142467,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,41,success,SQL Validation Test,"SUCCESS - Tables: this, REF_Entity, Columns: ",
2025-08-18T21:29:06.148686,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,42,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\'. (102) (SQLExecDirectW)"")",
2025-08-18T21:29:06.153686,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,43,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-18T21:29:06.159076,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,44,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 's'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Unclosed quotation mark after the character string ''. (105)"")",
2025-08-18T21:29:06.165078,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,47,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.171666,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,48,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:06.177067,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,49,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.182991,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,50,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.189028,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,51,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]'DATE' is not a recognized built-in function name. (195) (SQLExecDirectW)"")",
2025-08-18T21:29:06.196032,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,52,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]'CURDATE' is not a recognized built-in function name. (195) (SQLExecDirectW)"")",
2025-08-18T21:29:06.201501,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,67,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:06.207833,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,72,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'FROM'. (102) (SQLExecDirectW)"")",
2025-08-18T21:29:06.212749,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,73,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:06.218026,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,74,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:06.226228,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,194,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)"")",
2025-08-18T21:29:06.233329,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,194,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)"")",
2025-08-18T21:29:06.239786,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,210,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:06.245543,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,218,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:06.251542,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,219,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.260408,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_db.py,11,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:06.269671,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py,257,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: COUNT, count",
2025-08-18T21:29:06.277114,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,0,success,Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue,,
2025-08-18T21:29:06.282839,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,79,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.288302,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,79,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.294603,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,129,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name, ID",
2025-08-18T21:29:06.299858,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,135,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:06.305975,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,135,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-18T21:29:06.310986,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,360,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: type, name",
2025-08-18T21:29:06.318131,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,360,success,SQL Validation Test,"SUCCESS - Tables: sys, Columns: type, name",
2025-08-18T21:29:06.322819,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,393,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T21:29:06.329310,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,393,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME",
2025-08-18T21:29:06.362903,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_database.py,180,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:06.369138,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,0,success,Table name fix: REF_EntityValues → REF_EntityValue,,
2025-08-18T21:29:06.374141,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,29,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: DATA_TYPE, COLUMN_NAME, IS_NULLABLE, TABLE_NAME",
2025-08-18T21:29:06.382131,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,29,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: DATA_TYPE, COLUMN_NAME, IS_NULLABLE, TABLE_NAME",
2025-08-18T21:29:06.387251,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,48,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: NumericValue, NOT, NULL, ValueUnits, COUNT, CASE, UnitsRows, IS, THEN, WHEN, NumericRows, END, TotalRows",
2025-08-18T21:29:06.394104,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,48,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: NumericValue, NOT, NULL, ValueUnits, COUNT, CASE, UnitsRows, IS, THEN, WHEN, NumericRows, END, TotalRows",
2025-08-18T21:29:06.400147,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,66,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: NumericValue, ValueUnits, EntityValue, Name",
2025-08-18T21:29:06.405891,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,66,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: NumericValue, ValueUnits, EntityValue, Name",
2025-08-18T21:29:06.411888,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,149,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.418234,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,149,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.430234,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py,56,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.438813,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py,56,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.451587,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_server.py,276,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.458719,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_server.py,276,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.467303,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,139,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME, TABLE_SCHEMA",
2025-08-18T21:29:06.474835,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,139,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME, TABLE_SCHEMA",
2025-08-18T21:29:06.481406,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,161,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_SCHEMA",
2025-08-18T21:29:06.488965,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,161,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_SCHEMA",
2025-08-18T21:29:06.497966,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,181,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: ROUTINE_TYPE, ROUTINE_SCHEMA, ROUTINE_NAME",
2025-08-18T21:29:06.507281,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,181,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: ROUTINE_TYPE, ROUTINE_SCHEMA, ROUTINE_NAME",
2025-08-18T21:29:06.514787,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,203,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: ROUTINE_TYPE, ROUTINE_SCHEMA, ROUTINE_NAME",
2025-08-18T21:29:06.524332,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,203,success,SQL Validation Test,"SUCCESS - Tables: INFORMATION_SCHEMA, Columns: ROUTINE_TYPE, ROUTINE_SCHEMA, ROUTINE_NAME",
2025-08-18T21:29:06.530332,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,255,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.536775,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,255,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.542754,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,287,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.549057,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,287,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.555058,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,318,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.562123,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,318,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-18T21:29:06.568401,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,348,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:06.575711,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,439,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:06.605798,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\distlib\manifest.py,258,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'self.files'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)"")",
2025-08-18T21:29:06.613909,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\distlib\manifest.py,258,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'self.files'. (102) (SQLExecDirectW)"")",
2025-08-18T21:29:06.636317,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,208,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:06.643318,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,208,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:06.651317,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,244,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:06.657130,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,244,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:06.670572,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,1789,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:06.677708,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,1789,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:06.684866,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,1819,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:06.690865,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,1819,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:06.728070,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\progress.py,1413,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:06.738083,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\progress.py,1413,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)"")",
2025-08-18T21:29:06.832794,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_distutils\filelist.py,203,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'self.files'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)"")",
2025-08-18T21:29:06.841178,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_distutils\filelist.py,203,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'self.files'. (102) (SQLExecDirectW)"")",
2025-08-18T21:29:06.851730,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_vendor\importlib_metadata\__init__.py,349,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'match'. (102) (SQLExecDirectW)"")",
2025-08-18T21:29:06.859465,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_vendor\importlib_metadata\__init__.py,349,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'match'. (102) (SQLExecDirectW)"")",
