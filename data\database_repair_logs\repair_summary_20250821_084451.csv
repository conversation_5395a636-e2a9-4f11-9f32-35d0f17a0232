Timestamp,Operation,File,Line,Status,Rule Applied,SQL Validation,Error
2025-08-21T08:44:52.167135,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,109,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Column 'REF_Fact.ID' is invalid in the select list because it is not contained in either an aggregate function or the GROUP BY clause. (8120) (SQLExecDirectW)"")",
2025-08-21T08:44:52.182537,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,109,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Column 'REF_Fact.ID' is invalid in the select list because it is not contained in either an aggregate function or the GROUP BY clause. (8120) (SQLExecDirectW)"")",
2025-08-21T08:44:52.186123,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,194,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: FactID, DataSource, Evidence, Name",
2025-08-21T08:44:52.188094,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,194,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: FactID, DataSource, Evidence, Name",
2025-08-21T08:44:52.193099,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,206,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ID, ValidityRating",
2025-08-21T08:44:52.195103,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,218,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: FactID",
2025-08-21T08:44:52.200766,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,226,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ID, ValidityRating",
2025-08-21T08:44:52.201767,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,260,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: fact, CreateDate, ValidityRating, ID, DataSource, Name",
2025-08-21T08:44:52.201767,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,260,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: fact, CreateDate, ValidityRating, ID, DataSource, Name",
2025-08-21T08:44:52.201767,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,276,success,SQL Validation Test,"SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ValidityRating, opinion, ID, DataSource, Opinion, Name",
2025-08-21T08:44:52.213888,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,276,success,SQL Validation Test,"SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ValidityRating, opinion, ID, DataSource, Opinion, Name",
2025-08-21T08:44:52.217936,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,292,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ValidityRating, FactID, ID, DataSource, Evidence, evidence, Name",
2025-08-21T08:44:52.219021,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,292,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ValidityRating, FactID, ID, DataSource, Evidence, evidence, Name",
2025-08-21T08:44:52.219021,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,329,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource",
2025-08-21T08:44:52.219021,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,329,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource",
2025-08-21T08:44:52.219021,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,241,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource, Name",
2025-08-21T08:44:52.235235,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,241,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource, Name",
2025-08-21T08:44:52.238275,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,267,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: FactID, DataSource, Evidence, Name",
2025-08-21T08:44:52.240356,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,267,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: FactID, DataSource, Evidence, Name",
2025-08-21T08:44:52.243503,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,374,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: FactID, ID, ValidityRating, Name",
2025-08-21T08:44:52.245640,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,374,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: FactID, ID, ValidityRating, Name",
2025-08-21T08:44:52.245640,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,396,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ID, ValidityRating",
2025-08-21T08:44:52.249177,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,410,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name",
2025-08-21T08:44:52.249177,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,419,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: CreateDate, ValidityRating, ID, DataSource, Name",
2025-08-21T08:44:52.249177,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,419,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: CreateDate, ValidityRating, ID, DataSource, Name",
2025-08-21T08:44:52.249177,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,430,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: CreateDate, FactID, ID, DataSource, Evidence, Name",
2025-08-21T08:44:52.261693,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,430,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: CreateDate, FactID, ID, DataSource, Evidence, Name",
2025-08-21T08:44:52.264733,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,439,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.266802,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,439,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.271760,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,465,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ValidityRating, FactID, ID, DataSource, Name",
2025-08-21T08:44:52.273837,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,465,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ValidityRating, FactID, ID, DataSource, Name",
2025-08-21T08:44:52.278526,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,477,success,SQL Validation Test,"SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ValidityRating, ID, DataSource, Opinion, Name",
2025-08-21T08:44:52.281656,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,477,success,SQL Validation Test,"SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ValidityRating, ID, DataSource, Opinion, Name",
2025-08-21T08:44:52.283662,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,552,success,SQL Validation Test,"SUCCESS - Tables: REF_Category, Columns: ID, Name",
2025-08-21T08:44:52.283662,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,559,success,SQL Validation Test,"SUCCESS - Tables: REF_Category, Columns: Name",
2025-08-21T08:44:52.283662,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,566,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name",
2025-08-21T08:44:52.283662,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,573,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-21T08:44:52.283662,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,580,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ID, Name",
2025-08-21T08:44:52.283662,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,587,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: Name",
2025-08-21T08:44:52.299313,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,594,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ID, EntityValue, Name",
2025-08-21T08:44:52.299313,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,602,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue, EntityValue, Name",
2025-08-21T08:44:52.299313,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,602,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue, EntityValue, Name",
2025-08-21T08:44:52.308003,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,610,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, Name",
2025-08-21T08:44:52.308348,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,620,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.308348,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,620,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.308348,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,630,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.319282,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,630,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.327986,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,855,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, CreateDate, Name",
2025-08-21T08:44:52.327986,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,855,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, CreateDate, Name",
2025-08-21T08:44:52.327986,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,872,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.336894,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,872,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.341582,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,914,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource, CreateDate, Name",
2025-08-21T08:44:52.345581,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,914,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource, CreateDate, Name",
2025-08-21T08:44:52.345581,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,926,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: CreateDate, DataSource, Evidence, Name",
2025-08-21T08:44:52.345581,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,926,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: CreateDate, DataSource, Evidence, Name",
2025-08-21T08:44:52.345581,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,991,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource, CreateDate, Name",
2025-08-21T08:44:52.345581,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,991,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource, CreateDate, Name",
2025-08-21T08:44:52.361262,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1009,success,SQL Validation Test,"SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ValidityRating, DataSource, Opinion, Name",
2025-08-21T08:44:52.362118,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1009,success,SQL Validation Test,"SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ValidityRating, DataSource, Opinion, Name",
2025-08-21T08:44:52.362118,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1021,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.371137,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1021,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.374459,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,34,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.378490,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,34,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.381620,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,66,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.384011,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,66,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.388462,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,93,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.391463,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,106,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.393062,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,106,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.393062,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,119,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-21T08:44:52.401629,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.403705,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,17,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-21T08:44:52.403705,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,26,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-21T08:44:52.403705,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,33,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.403705,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py,48,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.420089,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py,48,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.424124,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,113,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable ""@count"". (137) (SQLExecDirectW)')",
2025-08-21T08:44:52.436673,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Table name fix: REF_Facts → REF_Fact,,
2025-08-21T08:44:52.438755,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Table name fix: REF_Opinions → REF_Opinion,,
2025-08-21T08:44:52.442144,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Table name fix: REF_Sources → REF_Source,,
2025-08-21T08:44:52.444133,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Table name fix: REF_Categories → REF_Category,,
2025-08-21T08:44:52.448705,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,117,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '\\1'. (102) (SQLExecDirectW)"")",
2025-08-21T08:44:52.453704,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,37,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.457190,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,37,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.460190,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,77,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-21T08:44:52.465135,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,87,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-21T08:44:52.470135,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,0,success,Table name fix: REF_Entities → REF_Entity,,
2025-08-21T08:44:52.475977,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,268,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-21T08:44:52.480685,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,269,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155) (SQLExecDirectW)"")",
2025-08-21T08:44:52.481705,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,31,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-21T08:44:52.481705,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,35,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: Value, EntityID, Name",
2025-08-21T08:44:52.493413,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,35,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: Value, EntityID, Name",
2025-08-21T08:44:52.498814,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,39,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.503733,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,53,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.507742,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,54,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.508742,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,55,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'logs'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.517660,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Entities → REF_Entity,,
2025-08-21T08:44:52.518660,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Attributes → REF_Attribute,,
2025-08-21T08:44:52.523195,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_EntityValues → REF_EntityValue,,
2025-08-21T08:44:52.525705,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Facts → REF_Fact,,
2025-08-21T08:44:52.527103,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Opinions → REF_Opinion,,
2025-08-21T08:44:52.532045,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Sources → REF_Source,,
2025-08-21T08:44:52.535045,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Categories → REF_Category,,
2025-08-21T08:44:52.539845,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue,,
2025-08-21T08:44:52.544179,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: XRF_Entity_AttributeValue → XRF_EntityAttributeValue,,
2025-08-21T08:44:52.558593,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,208,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\'. (102) (SQLExecDirectW)"")",
2025-08-21T08:44:52.562630,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,209,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: Value, Name",
2025-08-21T08:44:52.563641,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,209,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: Value, Name",
2025-08-21T08:44:52.563641,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,210,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name",
2025-08-21T08:44:52.563641,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,211,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID",
2025-08-21T08:44:52.583847,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,212,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155)"")",
2025-08-21T08:44:52.588079,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,218,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.592798,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,228,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ')'. (102) (SQLExecDirectW)"")",
2025-08-21T08:44:52.597231,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,230,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.604756,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Table name fix: REF_Entities → REF_Entity,,
2025-08-21T08:44:52.606758,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Table name fix: REF_Facts → REF_Fact,,
2025-08-21T08:44:52.606758,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Table name fix: REF_Opinions → REF_Opinion,,
2025-08-21T08:44:52.615059,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Table name fix: REF_Sources → REF_Source,,
2025-08-21T08:44:52.617059,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Table name fix: REF_Categories → REF_Category,,
2025-08-21T08:44:52.623094,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,122,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-21T08:44:52.629064,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,145,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'table'. (156) (SQLExecDirectW)"")",
2025-08-21T08:44:52.633954,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,158,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.637419,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,570,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-21T08:44:52.642888,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,584,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-21T08:44:52.645896,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,591,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-21T08:44:52.652360,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,598,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-21T08:44:52.657237,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,605,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-21T08:44:52.661405,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,612,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-21T08:44:52.663686,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,720,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-21T08:44:52.673140,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py,39,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-21T08:44:52.675132,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py,42,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.683450,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py,45,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: Value, EntityID, Name",
2025-08-21T08:44:52.683450,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py,45,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: Value, EntityID, Name",
2025-08-21T08:44:52.683450,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py,54,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name",
2025-08-21T08:44:52.698497,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py,36,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.716513,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py,36,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.725830,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,111,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.726969,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,111,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.738049,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,131,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.742805,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,131,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.750676,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.756062,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.761160,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,83,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.766735,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,83,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.797392,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,45,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.803398,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,66,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *",
2025-08-21T08:44:52.808605,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,67,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-21T08:44:52.814088,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,68,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155) (SQLExecDirectW)"")",
2025-08-21T08:44:52.814088,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,71,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Value, Name",
2025-08-21T08:44:52.821131,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,72,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: Value, EntityID, Name",
2025-08-21T08:44:52.821131,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,75,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name",
2025-08-21T08:44:52.832700,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,76,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name, Status",
2025-08-21T08:44:52.837359,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,79,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID",
2025-08-21T08:44:52.843425,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,80,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: CreatedDate",
2025-08-21T08:44:52.845424,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,90,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207)"")",
2025-08-21T08:44:52.845424,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,90,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207)"")",
2025-08-21T08:44:52.858624,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,99,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Description, CreatedDate, Status, Name",
2025-08-21T08:44:52.859624,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,99,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Description, CreatedDate, Status, Name",
2025-08-21T08:44:52.868160,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,108,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ModifiedDate, CreatedDate, Status",
2025-08-21T08:44:52.873710,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,108,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ModifiedDate, CreatedDate, Status",
2025-08-21T08:44:52.876709,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,117,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: EntityID, Status",
2025-08-21T08:44:52.882239,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,117,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: EntityID, Status",
2025-08-21T08:44:52.882239,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,135,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Description'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207)"")",
2025-08-21T08:44:52.893355,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,135,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Description'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207)"")",
2025-08-21T08:44:52.897863,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,158,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Description, CreatedDate, Status, Name",
2025-08-21T08:44:52.902620,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,158,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Description, CreatedDate, Status, Name",
2025-08-21T08:44:52.907626,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,194,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155) (SQLExecDirectW)"")",
2025-08-21T08:44:52.912726,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,195,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-21T08:44:52.913726,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,198,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, with, Name",
2025-08-21T08:44:52.913726,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,198,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, with, Name",
2025-08-21T08:44:52.913726,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,205,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-21T08:44:52.934472,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,208,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 's'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Unclosed quotation mark after the character string ') AS test_query'. (105)"")",
2025-08-21T08:44:52.939478,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,211,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'ParentID'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Type'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207)"")",
2025-08-21T08:44:52.939478,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,211,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'ParentID'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Type'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207)"")",
2025-08-21T08:44:52.952690,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,236,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.966396,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,237,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.972554,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,238,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.977951,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,239,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.984257,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,243,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.989850,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,244,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:52.996076,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,269,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *",
2025-08-21T08:44:53.002417,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,285,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ')'. (102) (SQLExecDirectW)"")",
2025-08-21T08:44:53.006796,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,286,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-21T08:44:53.007795,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,287,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-21T08:44:53.007795,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,291,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'INSERT'. (156) (SQLExecDirectW)"")",
2025-08-21T08:44:53.007795,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,305,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155)"")",
2025-08-21T08:44:53.028360,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,309,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: successfully, Test, completed",
2025-08-21T08:44:53.028360,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,309,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: successfully, Test, completed",
2025-08-21T08:44:53.040547,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py,21,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *",
2025-08-21T08:44:53.044770,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py,22,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-21T08:44:53.044770,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py,23,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name",
2025-08-21T08:44:53.058999,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py,32,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)"")",
2025-08-21T08:44:53.065722,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py,32,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)"")",
2025-08-21T08:44:53.070741,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,62,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.070741,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,71,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.085538,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,80,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.093125,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,89,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'P'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.098844,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,98,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'P'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.103854,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,107,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'P'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.109297,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,116,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.116669,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,125,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.122156,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,135,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.122156,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,149,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.136033,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,158,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.142255,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,168,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.147265,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,177,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.152241,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,187,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.160519,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,197,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.166132,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,207,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.172201,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,217,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.179289,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,227,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.185037,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,236,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Tabl'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.187063,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,250,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.197199,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,259,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.205144,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,269,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.211360,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,279,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.218526,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,288,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.232676,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,298,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.239429,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,308,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'D'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.252313,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,318,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: ValidityRating, FactID, ID, DataSource, Name",
2025-08-21T08:44:53.260782,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,328,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.268053,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,342,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.275338,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,351,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.281735,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,361,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'ActiveUsers'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.288895,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,371,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.288895,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,385,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.302466,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,394,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.309251,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,404,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.315863,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,414,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.322479,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,424,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.328158,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,438,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.334110,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,447,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.340770,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,457,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.348062,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,467,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.354703,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,481,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.360714,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,491,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.368024,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,501,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_Fact_Evidence'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.375691,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,511,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Tabl'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.381713,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,521,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.389697,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,27,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *",
2025-08-21T08:44:53.392697,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,28,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-21T08:44:53.402911,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,29,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155) (SQLExecDirectW)"")",
2025-08-21T08:44:53.407916,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,30,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Value, Name",
2025-08-21T08:44:53.407916,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,31,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: Value, EntityID, Name",
2025-08-21T08:44:53.407916,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,32,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name",
2025-08-21T08:44:53.428109,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,33,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name, Status",
2025-08-21T08:44:53.433135,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,34,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID",
2025-08-21T08:44:53.433135,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,35,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: CreatedDate",
2025-08-21T08:44:53.433135,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,40,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155) (SQLExecDirectW)"")",
2025-08-21T08:44:53.454079,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,41,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-21T08:44:53.456783,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,42,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\'. (102) (SQLExecDirectW)"")",
2025-08-21T08:44:53.467273,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,43,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-21T08:44:53.476034,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,44,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 's'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Unclosed quotation mark after the character string ') AS test_query'. (105)"")",
2025-08-21T08:44:53.481653,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,47,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.490918,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,48,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.499338,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,49,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.506575,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,50,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.511589,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,51,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.520656,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,52,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.528754,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,67,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.535959,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,72,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ')'. (102) (SQLExecDirectW)"")",
2025-08-21T08:44:53.540965,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,73,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-21T08:44:53.540965,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,74,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-21T08:44:53.556232,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,194,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)"")",
2025-08-21T08:44:53.559152,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,194,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)"")",
2025-08-21T08:44:53.569546,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,210,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'us'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.569546,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,218,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *",
2025-08-21T08:44:53.588370,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,219,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, ID",
2025-08-21T08:44:53.596146,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_db.py,11,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-21T08:44:53.613301,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py,257,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-21T08:44:53.622114,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,0,success,Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue,,
2025-08-21T08:44:53.630424,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,79,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.637326,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,79,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.643456,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,129,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name",
2025-08-21T08:44:53.649615,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,135,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Description, ModifyDate, CreateDate, Name",
2025-08-21T08:44:53.657031,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,135,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Description, ModifyDate, CreateDate, Name",
2025-08-21T08:44:53.658031,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,360,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.673024,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,360,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.681042,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,393,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.690004,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,393,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.698432,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_database.py,180,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.706768,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,0,success,Table name fix: REF_EntityValues → REF_EntityValue,,
2025-08-21T08:44:53.714678,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,29,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.714678,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,29,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.727352,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,48,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue",
2025-08-21T08:44:53.727352,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,48,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue",
2025-08-21T08:44:53.746439,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,66,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue, EntityValue, Name",
2025-08-21T08:44:53.746439,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,66,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue, EntityValue, Name",
2025-08-21T08:44:53.746439,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,149,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, ID, NumericValue, EntityValue",
2025-08-21T08:44:53.767649,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,149,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, ID, NumericValue, EntityValue",
2025-08-21T08:44:53.781637,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py,56,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.788384,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py,56,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.805958,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_server.py,276,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'FactText'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'FactID'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'FactType'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'FactText'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'LastUpdated'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Source'. (207)"")",
2025-08-21T08:44:53.808552,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_server.py,276,failed,SQL Validation Test,"FAILED - ('42S22', ""[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'FactText'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'FactID'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'FactType'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'FactText'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'LastUpdated'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Source'. (207)"")",
2025-08-21T08:44:53.823389,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,139,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.831678,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,139,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.838503,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,161,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.847403,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,161,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.855048,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,181,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.864187,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,181,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.872039,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,203,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.876038,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,203,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.885256,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,255,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.895728,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,255,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.903646,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,287,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.907624,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,287,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.918074,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,318,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.918074,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,318,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.936769,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,348,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:53.938759,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,439,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-21T08:44:53.982411,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\distlib\manifest.py,258,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)"")",
2025-08-21T08:44:53.988447,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\distlib\manifest.py,258,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW)"")",
2025-08-21T08:44:54.013113,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,208,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:54.027043,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,208,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:54.035309,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,244,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:54.040888,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,244,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:54.052186,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,1789,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:54.059625,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,1789,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:54.068107,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,1819,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:54.075706,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,1819,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:54.093943,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\progress.py,1413,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:54.098955,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\progress.py,1413,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:54.195493,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_distutils\filelist.py,203,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)"")",
2025-08-21T08:44:54.204156,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_distutils\filelist.py,203,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW)"")",
2025-08-21T08:44:54.217012,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_vendor\importlib_metadata\__init__.py,349,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 's'. (208) (SQLExecDirectW)"")",
2025-08-21T08:44:54.222424,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_vendor\importlib_metadata\__init__.py,349,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 's'. (208) (SQLExecDirectW)"")",
