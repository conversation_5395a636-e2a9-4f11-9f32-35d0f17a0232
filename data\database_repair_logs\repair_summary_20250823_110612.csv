Timestamp,Operation,File,Line,Status,Rule Applied,SQL Validation,Error
2025-08-23T11:06:13.128023,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,109,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ID, FactID, ValidityRating, Name, DataSource",
2025-08-23T11:06:13.130775,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,109,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ID, FactID, ValidityRating, Name, DataSource",
2025-08-23T11:06:13.132774,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,194,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: FactID, DataSource, Name, Evidence",
2025-08-23T11:06:13.134773,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,194,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: FactID, DataSource, Name, Evidence",
2025-08-23T11:06:13.137298,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,206,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ID, ValidityRating",
2025-08-23T11:06:13.139303,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,218,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: FactID",
2025-08-23T11:06:13.141303,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,226,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ID, ValidityRating",
2025-08-23T11:06:13.143302,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,260,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: CreateDate, ID, ValidityRating, fact, Name, DataSource",
2025-08-23T11:06:13.144302,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,260,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: CreateDate, ID, ValidityRating, fact, Name, DataSource",
2025-08-23T11:06:13.147363,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,276,success,SQL Validation Test,"SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ID, opinion, ValidityRating, Name, Opinion, DataSource",
2025-08-23T11:06:13.149275,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,276,success,SQL Validation Test,"SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ID, opinion, ValidityRating, Name, Opinion, DataSource",
2025-08-23T11:06:13.151274,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,292,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ID, FactID, Evidence, ValidityRating, evidence, Name, DataSource",
2025-08-23T11:06:13.153778,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,292,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ID, FactID, Evidence, ValidityRating, evidence, Name, DataSource",
2025-08-23T11:06:13.154781,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,329,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: DataSource, ValidityRating",
2025-08-23T11:06:13.154781,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py,329,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: DataSource, ValidityRating",
2025-08-23T11:06:13.154781,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,241,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: DataSource, Name, ValidityRating",
2025-08-23T11:06:13.154781,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,241,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: DataSource, Name, ValidityRating",
2025-08-23T11:06:13.154781,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,267,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: FactID, DataSource, Name, Evidence",
2025-08-23T11:06:13.172218,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,267,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: FactID, DataSource, Name, Evidence",
2025-08-23T11:06:13.173945,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,374,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: ID, FactID, Name, ValidityRating",
2025-08-23T11:06:13.175945,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,374,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: ID, FactID, Name, ValidityRating",
2025-08-23T11:06:13.176946,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,396,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ID, ValidityRating",
2025-08-23T11:06:13.179673,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,410,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name",
2025-08-23T11:06:13.182551,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,419,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: CreateDate, ID, ValidityRating, Name, DataSource",
2025-08-23T11:06:13.186157,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,419,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: CreateDate, ID, ValidityRating, Name, DataSource",
2025-08-23T11:06:13.188906,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,430,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: CreateDate, ID, FactID, Evidence, Name, DataSource",
2025-08-23T11:06:13.190840,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,430,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: CreateDate, ID, FactID, Evidence, Name, DataSource",
2025-08-23T11:06:13.194810,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,439,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.197501,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,439,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.200138,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,465,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ID, FactID, ValidityRating, Name, DataSource",
2025-08-23T11:06:13.203340,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,465,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ID, FactID, ValidityRating, Name, DataSource",
2025-08-23T11:06:13.205892,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,477,success,SQL Validation Test,"SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ID, ValidityRating, Name, Opinion, DataSource",
2025-08-23T11:06:13.207422,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,477,success,SQL Validation Test,"SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ID, ValidityRating, Name, Opinion, DataSource",
2025-08-23T11:06:13.208463,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,552,success,SQL Validation Test,"SUCCESS - Tables: REF_Category, Columns: ID, Name",
2025-08-23T11:06:13.208463,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,559,success,SQL Validation Test,"SUCCESS - Tables: REF_Category, Columns: Name",
2025-08-23T11:06:13.215869,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,566,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name",
2025-08-23T11:06:13.218962,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,573,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-23T11:06:13.222404,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,580,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: ID, Name",
2025-08-23T11:06:13.224404,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,587,success,SQL Validation Test,"SUCCESS - Tables: REF_Attribute, Columns: Name",
2025-08-23T11:06:13.224404,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,594,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: ID, Name, EntityValue",
2025-08-23T11:06:13.224404,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,602,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, NumericValue, Name, ValueUnits",
2025-08-23T11:06:13.224404,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,602,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, NumericValue, Name, ValueUnits",
2025-08-23T11:06:13.224404,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,610,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, Name",
2025-08-23T11:06:13.224404,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,620,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.250916,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,620,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.254933,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,630,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.258133,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py,630,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.264985,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,855,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, CreateDate, Name",
2025-08-23T11:06:13.266989,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,855,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, CreateDate, Name",
2025-08-23T11:06:13.270011,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,872,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.273369,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,872,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.276370,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,914,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: CreateDate, DataSource, Name, ValidityRating",
2025-08-23T11:06:13.279610,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,914,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: CreateDate, DataSource, Name, ValidityRating",
2025-08-23T11:06:13.284892,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,926,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: CreateDate, DataSource, Name, Evidence",
2025-08-23T11:06:13.286892,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,926,success,SQL Validation Test,"SUCCESS - Tables: REF_Evidence, Columns: CreateDate, DataSource, Name, Evidence",
2025-08-23T11:06:13.286892,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,991,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: CreateDate, DataSource, Name, ValidityRating",
2025-08-23T11:06:13.286892,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,991,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: CreateDate, DataSource, Name, ValidityRating",
2025-08-23T11:06:13.304254,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1009,success,SQL Validation Test,"SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ValidityRating, Name, Opinion, DataSource",
2025-08-23T11:06:13.307258,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1009,success,SQL Validation Test,"SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ValidityRating, Name, Opinion, DataSource",
2025-08-23T11:06:13.310005,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1021,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: EntityID, CreateDate, EntityValue, ID, EntityValueID, Name, AttributeID",
2025-08-23T11:06:13.312884,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py,1021,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: EntityID, CreateDate, EntityValue, ID, EntityValueID, Name, AttributeID",
2025-08-23T11:06:13.316547,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,34,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.320455,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,34,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.324606,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,66,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.327605,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,66,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.332967,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,93,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.335344,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,106,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.338344,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,106,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.342384,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py,119,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-23T11:06:13.347387,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,9,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.350980,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,17,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-23T11:06:13.353141,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,26,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-23T11:06:13.357147,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py,33,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.361896,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py,48,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.366246,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py,48,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.370353,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql,113,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable ""@count"". (137) (SQLExecDirectW)')",
2025-08-23T11:06:13.395953,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Table name fix: REF_Facts → REF_Fact,,
2025-08-23T11:06:13.398526,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Table name fix: REF_Opinions → REF_Opinion,,
2025-08-23T11:06:13.400579,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Table name fix: REF_Sources → REF_Source,,
2025-08-23T11:06:13.403145,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,0,success,Table name fix: REF_Categories → REF_Category,,
2025-08-23T11:06:13.407412,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py,117,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '\\1'. (102) (SQLExecDirectW)"")",
2025-08-23T11:06:13.413666,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,37,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.418294,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,37,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.421784,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,77,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-23T11:06:13.424870,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py,87,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-23T11:06:13.427065,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,0,success,Table name fix: REF_Entities → REF_Entity,,
2025-08-23T11:06:13.433670,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,268,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-23T11:06:13.437418,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py,269,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-23T11:06:13.444297,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,31,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-23T11:06:13.447283,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,35,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: EntityID, Name, Value",
2025-08-23T11:06:13.450573,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,35,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: EntityID, Name, Value",
2025-08-23T11:06:13.454077,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,39,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.457860,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,53,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.459861,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,54,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.464908,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py,55,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'logs'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.470414,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Entities → REF_Entity,,
2025-08-23T11:06:13.473413,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Attributes → REF_Attribute,,
2025-08-23T11:06:13.475286,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_EntityValues → REF_EntityValue,,
2025-08-23T11:06:13.478683,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Facts → REF_Fact,,
2025-08-23T11:06:13.480508,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Opinions → REF_Opinion,,
2025-08-23T11:06:13.481507,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Sources → REF_Source,,
2025-08-23T11:06:13.486708,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: REF_Categories → REF_Category,,
2025-08-23T11:06:13.490708,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue,,
2025-08-23T11:06:13.493291,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py,0,success,Table name fix: XRF_Entity_AttributeValue → XRF_EntityAttributeValue,,
2025-08-23T11:06:13.504592,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,208,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-23T11:06:13.505592,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,209,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: Value, Name",
2025-08-23T11:06:13.505592,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,209,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: Value, Name",
2025-08-23T11:06:13.505592,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,210,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name",
2025-08-23T11:06:13.505592,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,211,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID",
2025-08-23T11:06:13.524748,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,212,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Entity, Columns: ID, EntityID",
2025-08-23T11:06:13.529351,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,218,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.534152,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,228,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ')'. (102) (SQLExecDirectW)"")",
2025-08-23T11:06:13.537468,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py,230,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.544181,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Table name fix: REF_Entities → REF_Entity,,
2025-08-23T11:06:13.547865,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Table name fix: REF_Facts → REF_Fact,,
2025-08-23T11:06:13.551313,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Table name fix: REF_Opinions → REF_Opinion,,
2025-08-23T11:06:13.554337,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Table name fix: REF_Sources → REF_Source,,
2025-08-23T11:06:13.556720,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py,0,success,Table name fix: REF_Categories → REF_Category,,
2025-08-23T11:06:13.562724,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,122,failed,SQL Validation Test,"FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')",
2025-08-23T11:06:13.566746,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,145,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'table'. (156) (SQLExecDirectW)"")",
2025-08-23T11:06:13.571028,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,158,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.573970,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,566,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-23T11:06:13.579684,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,580,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-23T11:06:13.583645,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,587,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-23T11:06:13.586648,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,594,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-23T11:06:13.591233,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,601,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-23T11:06:13.595691,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,608,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-23T11:06:13.600182,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py,716,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-23T11:06:13.606303,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py,39,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-23T11:06:13.609061,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py,42,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.614354,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py,45,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: EntityID, Name, Value",
2025-08-23T11:06:13.617693,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py,45,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: EntityID, Name, Value",
2025-08-23T11:06:13.623800,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py,54,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name",
2025-08-23T11:06:13.628937,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py,36,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.632947,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py,36,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.641250,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,111,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.645675,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,111,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.650492,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,131,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.656255,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py,131,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.662195,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.666583,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,12,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.668607,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,83,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.675122,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py,83,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.693404,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,45,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.702742,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,66,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *",
2025-08-23T11:06:13.704741,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,67,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-23T11:06:13.704741,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,68,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-23T11:06:13.716954,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,71,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Value, Name",
2025-08-23T11:06:13.721893,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,72,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: EntityID, Name, Value",
2025-08-23T11:06:13.725009,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,75,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name",
2025-08-23T11:06:13.725009,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,76,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name, Status",
2025-08-23T11:06:13.733714,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,79,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID",
2025-08-23T11:06:13.739385,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,80,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: CreatedDate",
2025-08-23T11:06:13.742897,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,90,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Entity, Columns: EntityID, ID, Value, Name, Status",
2025-08-23T11:06:13.748487,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,90,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Entity, Columns: EntityID, ID, Value, Name, Status",
2025-08-23T11:06:13.752402,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,99,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: CreatedDate, Name, Status, Description",
2025-08-23T11:06:13.754405,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,99,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: CreatedDate, Name, Status, Description",
2025-08-23T11:06:13.754405,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,108,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: CreatedDate, Status, ModifiedDate",
2025-08-23T11:06:13.766223,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,108,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: CreatedDate, Status, ModifiedDate",
2025-08-23T11:06:13.769768,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,117,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: EntityID, Status",
2025-08-23T11:06:13.769768,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,117,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: EntityID, Status",
2025-08-23T11:06:13.779293,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,135,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Entity, Columns: EntityID, ID, CreatedDate, Description, Value, Name, Status",
2025-08-23T11:06:13.779293,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,135,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Entity, Columns: EntityID, ID, CreatedDate, Description, Value, Name, Status",
2025-08-23T11:06:13.779293,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,158,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: CreatedDate, Name, Status, Description",
2025-08-23T11:06:13.794318,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,158,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: CreatedDate, Name, Status, Description",
2025-08-23T11:06:13.794903,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,194,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: This, keyword, contains",
2025-08-23T11:06:13.794903,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,195,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-23T11:06:13.807903,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,198,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name, with",
2025-08-23T11:06:13.813746,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,198,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name, with",
2025-08-23T11:06:13.817787,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,205,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-23T11:06:13.822979,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,208,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-23T11:06:13.827050,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,211,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Entity, Columns: EntityID, ID, Value, Name, Type, Status, ParentID",
2025-08-23T11:06:13.832391,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,211,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Entity, Columns: EntityID, ID, Value, Name, Type, Status, ParentID",
2025-08-23T11:06:13.837390,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,236,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.842397,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,237,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.847732,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,238,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.852339,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,239,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.857475,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,243,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'transactions'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.862475,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,244,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'transactions'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.866462,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,269,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *",
2025-08-23T11:06:13.872386,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,285,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ')'. (102) (SQLExecDirectW)"")",
2025-08-23T11:06:13.879031,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,286,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-23T11:06:13.882862,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,287,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-23T11:06:13.888312,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,291,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'WHERE'. (156) (SQLExecDirectW)"")",
2025-08-23T11:06:13.893660,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,305,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Status",
2025-08-23T11:06:13.895660,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,309,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: successfully, completed, Test",
2025-08-23T11:06:13.903763,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py,309,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: successfully, completed, Test",
2025-08-23T11:06:13.912066,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py,21,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *",
2025-08-23T11:06:13.912066,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py,22,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-23T11:06:13.924103,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py,23,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name",
2025-08-23T11:06:13.929505,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py,32,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Status",
2025-08-23T11:06:13.929505,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py,32,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Status",
2025-08-23T11:06:13.941148,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,62,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.944069,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,71,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.951445,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,80,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.957638,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,89,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Products'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.962949,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,98,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Products'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.969066,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,107,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Products'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.975084,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,116,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.980231,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,125,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.985956,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,135,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.991454,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,149,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:13.999546,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,158,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.004919,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,168,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.010824,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,177,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.015867,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,187,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.020871,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,197,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.027717,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,207,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.032936,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,217,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.039541,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,227,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.045909,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,236,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Table1'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.050970,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,250,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.057382,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,259,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.063391,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,269,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.068285,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,279,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.075012,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,288,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.080695,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,298,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.080695,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,308,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Departments'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.091122,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,318,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: ID, FactID, ValidityRating, Name, DataSource",
2025-08-23T11:06:14.097692,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,328,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.103635,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,342,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.109224,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,351,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.115364,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,361,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'ActiveUsers'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.121066,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,371,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.127767,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,385,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.133648,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,394,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.136830,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,404,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.145993,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,414,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.151559,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,424,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.158358,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,438,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.164387,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,447,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.170300,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,457,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.175620,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,467,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.182150,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,481,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.190069,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,491,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.195389,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,501,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, XRF_Fact_Evidence, Columns: ID, FactID, Name",
2025-08-23T11:06:14.205349,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,511,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Table1'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.213683,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py,521,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.220068,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,27,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *",
2025-08-23T11:06:14.225084,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,28,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-23T11:06:14.229221,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,29,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-23T11:06:14.237031,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,30,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Value, Name",
2025-08-23T11:06:14.242064,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,31,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: EntityID, Name, Value",
2025-08-23T11:06:14.248232,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,32,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name",
2025-08-23T11:06:14.255106,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,33,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name, Status",
2025-08-23T11:06:14.256244,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,34,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID",
2025-08-23T11:06:14.267338,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,35,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: CreatedDate",
2025-08-23T11:06:14.272892,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,40,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: This, keyword, contains",
2025-08-23T11:06:14.279528,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,41,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: Name",
2025-08-23T11:06:14.284507,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,42,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-23T11:06:14.284507,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,43,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-23T11:06:14.297142,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,44,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-23T11:06:14.297142,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,47,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.313441,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,48,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.321548,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,49,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.326946,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,50,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.326946,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,51,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'transactions'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.338842,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,52,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'transactions'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.346035,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,67,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.351953,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,72,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ')'. (102) (SQLExecDirectW)"")",
2025-08-23T11:06:14.357678,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,73,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-23T11:06:14.365024,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,74,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ",
2025-08-23T11:06:14.370677,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,194,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name, Status",
2025-08-23T11:06:14.376064,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,194,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name, Status",
2025-08-23T11:06:14.376064,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,210,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.389381,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,218,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *",
2025-08-23T11:06:14.392553,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py,219,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, *",
2025-08-23T11:06:14.403525,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_db.py,11,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: *, Name",
2025-08-23T11:06:14.412925,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py,257,success,SQL Validation Test,"SUCCESS - Tables: REF_Fact, Columns: ",
2025-08-23T11:06:14.418439,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,0,success,Table name fix: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue,,
2025-08-23T11:06:14.426446,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,79,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.437542,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,79,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.443886,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,129,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: ID, Name",
2025-08-23T11:06:14.448548,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,135,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: CreateDate, Name, ModifyDate, Description",
2025-08-23T11:06:14.457086,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,135,success,SQL Validation Test,"SUCCESS - Tables: REF_Entity, Columns: CreateDate, Name, ModifyDate, Description",
2025-08-23T11:06:14.460432,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,360,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.472491,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,360,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.479090,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,393,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.485343,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py,393,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.492758,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_database.py,180,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.500299,repair,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,0,success,Table name fix: REF_EntityValues → REF_EntityValue,,
2025-08-23T11:06:14.505320,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,29,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.505320,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,29,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.519906,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,48,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: NumericValue, ValueUnits",
2025-08-23T11:06:14.524927,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,48,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: NumericValue, ValueUnits",
2025-08-23T11:06:14.533785,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,66,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, NumericValue, Name, ValueUnits",
2025-08-23T11:06:14.537473,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,66,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, NumericValue, Name, ValueUnits",
2025-08-23T11:06:14.537473,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,149,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, NumericValue, ValueUnits, ID",
2025-08-23T11:06:14.537473,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py,149,success,SQL Validation Test,"SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, NumericValue, ValueUnits, ID",
2025-08-23T11:06:14.563622,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py,56,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.568935,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py,56,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.583892,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_server.py,276,success,SQL Validation Test,"SUCCESS - Tables: REF_FACT, Columns: FactID, CreatedDate, Source, ValidityRating, FactText, LastUpdated, FactType",
2025-08-23T11:06:14.589252,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_server.py,276,success,SQL Validation Test,"SUCCESS - Tables: REF_FACT, Columns: FactID, CreatedDate, Source, ValidityRating, FactText, LastUpdated, FactType",
2025-08-23T11:06:14.596393,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,139,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.596393,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,139,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.611275,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,161,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.618592,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,161,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.628366,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,181,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.635042,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,181,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.643294,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,203,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.650278,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,203,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.656412,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,255,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.664393,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,255,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.670105,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,287,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.676770,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,287,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.684908,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,318,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.692815,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,318,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.698977,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,348,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.707559,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py,439,failed,SQL Validation Test,"FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')",
2025-08-23T11:06:14.739190,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\distlib\manifest.py,258,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)"")",
2025-08-23T11:06:14.747347,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\distlib\manifest.py,258,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW)"")",
2025-08-23T11:06:14.771789,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,208,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.782390,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,208,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.787670,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,244,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.796900,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,244,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.812637,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,1789,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.818473,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,1789,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.825366,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,1819,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.825366,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py,1819,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.849947,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\progress.py,1413,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.857412,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\progress.py,1413,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.938884,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_distutils\filelist.py,203,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)"")",
2025-08-23T11:06:14.943124,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_distutils\filelist.py,203,failed,SQL Validation Test,"FAILED - ('42000', ""[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW)"")",
2025-08-23T11:06:14.954919,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_vendor\importlib_metadata\__init__.py,349,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'self'. (208) (SQLExecDirectW)"")",
2025-08-23T11:06:14.963857,sql_test,C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_vendor\importlib_metadata\__init__.py,349,failed,SQL Validation Test,"FAILED - ('42S02', ""[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'self'. (208) (SQLExecDirectW)"")",
