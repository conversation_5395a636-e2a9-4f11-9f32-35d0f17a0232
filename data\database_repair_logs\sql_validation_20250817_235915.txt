SQL Validation Log - Session 20250817_235915
Started: 2025-08-17T23:59:15.991660
================================================================================

[2025-08-17T23:59:16.019912] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\example_usage.py:12
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.034562] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\example_usage.py:18
Status: FAILED
Original SQL: from core.healer_manager import SelfHealerManager
Test SQL: SELECT 1 FROM core WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'core'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.037536] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\example_usage.py:19
Status: FAILED
Original SQL: from n8n_builder.error_handler import ErrorDetail, ErrorCategory, ErrorSeverity
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.039536] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\example_usage.py:243
Status: FAILED
Original SQL: print("python -c \"import asyncio; from example_usage import demonstrate_manual_healing; asyncio.run(demonstrate_manual_healing())\"")
Test SQL: SELECT 1 FROM example_usage WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'example_usage'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.042954] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Start_SelfHealer.py:2
Status: FAILED
Original SQL: from Self_Healer.core.healer_manager import SelfHealerManager
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.045204] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Start_SelfHealer.py:3
Status: FAILED
Original SQL: from Self_Healer.dashboard.dashboard import run_dashboard
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.047708] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\__init__.py:12
Status: FAILED
Original SQL: - Learning & Improvement: Learns from successful and failed fixes
Test SQL: SELECT 1 FROM successful WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'successful'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.049705] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\__init__.py:31
Status: FAILED
Original SQL: from core.healer_manager import SelfHealerManager
Test SQL: SELECT 1 FROM core WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'core'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.053507] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\__init__.py:32
Status: FAILED
Original SQL: from core.error_monitor import ErrorMonitor
Test SQL: SELECT 1 FROM core WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'core'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.055507] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\__init__.py:33
Status: FAILED
Original SQL: from core.context_analyzer import ContextAnalyzer
Test SQL: SELECT 1 FROM core WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'core'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.057508] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\__init__.py:34
Status: FAILED
Original SQL: from core.solution_generator import SolutionGenerator
Test SQL: SELECT 1 FROM core WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'core'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.060015] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\__init__.py:35
Status: FAILED
Original SQL: from core.solution_validator import SolutionValidator
Test SQL: SELECT 1 FROM core WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'core'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.062877] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\__init__.py:36
Status: FAILED
Original SQL: from core.learning_engine import LearningEngine
Test SQL: SELECT 1 FROM core WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'core'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.065702] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:8
Status: FAILED
Original SQL: from typing import Dict, List, Any, Optional
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.065702] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:9
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.071033] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:12
Status: FAILED
Original SQL: from fastapi import APIRouter, HTTPException, Query
Test SQL: SELECT 1 FROM fastapi WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.074152] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:13
Status: FAILED
Original SQL: from fastapi.responses import JSONResponse
Test SQL: SELECT 1 FROM fastapi WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.074152] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:14
Status: FAILED
Original SQL: from pydantic import BaseModel
Test SQL: SELECT 1 FROM pydantic WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pydantic'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.080434] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:110
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
Test SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ','. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.087589] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:113
Status: SUCCESS
Original SQL: FROM REF_Fact f
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-17T23:59:16.090147] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:114
Status: SUCCESS
Original SQL: LEFT JOIN REF_Evidence e ON f.ID = e.FactID
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-17T23:59:16.097756] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:186
Status: FAILED
Original SQL: Update knowledge validity based on new evidence.
Test SQL: SELECT 1 FROM knowledge WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'knowledge'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.101759] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:195
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-17T23:59:16.101759] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:206
Status: SUCCESS
Original SQL: fact_query = "SELECT ValidityRating FROM REF_Fact WHERE ID = ?"
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: ValidityRating, ID
----------------------------------------

[2025-08-17T23:59:16.112519] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:218
Status: SUCCESS
Original SQL: count_query = "SELECT COUNT(*) as EvidenceCount FROM REF_Evidence WHERE FactID = ?"
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: COUNT, EvidenceCount, FactID
----------------------------------------

[2025-08-17T23:59:16.115401] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:261
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
Test SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ','. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.118403] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:263
Status: SUCCESS
Original SQL: FROM REF_Fact f
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-17T23:59:16.118403] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:277
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
Test SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ','. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.123717] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:279
Status: SUCCESS
Original SQL: FROM REF_Opinion o
Test SQL: SELECT 1 FROM REF_Opinion WHERE 1=0
Result: SUCCESS - Tables: REF_Opinion, Columns: 
----------------------------------------

[2025-08-17T23:59:16.126717] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:293
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
Test SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ','. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.127484] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:295
Status: SUCCESS
Original SQL: FROM REF_Evidence e
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-17T23:59:16.127484] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:296
Status: SUCCESS
Original SQL: JOIN REF_Fact f ON e.FactID = f.ID
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-17T23:59:16.133895] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:340
Status: SUCCESS
Original SQL: FROM REF_Fact f
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-17T23:59:16.142461] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py:11
Status: FAILED
Original SQL: from datetime import datetime, timedelta
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.142461] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py:12
Status: FAILED
Original SQL: from typing import Dict, List, Optional, Any, Set, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.151795] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py:13
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.155410] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py:14
Status: FAILED
Original SQL: from dataclasses import dataclass, field
Test SQL: SELECT 1 FROM dataclasses WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.155410] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py:23
Status: FAILED
Original SQL: from n8n_builder.error_handler import ErrorDetail
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.167725] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py:24
Status: FAILED
Original SQL: from n8n_builder.logging_config import get_logger
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.174242] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py:25
Status: FAILED
Original SQL: from n8n_builder.project_manager import project_manager, filesystem_utils
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.182173] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py:66
Status: FAILED
Original SQL: - Documentation retrieval from structured docs
Test SQL: SELECT 1 FROM structured WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'structured'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.185172] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py:340
Status: FAILED
Original SQL: """Extract relevant keywords from error information."""
Test SQL: SELECT 1 FROM error WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'error'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.185172] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\context_analyzer.py:577
Status: FAILED
Original SQL: """Clear old context information from cache."""
Test SQL: SELECT 1 FROM cache WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'cache'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.192481] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py:2
Status: FAILED
Original SQL: Error Monitor - Detects and classifies errors from log files and system state.
Test SQL: SELECT 1 FROM log WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'log'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.193483] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py:12
Status: FAILED
Original SQL: from datetime import datetime, timedelta
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.193483] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py:13
Status: FAILED
Original SQL: from typing import Dict, List, Optional, Set, Any, TYPE_CHECKING
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.200632] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py:17
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.204353] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py:18
Status: FAILED
Original SQL: from dataclasses import dataclass
Test SQL: SELECT 1 FROM dataclasses WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.208162] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py:19
Status: FAILED
Original SQL: from watchdog.observers import Observer
Test SQL: SELECT 1 FROM watchdog WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'watchdog'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.221231] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py:20
Status: FAILED
Original SQL: from watchdog.events import FileSystemEventHandler
Test SQL: SELECT 1 FROM watchdog WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'watchdog'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.231487] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py:28
Status: FAILED
Original SQL: from n8n_builder.error_handler import EnhancedErrorHandler, ErrorDetail
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.241517] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py:29
Status: FAILED
Original SQL: from n8n_builder.logging_config import get_logger
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.242381] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py:329
Status: FAILED
Original SQL: """Create an error from application monitoring."""
Test SQL: SELECT 1 FROM application WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'application'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.242381] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py:390
Status: FAILED
Original SQL: """Extract timestamp from log line."""
Test SQL: SELECT 1 FROM log WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'log'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.242381] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py:479
Status: FAILED
Original SQL: """Process detected errors from the queue."""
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.255093] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py:508
Status: FAILED
Original SQL: self.logger.info(f"Processed {processed_count} remaining items from error queue")
Test SQL: SELECT 1 FROM error WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'error'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.262974] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py:707
Status: FAILED
Original SQL: """Clear old errors from memory."""
Test SQL: SELECT 1 FROM memory WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'memory'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.268422] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py:752
Status: FAILED
Original SQL: self.logger.info(f"Starting scan from line {start_line}")
Test SQL: SELECT 1 FROM line WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'line'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.268422] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\error_monitor.py:837
Status: FAILED
Original SQL: self.logger.error(f"Error getting healing statistics from log: {e}")
Test SQL: SELECT 1 FROM log WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'log'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.277385] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\generic_types.py:8
Status: FAILED
Original SQL: from dataclasses import dataclass, field
Test SQL: SELECT 1 FROM dataclasses WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.279386] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\generic_types.py:9
Status: FAILED
Original SQL: from typing import Dict, List, Optional, Any
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.283184] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\generic_types.py:10
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.285760] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\generic_types.py:11
Status: FAILED
Original SQL: from enum import Enum
Test SQL: SELECT 1 FROM enum WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'enum'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.292107] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:11
Status: FAILED
Original SQL: from datetime import datetime, timedelta
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.295262] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:12
Status: FAILED
Original SQL: from typing import Dict, List, Optional, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.299069] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:13
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.306188] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:14
Status: FAILED
Original SQL: from dataclasses import dataclass, field
Test SQL: SELECT 1 FROM dataclasses WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.311212] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:15
Status: FAILED
Original SQL: from enum import Enum
Test SQL: SELECT 1 FROM enum WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'enum'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.324177] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:30
Status: FAILED
Original SQL: from project_adapter import ProjectAdapter
Test SQL: SELECT 1 FROM project_adapter WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'project_adapter'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.329539] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:31
Status: FAILED
Original SQL: from error_monitor import ErrorMonitor
Test SQL: SELECT 1 FROM error_monitor WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'error_monitor'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.336606] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:32
Status: FAILED
Original SQL: from context_analyzer import ContextAnalyzer
Test SQL: SELECT 1 FROM context_analyzer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'context_analyzer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.352310] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:33
Status: FAILED
Original SQL: from solution_generator import SolutionGenerator
Test SQL: SELECT 1 FROM solution_generator WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'solution_generator'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.356312] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:34
Status: FAILED
Original SQL: from solution_validator import SolutionValidator
Test SQL: SELECT 1 FROM solution_validator WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'solution_validator'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.357313] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:35
Status: FAILED
Original SQL: from learning_engine import LearningEngine
Test SQL: SELECT 1 FROM learning_engine WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'learning_engine'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.367240] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:36
Status: FAILED
Original SQL: from knowledge_integration import get_knowledge_integrator, KnowledgeBaseIntegrator
Test SQL: SELECT 1 FROM knowledge_integration WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'knowledge_integration'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.373066] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:42
Status: FAILED
Original SQL: from n8n_builder.error_handler import ErrorDetail
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.376065] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:68
Status: FAILED
Original SQL: selected_solution: Optional[Dict[str, Any]] = None
Test SQL: selected_solution: Optional[Dict[str, Any]] = None
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'Optional'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Unclosed quotation mark after the character string 'Dict[str, Any] = None'. (105)")
----------------------------------------

[2025-08-17T23:59:16.377068] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:132
Status: FAILED
Original SQL: """Load configuration from file or use defaults."""
Test SQL: SELECT 1 FROM file WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'file'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.399498] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:221
Status: FAILED
Original SQL: """Process an error directly from the application (not from log monitoring)."""
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.424070] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:370
Status: FAILED
Original SQL: """Process a complete healing session from analysis to implementation."""
Test SQL: SELECT 1 FROM analysis WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'analysis'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.428599] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:512
Status: FAILED
Original SQL: """Collect and update system metrics."""
Test SQL: SELECT 1 FROM system WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'system'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.428599] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:517
Status: FAILED
Original SQL: await asyncio.sleep(60)  # Update every minute
Test SQL: SELECT 1 FROM every WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'every'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.428599] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\healer_manager.py:542
Status: FAILED
Original SQL: """Update metrics from all components."""
Test SQL: SELECT 1 FROM all WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'all'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.428599] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_database_wrapper.py:7
Status: FAILED
Original SQL: from typing import Dict, List, Any, Optional
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.454751] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_database_wrapper.py:8
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.466233] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:8
Status: FAILED
Original SQL: from typing import Dict, List, Any, Optional, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.470832] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:9
Status: FAILED
Original SQL: from dataclasses import dataclass
Test SQL: SELECT 1 FROM dataclasses WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.489080] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:10
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.491080] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:17
Status: FAILED
Original SQL: from typing import TYPE_CHECKING
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.497482] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:23
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.499481] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:26
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.499481] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:242
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, ValidityRating, DataSource)
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-17T23:59:16.499481] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:268
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-17T23:59:16.499481] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:366
Status: FAILED
Original SQL: """Update solution validity rating based on new evidence."""
Test SQL: SELECT 1 FROM solution WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'solution'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.534980] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:375
Status: FAILED
Original SQL: SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
Test SQL: SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "f.ID" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "f.ValidityRating" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "e.ID" could not be bound. (4104)')
----------------------------------------

[2025-08-17T23:59:16.534980] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:376
Status: SUCCESS
Original SQL: FROM REF_Fact f
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-17T23:59:16.534980] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:377
Status: SUCCESS
Original SQL: LEFT JOIN REF_Evidence e ON f.ID = e.FactID
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-17T23:59:16.534980] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:402
Status: FAILED
Original SQL: self.logger.error(f"Failed to update solution validity: {e}")
Test SQL: SELECT 1 FROM solution WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'solution'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.551479] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:410
Status: SUCCESS
Original SQL: entity_query = "SELECT ID FROM REF_Entity WHERE Name = ?"
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: Name, ID
----------------------------------------

[2025-08-17T23:59:16.552536] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:420
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
Test SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "f.ID" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "f.Name" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "f.ValidityRating" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "f.DataSource" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "f.CreateDate" could not be bound. (4104)')
----------------------------------------

[2025-08-17T23:59:16.563111] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:421
Status: SUCCESS
Original SQL: FROM REF_Fact f
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-17T23:59:16.563111] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:431
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
Test SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "e.ID" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "e.Name" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "e.Evidence" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "e.DataSource" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "e.CreateDate" could not be bound. (4104)')
----------------------------------------

[2025-08-17T23:59:16.580685] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:432
Status: SUCCESS
Original SQL: FROM REF_Evidence e
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-17T23:59:16.580685] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:440
Status: FAILED
Original SQL: SELECT a.Name as AttributeName, ev.EntityValue, eav.CreateDate
Test SQL: SELECT a.Name as AttributeName, ev.EntityValue, eav.CreateDate
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "a.Name" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "ev.EntityValue" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "eav.CreateDate" could not be bound. (4104)')
----------------------------------------

[2025-08-17T23:59:16.590135] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:441
Status: FAILED
Original SQL: FROM XRF_EntityAttributeValue eav
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.593132] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:442
Status: SUCCESS
Original SQL: JOIN REF_Attribute a ON eav.AttributeID = a.ID
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-17T23:59:16.595132] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:443
Status: SUCCESS
Original SQL: JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:16.595132] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:466
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
Test SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ','. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.605213] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:468
Status: SUCCESS
Original SQL: FROM REF_Fact f
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-17T23:59:16.617169] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:469
Status: SUCCESS
Original SQL: LEFT JOIN REF_Evidence e ON f.ID = e.FactID
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-17T23:59:16.617169] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:478
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
Test SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "o.ID" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "o.Name" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "o.ValidityRating" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "o.Opinion" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "o.DataSource" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "o.CreateDate" could not be bound. (4104)')
----------------------------------------

[2025-08-17T23:59:16.633610] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:479
Status: SUCCESS
Original SQL: FROM REF_Opinion o
Test SQL: SELECT 1 FROM REF_Opinion WHERE 1=0
Result: SUCCESS - Tables: REF_Opinion, Columns: 
----------------------------------------

[2025-08-17T23:59:16.642279] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:552
Status: SUCCESS
Original SQL: query = "SELECT ID FROM REF_Category WHERE Name = ?"
Test SQL: SELECT 1 FROM REF_Category WHERE 1=0
Result: SUCCESS - Tables: REF_Category, Columns: Name, ID
----------------------------------------

[2025-08-17T23:59:16.646560] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:566
Status: SUCCESS
Original SQL: query = "SELECT ID FROM REF_Entity WHERE Name = ?"
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: Name, ID
----------------------------------------

[2025-08-17T23:59:16.648564] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:580
Status: SUCCESS
Original SQL: query = "SELECT ID FROM REF_Attribute WHERE Name = ?"
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: Name, ID
----------------------------------------

[2025-08-17T23:59:16.648564] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:594
Status: SUCCESS
Original SQL: query = "SELECT ID FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?"
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, Name, ID
----------------------------------------

[2025-08-17T23:59:16.648564] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:603
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:16.648564] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:621
Status: FAILED
Original SQL: SELECT ID FROM XRF_EntityAttributeValue
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.664697] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:631
Status: FAILED
Original SQL: INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.664697] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py:2
Status: FAILED
Original SQL: Learning Engine - Learns from healing results to improve future performance.
Test SQL: SELECT 1 FROM healing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'healing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.676067] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py:11
Status: FAILED
Original SQL: from datetime import datetime, timedelta
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.681833] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py:12
Status: FAILED
Original SQL: from typing import Dict, List, Optional, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.682834] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py:13
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.682834] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py:14
Status: FAILED
Original SQL: from dataclasses import dataclass, field
Test SQL: SELECT 1 FROM dataclasses WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.714171] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py:15
Status: FAILED
Original SQL: from collections import defaultdict, Counter
Test SQL: SELECT 1 FROM collections WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.733020] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py:23
Status: FAILED
Original SQL: from n8n_builder.error_handler import ErrorDetail
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.737919] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py:24
Status: FAILED
Original SQL: from n8n_builder.logging_config import get_logger
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.737919] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py:79
Status: FAILED
Original SQL: """Insight derived from learning analysis."""
Test SQL: SELECT 1 FROM learning WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'learning'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.746629] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py:97
Status: FAILED
Original SQL: Learns from healing results to improve future performance.
Test SQL: SELECT 1 FROM healing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'healing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.748634] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py:248
Status: FAILED
Original SQL: """Extract relevant keywords from error information."""
Test SQL: SELECT 1 FROM error WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'error'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.748634] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py:364
Status: FAILED
Original SQL: """Analyze learning records to identify and update patterns."""
Test SQL: SELECT 1 FROM patterns WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'patterns'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.759886] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py:445
Status: FAILED
Original SQL: """Generate learning insights from patterns and records."""
Test SQL: SELECT 1 FROM patterns WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'patterns'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.764396] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\learning_engine.py:577
Status: FAILED
Original SQL: """Load existing learning data from disk."""
Test SQL: SELECT 1 FROM disk WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'disk'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.779422] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\project_adapter.py:12
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.779422] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\project_adapter.py:13
Status: FAILED
Original SQL: from typing import Dict, List, Optional, Any, Union, Callable
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.805931] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\project_adapter.py:16
Status: FAILED
Original SQL: from dataclasses import dataclass, field
Test SQL: SELECT 1 FROM dataclasses WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.805931] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\project_adapter.py:94
Status: FAILED
Original SQL: """Load project configuration from YAML file."""
Test SQL: SELECT 1 FROM YAML WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'YAML'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.819311] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_generator.py:11
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.819311] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_generator.py:12
Status: FAILED
Original SQL: from typing import Dict, List, Optional, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.830201] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_generator.py:13
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.831340] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_generator.py:14
Status: FAILED
Original SQL: from dataclasses import dataclass, field
Test SQL: SELECT 1 FROM dataclasses WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.839738] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_generator.py:15
Status: FAILED
Original SQL: from enum import Enum
Test SQL: SELECT 1 FROM enum WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'enum'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.841738] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_generator.py:23
Status: FAILED
Original SQL: from n8n_builder.error_handler import ErrorDetail
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.841738] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_generator.py:24
Status: FAILED
Original SQL: from n8n_builder.logging_config import get_logger
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.853097] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_generator.py:25
Status: FAILED
Original SQL: from n8n_builder.config import config
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.853097] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_validator.py:13
Status: FAILED
Original SQL: from datetime import datetime, timedelta
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.863590] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_validator.py:14
Status: FAILED
Original SQL: from typing import Dict, List, Optional, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.868566] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_validator.py:15
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.870566] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_validator.py:16
Status: FAILED
Original SQL: from dataclasses import dataclass, field
Test SQL: SELECT 1 FROM dataclasses WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.870566] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_validator.py:17
Status: FAILED
Original SQL: from enum import Enum
Test SQL: SELECT 1 FROM enum WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'enum'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.881641] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_validator.py:25
Status: FAILED
Original SQL: from n8n_builder.error_handler import ErrorDetail
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.885916] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_validator.py:26
Status: FAILED
Original SQL: from n8n_builder.logging_config import get_logger
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.890648] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\solution_validator.py:27
Status: FAILED
Original SQL: from n8n_builder.project_manager import project_manager, filesystem_utils
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.899598] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:11
Status: FAILED
Original SQL: from datetime import datetime, timedelta
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.899598] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:12
Status: FAILED
Original SQL: from typing import Dict, List, Optional, Any
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.908785] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:13
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.912290] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:15
Status: FAILED
Original SQL: from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
Test SQL: SELECT 1 FROM fastapi WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.919881] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:16
Status: FAILED
Original SQL: from websockets.exceptions import ConnectionClosedError
Test SQL: SELECT 1 FROM websockets WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'websockets'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.925170] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:17
Status: FAILED
Original SQL: from fastapi.responses import HTMLResponse, JSONResponse
Test SQL: SELECT 1 FROM fastapi WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.933286] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:18
Status: FAILED
Original SQL: from fastapi.staticfiles import StaticFiles
Test SQL: SELECT 1 FROM fastapi WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.933286] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:19
Status: FAILED
Original SQL: from fastapi.templating import Jinja2Templates
Test SQL: SELECT 1 FROM fastapi WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.946411] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:27
Status: FAILED
Original SQL: from n8n_builder.logging_config import get_logger
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.948412] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:28
Status: FAILED
Original SQL: from Self_Healer.core.healer_manager import SelfHealerManager
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.957562] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:29
Status: FAILED
Original SQL: from Self_Healer.api.knowledge_endpoints import knowledge_router
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.957562] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:193
Status: FAILED
Original SQL: """Get healing statistics from log analysis."""
Test SQL: SELECT 1 FROM log WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'log'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.967409] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:643
Status: FAILED
Original SQL: }, 10000); // Update every 10 seconds
Test SQL: SELECT 1 FROM every WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'every'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.968215] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:684
Status: FAILED
Original SQL: """Get comprehensive dashboard data from KnowledgeBase."""
Test SQL: SELECT 1 FROM KnowledgeBase WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'KnowledgeBase'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.977809] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:720
Status: FAILED
Original SQL: """Get session analytics from KnowledgeBase."""
Test SQL: SELECT 1 FROM KnowledgeBase WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'KnowledgeBase'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.980808] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:723
Status: FAILED
Original SQL: from Self_Healer.core.knowledge_database_wrapper import SelfHealerKnowledgeDB
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.988813] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:751
Status: FAILED
Original SQL: self.logger.error(f"Failed to get analytics from KnowledgeBase: {e}")
Test SQL: SELECT 1 FROM KnowledgeBase WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'KnowledgeBase'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.991970] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:766
Status: FAILED
Original SQL: """Get healing session data from KnowledgeBase using stored procedure."""
Test SQL: SELECT 1 FROM KnowledgeBase WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'KnowledgeBase'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:16.999293] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:769
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.004621] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:813
Status: FAILED
Original SQL: self.logger.debug(f"Retrieved {len(sessions)} sessions from KnowledgeBase")
Test SQL: SELECT 1 FROM KnowledgeBase WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'KnowledgeBase'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.004621] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:817
Status: FAILED
Original SQL: self.logger.error(f"Failed to get sessions from KnowledgeBase: {e}")
Test SQL: SELECT 1 FROM KnowledgeBase WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'KnowledgeBase'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.014078] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:838
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.018659] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:856
Status: FAILED
Original SQL: SELECT ID, Name, CreateDate
Test SQL: SELECT ID, Name, CreateDate
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'ID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Name'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreateDate'. (207)")
----------------------------------------

[2025-08-17T23:59:17.024862] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:857
Status: SUCCESS
Original SQL: FROM REF_Entity
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:17.034300] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:880
Status: FAILED
Original SQL: FROM XRF_EntityAttributeValue eav
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.038762] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:881
Status: SUCCESS
Original SQL: JOIN REF_Attribute a ON eav.AttributeID = a.ID
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-17T23:59:17.041541] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:882
Status: SUCCESS
Original SQL: JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:17.049010] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:915
Status: FAILED
Original SQL: SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate
Test SQL: SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "f.Name" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "f.ValidityRating" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "f.DataSource" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "f.CreateDate" could not be bound. (4104)')
----------------------------------------

[2025-08-17T23:59:17.053985] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:916
Status: SUCCESS
Original SQL: FROM REF_Fact f
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-17T23:59:17.058989] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:927
Status: FAILED
Original SQL: SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate
Test SQL: SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "e.Name" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "e.Evidence" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "e.DataSource" could not be bound. (4104); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "e.CreateDate" could not be bound. (4104)')
----------------------------------------

[2025-08-17T23:59:17.064562] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:928
Status: SUCCESS
Original SQL: FROM REF_Evidence e
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-17T23:59:17.066121] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:972
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.066121] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:992
Status: FAILED
Original SQL: SELECT Name, ValidityRating, DataSource, CreateDate
Test SQL: SELECT Name, ValidityRating, DataSource, CreateDate
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Name'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'ValidityRating'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'DataSource'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreateDate'. (207)")
----------------------------------------

[2025-08-17T23:59:17.080530] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:993
Status: SUCCESS
Original SQL: FROM REF_Fact
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-17T23:59:17.080530] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1010
Status: FAILED
Original SQL: SELECT Name, ValidityRating, Opinion, DataSource, CreateDate
Test SQL: SELECT Name, ValidityRating, Opinion, DataSource, CreateDate
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Name'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'ValidityRating'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Opinion'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'DataSource'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreateDate'. (207)")
----------------------------------------

[2025-08-17T23:59:17.090575] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1011
Status: SUCCESS
Original SQL: FROM REF_Opinion
Test SQL: SELECT 1 FROM REF_Opinion WHERE 1=0
Result: SUCCESS - Tables: REF_Opinion, Columns: 
----------------------------------------

[2025-08-17T23:59:17.095581] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1022
Status: FAILED
Original SQL: SELECT DISTINCT e.Name, e.CreateDate
Test SQL: SELECT DISTINCT e.Name, e.CreateDate
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "e.Name" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "e.CreateDate" could not be bound. (4104)')
----------------------------------------

[2025-08-17T23:59:17.101332] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1023
Status: SUCCESS
Original SQL: FROM REF_Entity e
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:17.103332] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1026
Status: FAILED
Original SQL: SELECT 1 FROM XRF_EntityAttributeValue eav
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue eav
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.112571] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1027
Status: SUCCESS
Original SQL: JOIN REF_Attribute a ON eav.AttributeID = a.ID
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-17T23:59:17.113103] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1028
Status: SUCCESS
Original SQL: JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:17.121244] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1104
Status: FAILED
Original SQL: """Extract error type from fact/evidence names."""
Test SQL: SELECT 1 FROM fact WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fact'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.128103] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1227
Status: FAILED
Original SQL: await asyncio.sleep(5)  # Update every 5 seconds
Test SQL: SELECT 1 FROM every WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'every'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.133625] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:10
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.140556] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:35
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
Test SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'COLUMN_NAME'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'DATA_TYPE'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'IS_NULLABLE'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'COLUMN_DEFAULT'. (207)")
----------------------------------------

[2025-08-17T23:59:17.145767] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:36
Status: FAILED
Original SQL: FROM INFORMATION_SCHEMA.COLUMNS
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.145767] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:67
Status: FAILED
Original SQL: SELECT name, create_date, modify_date
Test SQL: SELECT name, create_date, modify_date
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'name'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'create_date'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'modify_date'. (207)")
----------------------------------------

[2025-08-17T23:59:17.157875] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:68
Status: FAILED
Original SQL: FROM sys.procedures
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.163566] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:93
Status: FAILED
Original SQL: check_query = f"SELECT name FROM sys.procedures WHERE name = '{proc_name}'"
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.169510] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:107
Status: FAILED
Original SQL: SELECT TABLE_NAME
Test SQL: SELECT TABLE_NAME
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'TABLE_NAME'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.175586] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:108
Status: FAILED
Original SQL: FROM INFORMATION_SCHEMA.TABLES
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.182411] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:2
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.187714] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:9
Status: FAILED
Original SQL: tables_query = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME"
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.192933] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:17
Status: SUCCESS
Original SQL: entities_query = "SELECT TOP 5 * FROM REF_Entity WHERE Name LIKE 'Session_%'"
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:17.192933] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:26
Status: SUCCESS
Original SQL: all_entities = await db_tool.execute_query("SELECT TOP 10 * FROM REF_Entity ORDER BY CreateDate DESC")
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:17.205801] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:33
Status: FAILED
Original SQL: xrf_query = "SELECT TOP 5 * FROM XRF_EntityAttributeValue"
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.211229] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py:2
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.216919] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py:27
Status: SUCCESS
Original SQL: FROM REF_Entity e
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:17.217920] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py:49
Status: FAILED
Original SQL: SELECT name, create_date
Test SQL: SELECT name, create_date
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'name'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'create_date'. (207)")
----------------------------------------

[2025-08-17T23:59:17.229096] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py:50
Status: FAILED
Original SQL: FROM sys.procedures
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.236433] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_simple_session_procedure.py:2
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.238373] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_simple_session_procedure.py:9
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_SelfHealer_RecentSessions_P')
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.249438] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_simple_session_procedure.py:28
Status: FAILED
Original SQL: SELECT TOP (@Limit)
Test SQL: SELECT TOP (@Limit)
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable "@Limit". (137) (SQLExecDirectW)')
----------------------------------------

[2025-08-17T23:59:17.249438] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_simple_session_procedure.py:36
Status: SUCCESS
Original SQL: FROM REF_Entity e
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:17.260477] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:5
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema')
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.272554] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:14
Status: FAILED
Original SQL: NOTES:     Administrative procedure protected from auto-generation utility.
Test SQL: SELECT 1 FROM auto WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'auto'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.273553] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:35
Status: SUCCESS
Original SQL: SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA.TABLES
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME
----------------------------------------

[2025-08-17T23:59:17.305228] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:39
Status: SUCCESS
Original SQL: SELECT name FROM sys.objects WHERE type = 'P' AND name LIKE '%Schema%'
Test SQL: SELECT 1 FROM sys.objects WHERE type = 'P' AND name LIKE '%Schema%'
Result: SUCCESS - Tables: sys, Columns: name, type
----------------------------------------

[2025-08-17T23:59:17.305228] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:42
Status: FAILED
Original SQL: SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = REF_Entity
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = REF_Entity
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_Entity'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.320302] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:66
Status: FAILED
Original SQL: FROM INFORMATION_SCHEMA.TABLES t
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.328893] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:87
Status: FAILED
Original SQL: FROM INFORMATION_SCHEMA.COLUMNS c
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.333766] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:89
Status: FAILED
Original SQL: SELECT ku.TABLE_NAME, ku.COLUMN_NAME
Test SQL: SELECT ku.TABLE_NAME, ku.COLUMN_NAME
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "ku.TABLE_NAME" could not be bound. (4104) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The multi-part identifier "ku.COLUMN_NAME" could not be bound. (4104)')
----------------------------------------

[2025-08-17T23:59:17.333766] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:90
Status: FAILED
Original SQL: FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.347501] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:91
Status: FAILED
Original SQL: INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.348508] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:101
Status: FAILED
Original SQL: FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.359007] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:102
Status: FAILED
Original SQL: INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.365942] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:104
Status: FAILED
Original SQL: INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku2
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.366946] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:133
Status: FAILED
Original SQL: (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS c WHERE c.TABLE_NAME = t.TABLE_NAME) as ColumnCount
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.377014] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:134
Status: FAILED
Original SQL: FROM INFORMATION_SCHEMA.TABLES t
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.380695] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:147
Status: FAILED
Original SQL: FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.391552] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:148
Status: FAILED
Original SQL: INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.397788] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:150
Status: FAILED
Original SQL: INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku2
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.397788] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:159
Status: FAILED
Original SQL: (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS c WHERE c.TABLE_NAME = t.TABLE_NAME) as ColumnCount
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.413443] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:160
Status: FAILED
Original SQL: FROM INFORMATION_SCHEMA.TABLES t
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.424500] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:9
Status: FAILED
Original SQL: NOTES:     Administrative procedures protected from auto-generation utility.
Test SQL: SELECT 1 FROM auto WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'auto'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.434039] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:20
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_XRF_EntityAttributeValue_P_EntityID')
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.445086] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:45
Status: SUCCESS
Original SQL: SELECT ID, Name FROM REF_Entity WHERE ID = 123
Test SQL: SELECT 1 FROM REF_Entity WHERE ID = 123
Result: SUCCESS - Tables: REF_Entity, Columns: Name, ID
----------------------------------------

[2025-08-17T23:59:17.445086] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:48
Status: SUCCESS
Original SQL: SELECT * FROM REF_Attribute ORDER BY Name
Test SQL: SELECT 1 FROM REF_Attribute ORDER BY Name
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-17T23:59:17.461771] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:63
Status: FAILED
Original SQL: FROM XRF_EntityAttributeValue eav
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.467412] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:64
Status: SUCCESS
Original SQL: JOIN REF_Attribute a ON eav.AttributeID = a.ID
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-17T23:59:17.473091] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:65
Status: SUCCESS
Original SQL: JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:17.479659] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:76
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_REF_Fact_P_ErrorType')
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.486024] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:102
Status: SUCCESS
Original SQL: SELECT Name, ValidityRating FROM REF_Fact
Test SQL: SELECT 1 FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Name, ValidityRating
----------------------------------------

[2025-08-17T23:59:17.487682] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:120
Status: SUCCESS
Original SQL: FROM REF_Fact f
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-17T23:59:17.487682] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:132
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_REF_Opinion_P_ErrorType')
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.503725] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:170
Status: SUCCESS
Original SQL: FROM REF_Opinion o
Test SQL: SELECT 1 FROM REF_Opinion WHERE 1=0
Result: SUCCESS - Tables: REF_Opinion, Columns: 
----------------------------------------

[2025-08-17T23:59:17.503725] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:182
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_REF_Fact_SelfHealer_Analytics_Prms')
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.517502] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:222
Status: FAILED
Original SQL: SELECT TOP (@Limit)
Test SQL: SELECT TOP (@Limit)
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable "@Limit". (137) (SQLExecDirectW)')
----------------------------------------

[2025-08-17T23:59:17.517502] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:234
Status: SUCCESS
Original SQL: FROM REF_Fact f
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-17T23:59:17.527019] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:235
Status: SUCCESS
Original SQL: LEFT JOIN REF_Evidence e ON f.ID = e.FactID
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-17T23:59:17.534644] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:249
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_SelfHealer_KnowledgeSearch_Prms')
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.539793] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:296
Status: FAILED
Original SQL: SELECT TOP (@Limit)
Test SQL: SELECT TOP (@Limit)
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable "@Limit". (137) (SQLExecDirectW)')
----------------------------------------

[2025-08-17T23:59:17.546387] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:303
Status: SUCCESS
Original SQL: FROM REF_Fact f
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-17T23:59:17.548850] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:313
Status: FAILED
Original SQL: SELECT TOP (@Limit)
Test SQL: SELECT TOP (@Limit)
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable "@Limit". (137) (SQLExecDirectW)')
----------------------------------------

[2025-08-17T23:59:17.559294] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:321
Status: SUCCESS
Original SQL: FROM REF_Opinion o
Test SQL: SELECT 1 FROM REF_Opinion WHERE 1=0
Result: SUCCESS - Tables: REF_Opinion, Columns: 
----------------------------------------

[2025-08-17T23:59:17.568665] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:333
Status: FAILED
Original SQL: SELECT TOP (@Limit)
Test SQL: SELECT TOP (@Limit)
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable "@Limit". (137) (SQLExecDirectW)')
----------------------------------------

[2025-08-17T23:59:17.571668] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:341
Status: SUCCESS
Original SQL: FROM REF_Evidence e
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-17T23:59:17.581645] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql:342
Status: SUCCESS
Original SQL: JOIN REF_Fact f ON e.FactID = f.ID
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-17T23:59:17.589205] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:20
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_SelfHealer_RecentSessions_P')
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.592768] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:51
Status: FAILED
Original SQL: SELECT TOP (@Limit)
Test SQL: SELECT TOP (@Limit)
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable "@Limit". (137) (SQLExecDirectW)')
----------------------------------------

[2025-08-17T23:59:17.602223] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:66
Status: SUCCESS
Original SQL: FROM REF_Entity e
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:17.602223] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:67
Status: FAILED
Original SQL: LEFT JOIN XRF_EntityAttributeValue eav_duration ON e.ID = eav_duration.EntityID
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.616464] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:68
Status: SUCCESS
Original SQL: LEFT JOIN REF_Attribute attr_duration ON eav_duration.AttributeID = attr_duration.ID
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-17T23:59:17.619262] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:70
Status: SUCCESS
Original SQL: LEFT JOIN REF_EntityValue duration_val ON eav_duration.ValueID = duration_val.ID
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:17.631627] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:72
Status: FAILED
Original SQL: LEFT JOIN XRF_EntityAttributeValue eav_success ON e.ID = eav_success.EntityID
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.635621] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:73
Status: SUCCESS
Original SQL: LEFT JOIN REF_Attribute attr_success ON eav_success.AttributeID = attr_success.ID
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-17T23:59:17.642761] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:75
Status: SUCCESS
Original SQL: LEFT JOIN REF_EntityValue success_val ON eav_success.ValueID = success_val.ID
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:17.648562] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:87
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_SelfHealer_SessionDetails_P')
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.657521] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:119
Status: FAILED
Original SQL: SELECT @EntityID = ID FROM REF_Entity WHERE Name = 'Session_' + @SessionID
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = 'Session_' + @SessionID
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable "@SessionID". (137) (SQLExecDirectW)')
----------------------------------------

[2025-08-17T23:59:17.663534] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:123
Status: SUCCESS
Original SQL: SELECT 'Session not found' as Error
Test SQL: SELECT 'Session not found' as Error
Result: SUCCESS - Tables: , Columns: 
----------------------------------------

[2025-08-17T23:59:17.670096] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:135
Status: SUCCESS
Original SQL: FROM REF_Entity e
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:17.679241] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:136
Status: FAILED
Original SQL: LEFT JOIN XRF_EntityAttributeValue eav ON e.ID = eav.EntityID
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.680240] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:137
Status: SUCCESS
Original SQL: LEFT JOIN REF_Attribute attr ON eav.AttributeID = attr.ID
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-17T23:59:17.680240] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:138
Status: SUCCESS
Original SQL: LEFT JOIN REF_EntityValue val ON eav.EntityValueID = val.ID
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:17.700471] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:149
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_SelfHealer_SessionAnalytics_P')
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.705184] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:204
Status: SUCCESS
Original SQL: FROM REF_Entity e
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:17.713745] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:205
Status: FAILED
Original SQL: LEFT JOIN XRF_EntityAttributeValue eav_success ON e.ID = eav_success.EntityID
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.718779] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:206
Status: SUCCESS
Original SQL: LEFT JOIN REF_Attribute attr_success ON eav_success.AttributeID = attr_success.ID
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-17T23:59:17.718779] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:208
Status: SUCCESS
Original SQL: LEFT JOIN REF_EntityValue success_val ON eav_success.EntityValueID = success_val.ID
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:17.734303] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:210
Status: FAILED
Original SQL: LEFT JOIN XRF_EntityAttributeValue eav_duration ON e.ID = eav_duration.EntityID
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.740549] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:211
Status: SUCCESS
Original SQL: LEFT JOIN REF_Attribute attr_duration ON eav_duration.AttributeID = attr_duration.ID
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-17T23:59:17.740549] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:213
Status: SUCCESS
Original SQL: LEFT JOIN REF_EntityValue duration_val ON eav_duration.EntityValueID = duration_val.ID
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:17.756191] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:225
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_SelfHealer_SessionsByStatus_Prms')
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.761162] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:262
Status: FAILED
Original SQL: SELECT TOP (@Limit)
Test SQL: SELECT TOP (@Limit)
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable "@Limit". (137) (SQLExecDirectW)')
----------------------------------------

[2025-08-17T23:59:17.769299] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:277
Status: SUCCESS
Original SQL: FROM REF_Entity e
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:17.773893] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:278
Status: FAILED
Original SQL: LEFT JOIN XRF_EntityAttributeValue eav_duration ON e.ID = eav_duration.EntityID
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.784333] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:279
Status: SUCCESS
Original SQL: LEFT JOIN REF_Attribute attr_duration ON eav_duration.AttributeID = attr_duration.ID
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-17T23:59:17.784333] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:281
Status: SUCCESS
Original SQL: LEFT JOIN REF_EntityValue duration_val ON eav_duration.ValueID = duration_val.ID
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:17.802984] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:283
Status: FAILED
Original SQL: LEFT JOIN XRF_EntityAttributeValue eav_success ON e.ID = eav_success.EntityID
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.804983] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:284
Status: SUCCESS
Original SQL: LEFT JOIN REF_Attribute attr_success ON eav_success.AttributeID = attr_success.ID
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-17T23:59:17.814573] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql:286
Status: SUCCESS
Original SQL: LEFT JOIN REF_EntityValue success_val ON eav_success.EntityValueID = success_val.ID
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:17.821711] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql:9
Status: FAILED
Original SQL: NOTES:     Renames XRF tables from CamelCase to proper underscore format.
Test SQL: SELECT 1 FROM CamelCase WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'CamelCase'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.834175] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql:20
Status: FAILED
Original SQL: 1. Run this script to rename tables and update references
Test SQL: SELECT 1 FROM references WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'references'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.836186] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql:22
Status: FAILED
Original SQL: 3. Update any custom _SYS_ procedures to use new table names
Test SQL: SELECT 1 FROM any WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'any'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.847507] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql:37
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'XRF_CategoryEvidence')
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.847507] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql:46
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'XRF_CategoryFact')
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.863873] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql:55
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'XRF_CategoryOpinion')
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.863873] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql:64
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'XRF_CrossCorrelation')
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.877027] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql:73
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'XRF_EntityAttributeValue')
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.877027] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql:82
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'XRF_EntityCategoryEntityAttributeValue')
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.892559] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql:93
Status: FAILED
Original SQL: -- 2. UPDATE EXISTING STORED PROCEDURES
Test SQL: SELECT 1 FROM EXISTING WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'EXISTING'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.892559] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql:98
Status: FAILED
Original SQL: -- Update S_SYS_SelfHealer_RecentSessions_P if it references old table names
Test SQL: SELECT 1 FROM S_SYS_SelfHealer_RecentSessions_P WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'S_SYS_SelfHealer_RecentSessions_P'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.908211] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql:102
Status: FAILED
Original SQL: -- Update S_SYS_SelfHealer_SessionAnalytics_P if it references old table names
Test SQL: SELECT 1 FROM S_SYS_SelfHealer_SessionAnalytics_P WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'S_SYS_SelfHealer_SessionAnalytics_P'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.919350] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql:122
Status: FAILED
Original SQL: FROM INFORMATION_SCHEMA.TABLES
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.924110] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql:132
Status: FAILED
Original SQL: PRINT '2. Update Self-Healer _SYS_ procedures to reference new table names'
Test SQL: SELECT 1 FROM Self WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.924110] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql:134
Status: FAILED
Original SQL: PRINT '4. Update any application code that references old table names'
Test SQL: SELECT 1 FROM any WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'any'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.942201] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\get_knowledgebase_schema.py:7
Status: FAILED
Original SQL: retrieve schema information from the KnowledgeBase database.
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.955329] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\get_knowledgebase_schema.py:26
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.963401] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\get_knowledgebase_schema.py:27
Status: FAILED
Original SQL: from typing import Optional, Dict, Any, List
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.970787] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\get_knowledgebase_schema.py:33
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.975521] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\get_knowledgebase_schema.py:77
Status: FAILED
Original SQL: """Process the results from the stored procedure."""
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.986159] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\get_knowledgebase_schema.py:220
Status: FAILED
Original SQL: md.append("| From Table | From Column | To Table | To Column |")
Test SQL: SELECT 1 FROM Column WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'Column'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:17.986159] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\install_and_test.py:22
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.001556] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\install_and_test.py:28
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.009143] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\install_and_test.py:121
Status: FAILED
Original SQL: from get_knowledgebase_schema import KnowledgeBaseSchemaRetriever
Test SQL: SELECT 1 FROM get_knowledgebase_schema WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'get_knowledgebase_schema'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.020023] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:22
Status: FAILED
Original SQL: - Update procedures to use NumericValue for calculations, ValueUnits for formatting
Test SQL: SELECT 1 FROM procedures WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'procedures'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.024391] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:34
Status: FAILED
Original SQL: IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.033584] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:46
Status: FAILED
Original SQL: IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.033584] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:64
Status: FAILED
Original SQL: -- Update Duration Values (e.g., "23.5 seconds" -> NumericValue: 23.5, ValueUnits: "seconds")
Test SQL: SELECT 1 FROM Duration WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Duration'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.048932] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:65
Status: SUCCESS
Original SQL: UPDATE REF_EntityValue
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:18.048932] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:77
Status: FAILED
Original SQL: AND NumericValue IS NULL  -- Only update if not already set
Test SQL: SELECT 1 FROM if WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'if'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.064954] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:81
Status: FAILED
Original SQL: -- Update Success Rate Values (e.g., "90-100%" -> NumericValue: 95.0, ValueUnits: "percentage")
Test SQL: SELECT 1 FROM Success WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Success'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.064954] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:82
Status: SUCCESS
Original SQL: UPDATE REF_EntityValue
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:18.083274] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:97
Status: FAILED
Original SQL: AND NumericValue IS NULL  -- Only update if not already set
Test SQL: SELECT 1 FROM if WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'if'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.086277] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:101
Status: FAILED
Original SQL: -- Update Simple Percentage Values (e.g., "75%" -> NumericValue: 75.0, ValueUnits: "percentage")
Test SQL: SELECT 1 FROM Simple WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Simple'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.097725] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:102
Status: SUCCESS
Original SQL: UPDATE REF_EntityValue
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:18.097725] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:115
Status: FAILED
Original SQL: AND NumericValue IS NULL  -- Only update if not already set
Test SQL: SELECT 1 FROM if WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'if'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.114218] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:119
Status: FAILED
Original SQL: -- Update Millisecond Values (e.g., "1500ms" -> NumericValue: 1500.0, ValueUnits: "milliseconds")
Test SQL: SELECT 1 FROM Millisecond WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Millisecond'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.116730] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:120
Status: SUCCESS
Original SQL: UPDATE REF_EntityValue
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:18.128818] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:132
Status: FAILED
Original SQL: AND NumericValue IS NULL  -- Only update if not already set
Test SQL: SELECT 1 FROM if WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'if'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.134935] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:143
Status: FAILED
Original SQL: SELECT TOP 10
Test SQL: SELECT TOP 10
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '10'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.142508] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:149
Status: SUCCESS
Original SQL: FROM REF_EntityValue
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:18.146672] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:156
Status: FAILED
Original SQL: PRINT '1. Update stored procedures to use NumericValue for calculations'
Test SQL: SELECT 1 FROM stored WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'stored'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.156737] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql:157
Status: FAILED
Original SQL: PRINT '2. Update Self-Healer code to populate new fields'
Test SQL: SELECT 1 FROM Self WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.166779] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:5
Status: FAILED
Original SQL: PURPOSE:   Update stored procedures to use NumericValue field for calculations
Test SQL: SELECT 1 FROM stored WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'stored'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.173613] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:22
Status: FAILED
Original SQL: -- 1. UPDATE SESSION ANALYTICS PROCEDURE
Test SQL: SELECT 1 FROM SESSION WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'SESSION'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.179685] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:28
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_SelfHealer_SessionAnalytics_P')
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.189363] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:61
Status: SUCCESS
Original SQL: FROM REF_Entity e
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:18.200431] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:62
Status: FAILED
Original SQL: LEFT JOIN XRF_EntityAttributeValue eav_success ON e.ID = eav_success.EntityID
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.208162] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:63
Status: SUCCESS
Original SQL: LEFT JOIN REF_Attribute attr_success ON eav_success.AttributeID = attr_success.ID
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-17T23:59:18.213127] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:65
Status: SUCCESS
Original SQL: LEFT JOIN REF_EntityValue success_val ON eav_success.EntityValueID = success_val.ID
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:18.224131] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:67
Status: FAILED
Original SQL: LEFT JOIN XRF_EntityAttributeValue eav_duration ON e.ID = eav_duration.EntityID
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.227493] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:68
Status: SUCCESS
Original SQL: LEFT JOIN REF_Attribute attr_duration ON eav_duration.AttributeID = attr_duration.ID
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-17T23:59:18.238280] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:70
Status: SUCCESS
Original SQL: LEFT JOIN REF_EntityValue duration_val ON eav_duration.EntityValueID = duration_val.ID
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:18.243625] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:80
Status: FAILED
Original SQL: -- 2. UPDATE RECENT SESSIONS PROCEDURE
Test SQL: SELECT 1 FROM RECENT WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'RECENT'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.243625] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:86
Status: FAILED
Original SQL: IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_SelfHealer_RecentSessions_P')
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.260944] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:96
Status: FAILED
Original SQL: SELECT TOP (@Limit)
Test SQL: SELECT TOP (@Limit)
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable "@Limit". (137) (SQLExecDirectW)')
----------------------------------------

[2025-08-17T23:59:18.269114] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:125
Status: SUCCESS
Original SQL: FROM REF_Entity e
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:18.269114] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:126
Status: FAILED
Original SQL: LEFT JOIN XRF_EntityAttributeValue eav_duration ON e.ID = eav_duration.EntityID
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.285661] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:127
Status: SUCCESS
Original SQL: LEFT JOIN REF_Attribute attr_duration ON eav_duration.AttributeID = attr_duration.ID
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-17T23:59:18.285661] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:129
Status: SUCCESS
Original SQL: LEFT JOIN REF_EntityValue duration_val ON eav_duration.EntityValueID = duration_val.ID
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:18.305230] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:131
Status: FAILED
Original SQL: LEFT JOIN XRF_EntityAttributeValue eav_success ON e.ID = eav_success.EntityID
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.306231] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:132
Status: SUCCESS
Original SQL: LEFT JOIN REF_Attribute attr_success ON eav_success.AttributeID = attr_success.ID
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-17T23:59:18.314747] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql:134
Status: SUCCESS
Original SQL: LEFT JOIN REF_EntityValue success_val ON eav_success.EntityValueID = success_val.ID
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:18.333441] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\check_healer_status.py:9
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.339438] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\check_healer_status.py:14
Status: FAILED
Original SQL: from Self_Healer.core.healer_manager import SelfHealerManager
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.350498] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_error_criteria.py:9
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.354403] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_error_criteria.py:10
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.364863] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_error_criteria.py:29
Status: FAILED
Original SQL: from Self_Healer.core.error_monitor import ErrorMonitor
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.364863] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_self_healer.py:9
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.384024] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_self_healer.py:10
Status: FAILED
Original SQL: from datetime import datetime, timedelta
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.392470] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_self_healer.py:15
Status: FAILED
Original SQL: from Self_Healer.core.error_monitor import ErrorMonitor
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.398562] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_self_healer.py:16
Status: FAILED
Original SQL: from Self_Healer.core.healer_manager import SelfHealerManager
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.404595] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_self_healer_flow.py:8
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.417972] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_self_healer_flow.py:9
Status: FAILED
Original SQL: from datetime import datetime, timedelta
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.424016] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_self_healer_flow.py:18
Status: FAILED
Original SQL: from Self_Healer.core.error_monitor import ErrorMonitor
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.429738] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\debug_self_healer_flow.py:19
Status: FAILED
Original SQL: from Self_Healer.core.healer_manager import SelfHealerManager
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.442128] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\fix_healer_sync.py:11
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.442128] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\force_error_detection.py:9
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.456477] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\force_error_detection.py:10
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.466825] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\force_error_detection.py:19
Status: FAILED
Original SQL: from Self_Healer.core.error_monitor import ErrorMonitor
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.476918] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\tests\force_error_detection.py:135
Status: FAILED
Original SQL: print(f"\n   🔍 ISSUE: Error monitor is not detecting errors from log file")
Test SQL: SELECT 1 FROM log WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'log'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.487657] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_documentation.py:14
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.488567] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_documentation.py:22
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.504035] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_documentation.py:23
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.513188] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_documentation.py:24
Status: FAILED
Original SQL: from typing import List, Dict, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.520863] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_documentation.py:40
Status: FAILED
Original SQL: """Extract headers and bullet points from a markdown file"""
Test SQL: SELECT 1 FROM a WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.520863] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_naming_convention.py:15
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.536760] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_naming_convention.py:16
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.550225] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_naming_convention.py:17
Status: FAILED
Original SQL: from typing import Dict, List, Tuple, Any
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.550225] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_naming_convention.py:20
Status: FAILED
Original SQL: """Analyzes and migrates naming conventions from _public to _community."""
Test SQL: SELECT 1 FROM _public WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '_public'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.572158] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_project_files.py:14
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.583626] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_project_files.py:25
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.591846] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_project_files.py:26
Status: FAILED
Original SQL: from typing import Dict, List, Set, Tuple, Optional
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.591846] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_project_files.py:27
Status: FAILED
Original SQL: from collections import defaultdict
Test SQL: SELECT 1 FROM collections WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.611188] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_project_files.py:81
Status: FAILED
Original SQL: """Extract imports from Python file."""
Test SQL: SELECT 1 FROM Python WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Python'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.617068] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_project_files.py:794
Status: FAILED
Original SQL: md_content.append("- **Recovery:** You can restore files from Archive if needed")
Test SQL: SELECT 1 FROM Archive WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Archive'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.617068] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_project_files.py:800
Status: FAILED
Original SQL: md_content.append("If you need to restore files from the archive:")
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.633313] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_script_paths.py:5
Status: FAILED
Original SQL: after being moved from root to Scripts subfolder.
Test SQL: SELECT 1 FROM root WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'root'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.643255] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_script_paths.py:7
Status: FAILED
Original SQL: This script identifies patterns that suggest scripts are assuming they're running from root:
Test SQL: SELECT 1 FROM root WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'root'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.643255] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_script_paths.py:10
Status: FAILED
Original SQL: - Relative paths that would break when run from Scripts/
Test SQL: SELECT 1 FROM Scripts WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Scripts'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.658886] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_script_paths.py:16
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.673658] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_script_paths.py:17
Status: FAILED
Original SQL: from typing import Dict, List, Set, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.678727] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_script_paths.py:160
Status: FAILED
Original SQL: report.append("after being moved from root to Scripts subfolder.")
Test SQL: SELECT 1 FROM root WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'root'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.678727] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_script_paths.py:207
Status: FAILED
Original SQL: report.append("from pathlib import Path")
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.696975] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_venv_files.py:5
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.710601] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_venv_files.py:12
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.715607] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_venv_files.py:13
Status: FAILED
Original SQL: from collections import defaultdict
Test SQL: SELECT 1 FROM collections WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.725686] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_workspace_folders.py:11
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.739265] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_workspace_folders.py:19
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.751069] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_workspace_folders.py:20
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.760573] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\analyze_workspace_folders.py:21
Status: FAILED
Original SQL: from typing import Dict, List, Any
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.798943] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py:18
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.810322] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py:19
Status: FAILED
Original SQL: from typing import Dict, List, Set, Tuple, Optional
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.819865] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py:20
Status: FAILED
Original SQL: from dataclasses import dataclass
Test SQL: SELECT 1 FROM dataclasses WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.829136] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py:21
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.845378] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py:118
Status: FAILED
Original SQL: description="Fix SELECT * FROM spacing",
Test SQL: SELECT 1 FROM spacing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'spacing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.858339] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_script_paths.py:4
Status: FAILED
Original SQL: Automatically updates scripts in the Scripts folder to work properly from their new location
Test SQL: SELECT 1 FROM their WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'their'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.864769] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_script_paths.py:19
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.875614] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_script_paths.py:20
Status: FAILED
Original SQL: from typing import Dict, List, Set, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.875614] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_script_paths.py:21
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.906169] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_script_paths.py:32
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.916432] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:8
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.927827] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:13
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.936345] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:38
Status: FAILED
Original SQL: SELECT TABLE_NAME
Test SQL: SELECT TABLE_NAME
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'TABLE_NAME'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.944540] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:39
Status: FAILED
Original SQL: FROM INFORMATION_SCHEMA.TABLES
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.954048] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:77
Status: SUCCESS
Original SQL: test_query = "SELECT TOP 1 * FROM REF_Entity"
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:18.964626] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:87
Status: SUCCESS
Original SQL: test_query = "SELECT TOP 1 * FROM REF_Entity"
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:18.975322] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\clean_commit_messages.py:7
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.984419] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\clean_commit_messages.py:16
Status: FAILED
Original SQL: from typing import List, Dict, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:18.992477] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\clean_commit_messages.py:88
Status: FAILED
Original SQL: r'(?:update\s+gitignore\s+for\s+self[-_\s]?healer\s+and\s+knowledgebase)': 'update gitignore for private components',
Test SQL: SELECT 1 FROM gitignore WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'gitignore'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.014481] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\clean_commit_messages.py:93
Status: FAILED
Original SQL: r'update\s+to\s+full\s+version\s+of\s+self[-_\s]?healer': 'update to full system version',
Test SQL: SELECT 1 FROM to WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'to'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.027992] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\compare_readme_files.py:11
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.032051] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\compare_readme_files.py:20
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.048212] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\compare_readme_files.py:21
Status: FAILED
Original SQL: from typing import List, Dict, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.051907] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\comprehensive_repo_scan.py:11
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.064469] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\comprehensive_repo_scan.py:22
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.072865] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\comprehensive_repo_scan.py:23
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.085910] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\comprehensive_repo_scan.py:24
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple, Optional
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.085910] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\config_backup.py:1
Status: FAILED
Original SQL: from typing import Dict, Any, Optional
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.100878] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\config_backup.py:4
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.115841] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\config_backup.py:5
Status: FAILED
Original SQL: from pydantic import BaseModel
Test SQL: SELECT 1 FROM pydantic WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pydantic'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.127870] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\create_documentation_consolidation_plan.py:14
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.136721] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\create_documentation_consolidation_plan.py:22
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.169770] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\create_documentation_consolidation_plan.py:23
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.180862] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\create_documentation_consolidation_plan.py:24
Status: FAILED
Original SQL: from typing import Dict, List, Set, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.198890] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\create_documentation_consolidation_plan.py:67
Status: FAILED
Original SQL: """Identify redundancy patterns from the analysis report"""
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.220732] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:15
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.232304] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:16
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.241994] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:17
Status: FAILED
Original SQL: from typing import Dict, List, Optional, Any
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.251128] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:18
Status: FAILED
Original SQL: from dataclasses import dataclass, asdict
Test SQL: SELECT 1 FROM dataclasses WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.256346] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:145
Status: FAILED
Original SQL: """Add entry to in-memory storage and update log files."""
Test SQL: SELECT 1 FROM log WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'log'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.268479] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:262
Status: SUCCESS
Original SQL: "FROM REF_Entity", "FROM REF_Entity",
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:19.278296] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:268
Status: SUCCESS
Original SQL: "SELECT * FROM REF_Entity WHERE Name = 'test'",
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-17T23:59:19.278296] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:269
Status: SUCCESS
Original SQL: "SELECT 1 FROM REF_Entity WHERE Name = 'test'",
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-17T23:59:19.294182] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\debug_analyze_files.py:5
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.302751] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\debug_analyze_files.py:13
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.316514] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\delete_obsolete_files.py:13
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.323818] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\delete_obsolete_files.py:21
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.340590] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\delete_obsolete_files.py:22
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.346458] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\delete_obsolete_files.py:25
Status: FAILED
Original SQL: """Load the list of files to delete from the analysis"""
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.360559] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\delete_obsolete_files.py:140
Status: FAILED
Original SQL: f.write("2. Update cross-references in kept files\n")
Test SQL: SELECT 1 FROM cross WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'cross'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.367599] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\delete_obsolete_files.py:157
Status: FAILED
Original SQL: print(f"Loaded {len(files_to_delete)} files to delete from analysis.")
Test SQL: SELECT 1 FROM analysis WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'analysis'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.367599] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components.py:6
Status: FAILED
Original SQL: references that must be excluded from the public repository.
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.392747] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components.py:15
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.399313] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components.py:16
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.408259] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components.py:17
Status: FAILED
Original SQL: from typing import Dict, List, Any, Set
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.417959] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components.py:77
Status: FAILED
Original SQL: r"from Self_Healer",
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.429172] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components.py:129
Status: FAILED
Original SQL: """Check if directory should be excluded from scanning."""
Test SQL: SELECT 1 FROM scanning WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'scanning'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.433178] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components2.py:6
Status: FAILED
Original SQL: references that must be excluded from the public repository.
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.451942] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components2.py:15
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.465156] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components2.py:16
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.487798] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components2.py:17
Status: FAILED
Original SQL: from typing import Dict, List, Any, Set
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.519331] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components2.py:77
Status: FAILED
Original SQL: r"from Self_Healer",
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.541488] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\detect_private_components2.py:129
Status: FAILED
Original SQL: """Check if directory should be excluded from scanning."""
Test SQL: SELECT 1 FROM scanning WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'scanning'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.553977] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\dev_publish.py:5
Status: FAILED
Original SQL: Streamlined workflow for developers to update public GitHub repository.
Test SQL: SELECT 1 FROM public WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'public'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.564797] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\dev_publish.py:10
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.571509] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\dev_publish.py:20
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.581699] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\dev_publish.py:21
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.598770] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\dev_publish.py:22
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.611193] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\dev_publish.py:28
Status: FAILED
Original SQL: self.commit_message = commit_message or f"Update N8N_Builder Community Edition - {datetime.now().strftime('%Y-%m-%d')}"
Test SQL: SELECT 1 FROM N8N_Builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'N8N_Builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.702000] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py:17
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.711310] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py:18
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.716828] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py:19
Status: FAILED
Original SQL: from typing import Dict, List, Set, Tuple, Optional
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.729303] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py:24
Status: FAILED
Original SQL: from Scripts.database_repair_logger import DatabaseRepairLogger
Test SQL: SELECT 1 FROM Scripts WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Scripts'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.741048] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\enhanced_database_fixer.py:25
Status: FAILED
Original SQL: from Scripts.sql_validator import SQLValidator
Test SQL: SELECT 1 FROM Scripts WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Scripts'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.741048] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\execute_separation.py:10
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.760491] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\execute_separation.py:21
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.770841] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\execute_separation.py:22
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.786475] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\execute_separation.py:23
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.798132] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\final_documentation_validation.py:16
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.811874] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\final_documentation_validation.py:24
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.815732] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\final_documentation_validation.py:25
Status: FAILED
Original SQL: from typing import Dict, List, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.836379] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\final_documentation_validation.py:26
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.861943] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\final_documentation_validation.py:292
Status: FAILED
Original SQL: print("  4. Get feedback from a new developer")
Test SQL: SELECT 1 FROM a WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.876534] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_analyze_files.py:6
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.890622] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_analyze_files.py:14
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.896107] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_analyze_files.py:75
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.907331] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_analyze_files.py:76
Status: FAILED
Original SQL: from collections import defaultdict
Test SQL: SELECT 1 FROM collections WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.917336] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_analyze_files.py:290
Status: FAILED
Original SQL: print("   may have been compromised and should be restored from backup")
Test SQL: SELECT 1 FROM backup WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'backup'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.928309] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_healer_logging.py:11
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.937813] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_script_references.py:7
Status: FAILED
Original SQL: when called from within the Scripts/ folder itself.
Test SQL: SELECT 1 FROM within WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'within'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.947857] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_script_references.py:12
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.959125] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_script_references.py:21
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.970243] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_script_references.py:22
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.970243] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\fix_script_references.py:23
Status: FAILED
Original SQL: from typing import List, Dict, Tuple, Set
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:19.990028] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\generate_consolidation_report.py:12
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.005531] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\generate_consolidation_report.py:19
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.017621] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\generate_consolidation_report.py:20
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.024545] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\generate_consolidation_report.py:23
Status: FAILED
Original SQL: """Generate a markdown report from the JSON consolidation plan"""
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.036500] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\generate_consolidation_report.py:121
Status: FAILED
Original SQL: f.write("2. **Update cross-references** - Fix broken links\n")
Test SQL: SELECT 1 FROM cross WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'cross'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.048907] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\generate_consolidation_report.py:158
Status: FAILED
Original SQL: f.write("*This report was generated automatically from the documentation analysis.*\n")
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.048907] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\generate_process_flow.py:3
Status: FAILED
Original SQL: from collections import defaultdict
Test SQL: SELECT 1 FROM collections WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.072012] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\github_repository_setup.py:10
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.086012] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\github_repository_setup.py:19
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.089970] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\github_repository_setup.py:20
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.114574] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\github_repository_setup.py:21
Status: FAILED
Original SQL: from typing import Dict, List, Any
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.117446] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\github_repository_setup.py:368
Status: FAILED
Original SQL: - Update README.md if needed
Test SQL: SELECT 1 FROM README WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'README'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.133739] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\github_repository_setup.py:370
Status: FAILED
Original SQL: - Update API documentation
Test SQL: SELECT 1 FROM API WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'API'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.152028] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\improve_documentation.py:6
Status: FAILED
Original SQL: 1. Updates tunnel references from ngrok to LocalTunnel
Test SQL: SELECT 1 FROM ngrok WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'ngrok'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.162016] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\improve_documentation.py:17
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.173046] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\improve_documentation.py:25
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.178292] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\improve_documentation.py:26
Status: FAILED
Original SQL: from typing import Dict, List, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.194192] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\improve_documentation.py:27
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.206787] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\improve_documentation.py:35
Status: FAILED
Original SQL: """Update all ngrok references to LocalTunnel"""
Test SQL: SELECT 1 FROM all WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'all'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.222414] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\improve_documentation.py:36
Status: FAILED
Original SQL: print("🔄 Updating tunnel references from ngrok to LocalTunnel...")
Test SQL: SELECT 1 FROM ngrok WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'ngrok'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.231260] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\improve_documentation.py:251
Status: FAILED
Original SQL: """Update GETTING_STARTED.md with proper LocalTunnel instructions"""
Test SQL: SELECT 1 FROM GETTING_STARTED WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'GETTING_STARTED'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.246713] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py:5
Status: FAILED
Original SQL: Executes the migration from _public to _community naming convention.
Test SQL: SELECT 1 FROM _public WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '_public'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.252141] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py:6
Status: FAILED
Original SQL: Based on analysis from analyze_naming_convention.py.
Test SQL: SELECT 1 FROM analyze_naming_convention WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'analyze_naming_convention'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.267301] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py:11
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.273591] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py:21
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.286638] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py:22
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.300446] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py:23
Status: FAILED
Original SQL: from typing import Dict, List
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.300446] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py:26
Status: FAILED
Original SQL: """Executes naming convention migration from _public to _community."""
Test SQL: SELECT 1 FROM _public WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '_public'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.325579] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py:117
Status: FAILED
Original SQL: """Update sync scripts to use _community files instead of _public."""
Test SQL: SELECT 1 FROM sync WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sync'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.338790] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\migrate_naming_convention.py:193
Status: FAILED
Original SQL: parser = argparse.ArgumentParser(description="Migrate naming convention from _public to _community")
Test SQL: SELECT 1 FROM _public WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '_public'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.345100] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\prepare_public_release.py:10
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.361553] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\prepare_public_release.py:21
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.367430] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\prepare_public_release.py:22
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.378922] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\prepare_public_release.py:23
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.392294] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\prepare_public_release.py:226
Status: FAILED
Original SQL: - **Contributing**: We welcome contributions from the community!
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.392294] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\pre_commit_cleanup.py:6
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.409775] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\pre_commit_cleanup.py:14
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.429071] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\pre_commit_cleanup.py:17
Status: FAILED
Original SQL: from typing import List, Set
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.438586] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\pre_commit_cleanup.py:267
Status: FAILED
Original SQL: '*.pyc', '*.pyo'  # Removed *.pyd from here!
Test SQL: SELECT 1 FROM here WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'here'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.449789] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\pre_execution_verification.py:10
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.469540] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\pre_execution_verification.py:20
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.472007] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\pre_execution_verification.py:21
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.491717] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\pre_execution_verification.py:22
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.500720] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\project_cleanup_manager.py:6
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.517062] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\project_cleanup_manager.py:16
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.524113] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\project_cleanup_manager.py:17
Status: FAILED
Original SQL: from typing import Dict, List, Set, Tuple, Optional
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.539868] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\project_cleanup_manager.py:18
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.553786] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\quick_test_research.py:8
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.565344] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\quick_test_research.py:13
Status: FAILED
Original SQL: from n8n_builder.mcp_research_tool import N8NResearchTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.571781] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\restart_n8n_clean.py:11
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.581809] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\safe_analyze_project_files.py:8
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.604229] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\safe_analyze_project_files.py:9
Status: FAILED
Original SQL: from collections import defaultdict
Test SQL: SELECT 1 FROM collections WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.618066] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\safe_cleanup.py:4
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.621593] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sanitize_documentation.py:5
Status: FAILED
Original SQL: Removes all private component references from documentation files
Test SQL: SELECT 1 FROM documentation WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'documentation'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.641535] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sanitize_documentation.py:11
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.648045] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sanitize_documentation.py:22
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.659204] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sanitize_documentation.py:23
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.675128] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sanitize_documentation.py:24
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.687097] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\scan_md_for_private_refs.py:7
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.690786] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\scan_md_for_private_refs.py:16
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.710440] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\scan_md_for_private_refs.py:17
Status: FAILED
Original SQL: from typing import List, Dict, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.718427] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\setup_log_rotation.py:6
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.729351] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\setup_log_rotation.py:15
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.741784] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\setup_log_rotation.py:16
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.754216] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\setup_log_rotation.py:21
Status: FAILED
Original SQL: from n8n_builder.log_rotation_manager import LogRotationManager, setup_24hour_log_rotation
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.769223] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\setup_log_rotation.py:132
Status: FAILED
Original SQL: """Remove existing file handlers from a logger to avoid duplicates."""
Test SQL: SELECT 1 FROM a WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.769223] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\setup_log_rotation.py:154
Status: FAILED
Original SQL: ('n8n_builder', 'INFO', 'Test message from N8N Builder'),
Test SQL: SELECT 1 FROM N8N WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'N8N'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.791907] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\setup_log_rotation.py:155
Status: FAILED
Original SQL: ('self_healer', 'INFO', 'Test message from Self-Healer'),
Test SQL: SELECT 1 FROM Self WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.802782] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\shutdown.py:7
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.812554] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\shutdown.py:20
Status: FAILED
Original SQL: from typing import List
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.829028] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\shutdown.py:134
Status: FAILED
Original SQL: from n8n_builder.knowledge_cache import EnhancedKnowledgeCache
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.843672] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_database_fixer.py:11
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.854131] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_database_fixer.py:12
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.922844] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py:8
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.925150] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:16
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.941709] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:17
Status: FAILED
Original SQL: from typing import Dict, List, Tuple, Optional
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.955312] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:18
Status: FAILED
Original SQL: from dataclasses import dataclass
Test SQL: SELECT 1 FROM dataclasses WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.967470] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:23
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.987212] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:24
Status: FAILED
Original SQL: from Scripts.database_repair_logger import DatabaseRepairLogger
Test SQL: SELECT 1 FROM Scripts WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Scripts'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:20.987212] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:49
Status: FAILED
Original SQL: """Extract tables and columns from SQL statement."""
Test SQL: SELECT 1 FROM SQL WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'SQL'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.002856] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:70
Status: FAILED
Original SQL: select_match = re.search(r'\bSELECT\s+(.+?)\s+FROM', sql, re.IGNORECASE | re.DOTALL)
Test SQL: select_match = re.search(r'\bSELECT\s+(.+?)\s+FROM', sql, re.IGNORECASE | re.DOTALL)
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '='. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.028814] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:72
Status: FAILED
Original SQL: select_part = select_match.group(1)
Test SQL: select_part = select_match.group(1)
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '='. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.032488] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:98
Status: FAILED
Original SQL: select_match = re.match(r'(SELECT\s+)(.+?)(\s+FROM.+)', sql_clean, re.IGNORECASE | re.DOTALL)
Test SQL: select_match = re.match(r'(SELECT\s+)(.+?)(\s+FROM.+)', sql_clean, re.IGNORECASE | re.DOTALL)
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '='. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.053007] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\streamlined_documentation_cleanup.py:15
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.053007] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\streamlined_documentation_cleanup.py:23
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.073881] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\streamlined_documentation_cleanup.py:24
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.093007] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\streamlined_documentation_cleanup.py:25
Status: FAILED
Original SQL: from typing import List, Dict, Set
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.096013] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\streamlined_documentation_cleanup.py:227
Status: FAILED
Original SQL: f.write("5. **Update cross-references** in kept files\n\n")
Test SQL: SELECT 1 FROM cross WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'cross'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.116335] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_detection_simple.py:4
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.122519] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_detection_simple.py:11
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.143285] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_enhanced_sync.py:10
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.148865] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_enhanced_sync.py:22
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.165940] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_enhanced_sync.py:23
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.175224] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_enhanced_sync.py:24
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.191659] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_llm_connection.py:10
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.209387] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_llm_connection.py:33
Status: FAILED
Original SQL: "content": "Create a simple JSON object with 'message': 'Hello from N8N Builder test'"
Test SQL: SELECT 1 FROM N8N WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'N8N'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.218200] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_llm_connection.py:76
Status: FAILED
Original SQL: from n8n_builder.n8n_builder import N8NBuilder
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.232169] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_logging.py:3
Status: FAILED
Original SQL: from n8n_builder.logging_config import validation_logger, retry_logger, diff_logger, error_logger
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.248159] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_mcp_research.py:10
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.259811] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_mcp_research.py:15
Status: FAILED
Original SQL: from n8n_builder.mcp_research_tool import N8NResearchTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.270447] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_mcp_research.py:16
Status: FAILED
Original SQL: from n8n_builder.config import config
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.282029] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_safe_cleanup.py:6
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.346420] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_safe_cleanup.py:14
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.361618] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_safe_cleanup.py:21
Status: FAILED
Original SQL: from Scripts.pre_commit_cleanup import PreCommitCleanup
Test SQL: SELECT 1 FROM Scripts WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Scripts'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.374187] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py:8
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.379725] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py:13
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.415011] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py:37
Status: FAILED
Original SQL: SELECT TABLE_NAME
Test SQL: SELECT TABLE_NAME
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'TABLE_NAME'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.461929] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py:38
Status: FAILED
Original SQL: FROM INFORMATION_SCHEMA.TABLES
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.477961] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_self_healer_real_world.py:6
Status: FAILED
Original SQL: workflow from detection to resolution, providing comprehensive validation
Test SQL: SELECT 1 FROM detection WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'detection'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.490384] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_self_healer_real_world.py:15
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.493388] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_self_healer_real_world.py:16
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.565954] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_self_healer_real_world.py:54
Status: FAILED
Original SQL: """Restore the original config file from backup."""
Test SQL: SELECT 1 FROM backup WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'backup'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.579909] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_self_healer_real_world.py:58
Status: FAILED
Original SQL: print(f"✅ Config restored from backup")
Test SQL: SELECT 1 FROM backup WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'backup'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.610415] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_self_healer_real_world.py:100
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.625178] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_verification_systems.py:10
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.633174] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_verification_systems.py:20
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.643361] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_verification_systems.py:21
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.656932] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_verification_systems.py:22
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.674014] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_verification_systems.py:153
Status: FAILED
Original SQL: from Self_Healer.core.healer_manager import SelfHealerManager
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.674014] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_verification_systems.py:154
Status: FAILED
Original SQL: from Self_Healer.core.knowledge_database_wrapper import SelfHealerKnowledgeDB
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.700171] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_workflow_generator.py:10
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.713874] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_workflow_generator.py:15
Status: FAILED
Original SQL: from n8n_builder.config import config
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.719766] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_workflow_generator.py:77
Status: FAILED
Original SQL: from n8n_builder.n8n_builder import N8NBuilder
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.739839] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_workflow_generator.py:135
Status: FAILED
Original SQL: from n8n_builder.mcp_research_tool import N8NResearchTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.755629] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py:3
Status: FAILED
Original SQL: Folder Reference Update Script
Test SQL: SELECT 1 FROM Script WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Script'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.769300] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py:11
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.773351] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py:20
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.794305] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py:21
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.801734] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py:22
Status: FAILED
Original SQL: from typing import List, Dict, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.822004] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py:47
Status: FAILED
Original SQL: """Log update action."""
Test SQL: SELECT 1 FROM action WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'action'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.835746] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py:97
Status: FAILED
Original SQL: """Update references in a file."""
Test SQL: SELECT 1 FROM references WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'references'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.836202] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py:203
Status: FAILED
Original SQL: """Run complete reference update process."""
Test SQL: SELECT 1 FROM process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.860421] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py:220
Status: FAILED
Original SQL: self.log_action(status, "Reference update completed")
Test SQL: SELECT 1 FROM completed WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'completed'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.866776] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py:225
Status: FAILED
Original SQL: """Save update log."""
Test SQL: SELECT 1 FROM log WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'log'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.879223] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\update_folder_references.py:232
Status: FAILED
Original SQL: parser = argparse.ArgumentParser(description="Update folder references from N8N_Builder_Community to N8N_Builder_Community")
Test SQL: SELECT 1 FROM folder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'folder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.893576] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:18
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.904150] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:19
Status: FAILED
Original SQL: from typing import Dict, List, Set, Tuple, Optional
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.924158] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:20
Status: FAILED
Original SQL: from dataclasses import dataclass
Test SQL: SELECT 1 FROM dataclasses WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dataclasses'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.932781] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:26
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.944871] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:112
Status: FAILED
Original SQL: SELECT TABLE_NAME
Test SQL: SELECT TABLE_NAME
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'TABLE_NAME'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.965826] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:113
Status: FAILED
Original SQL: FROM INFORMATION_SCHEMA.TABLES
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:21.984862] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:132
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE
Test SQL: SELECT COLUMN_NAME, DATA_TYPE
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'COLUMN_NAME'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'DATA_TYPE'. (207)")
----------------------------------------

[2025-08-17T23:59:21.991008] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:133
Status: FAILED
Original SQL: FROM INFORMATION_SCHEMA.COLUMNS
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.005468] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:181
Status: FAILED
Original SQL: """Extract table names from a SQL line."""
Test SQL: SELECT 1 FROM a WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.018559] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:203
Status: FAILED
Original SQL: """Extract column names from a SQL line."""
Test SQL: SELECT 1 FROM a WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.047989] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_links.py:13
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.057735] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_links.py:21
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.072225] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_links.py:22
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.081828] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_links.py:53
Status: FAILED
Original SQL: """Extract all markdown links from a file"""
Test SQL: SELECT 1 FROM a WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.095049] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py:17
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.116007] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py:26
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.120511] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py:27
Status: FAILED
Original SQL: from typing import Dict, List, Tuple, Any
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.145100] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py:28
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.156330] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py:82
Status: FAILED
Original SQL: """Extract all headers from markdown content"""
Test SQL: SELECT 1 FROM markdown WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'markdown'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.171385] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py:91
Status: FAILED
Original SQL: """Extract all links from markdown content"""
Test SQL: SELECT 1 FROM markdown WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'markdown'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.178853] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py:286
Status: FAILED
Original SQL: analysis["recommendations"].append("Update to prefer LocalTunnel over ngrok")
Test SQL: SELECT 1 FROM to WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'to'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.192116] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py:335
Status: FAILED
Original SQL: """Generate summary statistics from all analyses"""
Test SQL: SELECT 1 FROM all WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'all'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.204733] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_documentation_quality.py:394
Status: FAILED
Original SQL: "action": "Update tunnel documentation to prefer LocalTunnel",
Test SQL: SELECT 1 FROM tunnel WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'tunnel'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.225483] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:2
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.241593] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:17
Status: FAILED
Original SQL: FROM sys.procedures
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.259362] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:52
Status: FAILED
Original SQL: SELECT TOP (@Limit)
Test SQL: SELECT TOP (@Limit)
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable "@Limit". (137) (SQLExecDirectW)')
----------------------------------------

[2025-08-17T23:59:22.272885] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:60
Status: SUCCESS
Original SQL: FROM REF_Entity e
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:22.286564] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:84
Status: SUCCESS
Original SQL: SELECT name FROM sys.procedures
Test SQL: SELECT 1 FROM sys.procedures
Result: SUCCESS - Tables: sys, Columns: name
----------------------------------------

[2025-08-17T23:59:22.299111] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\verification_pipeline.py:10
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.318706] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\verification_pipeline.py:20
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.331616] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\verification_pipeline.py:21
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.342687] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\verification_pipeline.py:22
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.357058] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_documentation.py:16
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.372546] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_documentation.py:17
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.380395] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_documentation.py:18
Status: FAILED
Original SQL: from typing import List, Dict, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.394065] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_documentation.py:34
Status: FAILED
Original SQL: """Extract headers and bullet points from a markdown file"""
Test SQL: SELECT 1 FROM a WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.413912] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_project_files.py:19
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.424696] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_project_files.py:20
Status: FAILED
Original SQL: from typing import Dict, List, Set, Tuple, Optional
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.440320] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_project_files.py:21
Status: FAILED
Original SQL: from collections import defaultdict
Test SQL: SELECT 1 FROM collections WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.459507] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_project_files.py:75
Status: FAILED
Original SQL: """Extract imports from Python file."""
Test SQL: SELECT 1 FROM Python WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Python'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.466302] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_project_files.py:788
Status: FAILED
Original SQL: md_content.append("- **Recovery:** You can restore files from Archive if needed")
Test SQL: SELECT 1 FROM Archive WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Archive'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.491393] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_project_files.py:794
Status: FAILED
Original SQL: md_content.append("If you need to restore files from the archive:")
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.508079] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_script_paths.py:5
Status: FAILED
Original SQL: after being moved from root to Scripts subfolder.
Test SQL: SELECT 1 FROM root WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'root'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.520917] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_script_paths.py:7
Status: FAILED
Original SQL: This script identifies patterns that suggest scripts are assuming they're running from root:
Test SQL: SELECT 1 FROM root WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'root'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.526283] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_script_paths.py:10
Status: FAILED
Original SQL: - Relative paths that would break when run from Scripts/
Test SQL: SELECT 1 FROM Scripts WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Scripts'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.546986] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_script_paths.py:16
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.560797] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_script_paths.py:17
Status: FAILED
Original SQL: from typing import Dict, List, Set, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.569575] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_script_paths.py:160
Status: FAILED
Original SQL: report.append("after being moved from root to Scripts subfolder.")
Test SQL: SELECT 1 FROM root WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'root'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.592770] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_script_paths.py:207
Status: FAILED
Original SQL: report.append("from pathlib import Path")
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.606302] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_venv_files.py:6
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.616690] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_venv_files.py:7
Status: FAILED
Original SQL: from collections import defaultdict
Test SQL: SELECT 1 FROM collections WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.627519] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_workspace_folders.py:13
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.648327] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_workspace_folders.py:14
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.657197] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\analyze_workspace_folders.py:15
Status: FAILED
Original SQL: from typing import Dict, List, Any
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.677668] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\clean_commit_messages.py:10
Status: FAILED
Original SQL: from typing import List, Dict, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.679666] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\clean_commit_messages.py:82
Status: FAILED
Original SQL: r'(?:update\s+gitignore\s+for\s+self[-_\s]?healer\s+and\s+knowledgebase)': 'update gitignore for private components',
Test SQL: SELECT 1 FROM gitignore WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'gitignore'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.705280] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\clean_commit_messages.py:87
Status: FAILED
Original SQL: r'update\s+to\s+full\s+version\s+of\s+self[-_\s]?healer': 'update to full system version',
Test SQL: SELECT 1 FROM to WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'to'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.714849] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\compare_readme_files.py:14
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.727353] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\compare_readme_files.py:15
Status: FAILED
Original SQL: from typing import List, Dict, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.747591] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\comprehensive_repo_scan.py:16
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.761703] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\comprehensive_repo_scan.py:17
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.768516] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\comprehensive_repo_scan.py:18
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple, Optional
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.783199] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\create_documentation_consolidation_plan.py:16
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.805861] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\create_documentation_consolidation_plan.py:17
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.813676] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\create_documentation_consolidation_plan.py:18
Status: FAILED
Original SQL: from typing import Dict, List, Set, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.843173] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\create_documentation_consolidation_plan.py:61
Status: FAILED
Original SQL: """Identify redundancy patterns from the analysis report"""
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.860483] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\debug_analyze_files.py:7
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.867491] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\delete_obsolete_files.py:15
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.880208] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\delete_obsolete_files.py:16
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.896388] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\delete_obsolete_files.py:19
Status: FAILED
Original SQL: """Load the list of files to delete from the analysis"""
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.910402] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\delete_obsolete_files.py:134
Status: FAILED
Original SQL: f.write("2. Update cross-references in kept files\n")
Test SQL: SELECT 1 FROM cross WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'cross'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.928098] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\delete_obsolete_files.py:151
Status: FAILED
Original SQL: print(f"Loaded {len(files_to_delete)} files to delete from analysis.")
Test SQL: SELECT 1 FROM analysis WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'analysis'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.954510] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\dev_publish.py:5
Status: FAILED
Original SQL: Streamlined workflow for developers to update public GitHub repository.
Test SQL: SELECT 1 FROM public WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'public'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.969004] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\dev_publish.py:14
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.982635] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\dev_publish.py:15
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:22.988340] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\dev_publish.py:16
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.015355] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\dev_publish.py:22
Status: FAILED
Original SQL: self.commit_message = commit_message or f"Update N8N_Builder Community Edition - {datetime.now().strftime('%Y-%m-%d')}"
Test SQL: SELECT 1 FROM N8N_Builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'N8N_Builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.019661] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\execute_separation.py:15
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.034940] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\execute_separation.py:16
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.052298] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\execute_separation.py:17
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.069900] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\final_documentation_validation.py:18
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.082858] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\final_documentation_validation.py:19
Status: FAILED
Original SQL: from typing import Dict, List, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.097567] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\final_documentation_validation.py:20
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.112329] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\final_documentation_validation.py:286
Status: FAILED
Original SQL: print("  4. Get feedback from a new developer")
Test SQL: SELECT 1 FROM a WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.128846] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\fix_analyze_files.py:8
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.148384] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\fix_analyze_files.py:69
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.167228] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\fix_analyze_files.py:70
Status: FAILED
Original SQL: from collections import defaultdict
Test SQL: SELECT 1 FROM collections WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'collections'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.174014] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\fix_analyze_files.py:284
Status: FAILED
Original SQL: print("   may have been compromised and should be restored from backup")
Test SQL: SELECT 1 FROM backup WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'backup'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.195566] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\fix_script_references.py:7
Status: FAILED
Original SQL: when called from within the Scripts/ folder itself.
Test SQL: SELECT 1 FROM within WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'within'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.213011] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\fix_script_references.py:15
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.220103] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\fix_script_references.py:16
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.242098] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\fix_script_references.py:17
Status: FAILED
Original SQL: from typing import List, Dict, Tuple, Set
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.248048] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\generate_consolidation_report.py:13
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.270234] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\generate_consolidation_report.py:14
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.276599] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\generate_consolidation_report.py:17
Status: FAILED
Original SQL: """Generate a markdown report from the JSON consolidation plan"""
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.297645] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\generate_consolidation_report.py:115
Status: FAILED
Original SQL: f.write("2. **Update cross-references** - Fix broken links\n")
Test SQL: SELECT 1 FROM cross WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'cross'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.332055] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\generate_consolidation_report.py:152
Status: FAILED
Original SQL: f.write("*This report was generated automatically from the documentation analysis.*\n")
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.347347] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\github_repository_setup.py:13
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.367432] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\github_repository_setup.py:14
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.376495] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\github_repository_setup.py:15
Status: FAILED
Original SQL: from typing import Dict, List, Any
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.397663] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\github_repository_setup.py:362
Status: FAILED
Original SQL: - Update README.md if needed
Test SQL: SELECT 1 FROM README WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'README'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.404205] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\github_repository_setup.py:364
Status: FAILED
Original SQL: - Update API documentation
Test SQL: SELECT 1 FROM API WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'API'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.426320] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\improve_documentation.py:6
Status: FAILED
Original SQL: 1. Updates tunnel references from ngrok to LocalTunnel
Test SQL: SELECT 1 FROM ngrok WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'ngrok'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.435084] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\improve_documentation.py:19
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.454925] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\improve_documentation.py:20
Status: FAILED
Original SQL: from typing import Dict, List, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.467418] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\improve_documentation.py:21
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.477788] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\improve_documentation.py:29
Status: FAILED
Original SQL: """Update all ngrok references to LocalTunnel"""
Test SQL: SELECT 1 FROM all WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'all'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.489845] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\improve_documentation.py:30
Status: FAILED
Original SQL: print("🔄 Updating tunnel references from ngrok to LocalTunnel...")
Test SQL: SELECT 1 FROM ngrok WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'ngrok'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.514653] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\improve_documentation.py:245
Status: FAILED
Original SQL: """Update GETTING_STARTED.md with proper LocalTunnel instructions"""
Test SQL: SELECT 1 FROM GETTING_STARTED WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'GETTING_STARTED'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.523219] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\migrate_naming_convention.py:5
Status: FAILED
Original SQL: Executes the migration from _public to _community naming convention.
Test SQL: SELECT 1 FROM _public WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '_public'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.540316] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\migrate_naming_convention.py:6
Status: FAILED
Original SQL: Based on analysis from analyze_naming_convention.py.
Test SQL: SELECT 1 FROM analyze_naming_convention WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'analyze_naming_convention'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.551445] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\migrate_naming_convention.py:15
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.567482] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\migrate_naming_convention.py:16
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.587602] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\migrate_naming_convention.py:17
Status: FAILED
Original SQL: from typing import Dict, List
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.598757] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\migrate_naming_convention.py:20
Status: FAILED
Original SQL: """Executes naming convention migration from _public to _community."""
Test SQL: SELECT 1 FROM _public WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '_public'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.618207] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\migrate_naming_convention.py:111
Status: FAILED
Original SQL: """Update sync scripts to use _community files instead of _public."""
Test SQL: SELECT 1 FROM sync WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sync'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.636506] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\migrate_naming_convention.py:187
Status: FAILED
Original SQL: parser = argparse.ArgumentParser(description="Migrate naming convention from _public to _community")
Test SQL: SELECT 1 FROM _public WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '_public'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.653083] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\prepare_public_release.py:15
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.670139] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\prepare_public_release.py:16
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.685189] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\prepare_public_release.py:17
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.702658] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\prepare_public_release.py:220
Status: FAILED
Original SQL: - **Contributing**: We welcome contributions from the community!
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.708793] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\pre_commit_cleanup.py:8
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.728402] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\pre_commit_cleanup.py:11
Status: FAILED
Original SQL: from typing import List, Set
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.746320] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\pre_commit_cleanup.py:261
Status: FAILED
Original SQL: '*.pyc', '*.pyo'  # Removed *.pyd from here!
Test SQL: SELECT 1 FROM here WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'here'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.753686] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\pre_execution_verification.py:14
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.767536] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\pre_execution_verification.py:15
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.795913] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\pre_execution_verification.py:16
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.807078] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\project_cleanup_manager.py:10
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.825066] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\project_cleanup_manager.py:11
Status: FAILED
Original SQL: from typing import Dict, List, Set, Tuple, Optional
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.840208] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\project_cleanup_manager.py:12
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.860429] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\sanitize_documentation.py:5
Status: FAILED
Original SQL: Removes all private component references from documentation files
Test SQL: SELECT 1 FROM documentation WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'documentation'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.867337] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\sanitize_documentation.py:16
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.891756] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\sanitize_documentation.py:17
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.903353] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\sanitize_documentation.py:18
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.923479] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\scan_md_for_private_refs.py:10
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.940580] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\scan_md_for_private_refs.py:11
Status: FAILED
Original SQL: from typing import List, Dict, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.955517] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\setup_log_rotation.py:9
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.973215] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\setup_log_rotation.py:10
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:23.988993] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\setup_log_rotation.py:15
Status: FAILED
Original SQL: from n8n_builder.log_rotation_manager import LogRotationManager, setup_24hour_log_rotation
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.007691] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\setup_log_rotation.py:126
Status: FAILED
Original SQL: """Remove existing file handlers from a logger to avoid duplicates."""
Test SQL: SELECT 1 FROM a WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.022443] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\setup_log_rotation.py:148
Status: FAILED
Original SQL: ('n8n_builder', 'INFO', 'Test message from N8N Builder'),
Test SQL: SELECT 1 FROM N8N WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'N8N'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.037941] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\setup_log_rotation.py:149
Status: FAILED
Original SQL: ('self_healer', 'INFO', 'Test message from Self-Healer'),
Test SQL: SELECT 1 FROM Self WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.061566] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\shutdown.py:14
Status: FAILED
Original SQL: from typing import List
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.080317] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\shutdown.py:128
Status: FAILED
Original SQL: from n8n_builder.knowledge_cache import EnhancedKnowledgeCache
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.083448] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\streamlined_documentation_cleanup.py:17
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.111527] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\streamlined_documentation_cleanup.py:18
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.123826] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\streamlined_documentation_cleanup.py:19
Status: FAILED
Original SQL: from typing import List, Dict, Set
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.139677] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\streamlined_documentation_cleanup.py:221
Status: FAILED
Original SQL: f.write("5. **Update cross-references** in kept files\n\n")
Test SQL: SELECT 1 FROM cross WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'cross'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.158654] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_detection_simple.py:5
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.175373] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_enhanced_sync.py:16
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.182595] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_enhanced_sync.py:17
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.229631] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_enhanced_sync.py:18
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.249235] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_safe_cleanup.py:8
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.266268] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_safe_cleanup.py:15
Status: FAILED
Original SQL: from Scripts.pre_commit_cleanup import PreCommitCleanup
Test SQL: SELECT 1 FROM Scripts WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Scripts'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.279900] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_verification_systems.py:14
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.298518] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_verification_systems.py:15
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.305673] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_verification_systems.py:16
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.331045] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_verification_systems.py:147
Status: FAILED
Original SQL: from Self_Healer.core.healer_manager import SelfHealerManager
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.348911] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\test_verification_systems.py:148
Status: FAILED
Original SQL: from Self_Healer.core.knowledge_database_wrapper import SelfHealerKnowledgeDB
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.365743] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py:3
Status: FAILED
Original SQL: Folder Reference Update Script
Test SQL: SELECT 1 FROM Script WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Script'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.374451] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py:14
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.393689] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py:15
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.403137] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py:16
Status: FAILED
Original SQL: from typing import List, Dict, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.425307] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py:41
Status: FAILED
Original SQL: """Log update action."""
Test SQL: SELECT 1 FROM action WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'action'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.442111] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py:91
Status: FAILED
Original SQL: """Update references in a file."""
Test SQL: SELECT 1 FROM references WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'references'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.458084] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py:197
Status: FAILED
Original SQL: """Run complete reference update process."""
Test SQL: SELECT 1 FROM process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.467623] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py:214
Status: FAILED
Original SQL: self.log_action(status, "Reference update completed")
Test SQL: SELECT 1 FROM completed WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'completed'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.489481] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py:219
Status: FAILED
Original SQL: """Save update log."""
Test SQL: SELECT 1 FROM log WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'log'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.507428] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\update_folder_references.py:226
Status: FAILED
Original SQL: parser = argparse.ArgumentParser(description="Update folder references from N8N_Builder_Community to N8N_Builder_Community")
Test SQL: SELECT 1 FROM folder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'folder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.523728] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_links.py:15
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.524254] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_links.py:16
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.555943] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_links.py:47
Status: FAILED
Original SQL: """Extract all markdown links from a file"""
Test SQL: SELECT 1 FROM a WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.571345] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_quality.py:20
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.586609] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_quality.py:21
Status: FAILED
Original SQL: from typing import Dict, List, Tuple, Any
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.607227] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_quality.py:22
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.622026] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_quality.py:76
Status: FAILED
Original SQL: """Extract all headers from markdown content"""
Test SQL: SELECT 1 FROM markdown WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'markdown'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.634889] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_quality.py:85
Status: FAILED
Original SQL: """Extract all links from markdown content"""
Test SQL: SELECT 1 FROM markdown WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'markdown'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.652808] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_quality.py:280
Status: FAILED
Original SQL: analysis["recommendations"].append("Update to prefer LocalTunnel over ngrok")
Test SQL: SELECT 1 FROM to WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'to'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.667764] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_quality.py:329
Status: FAILED
Original SQL: """Generate summary statistics from all analyses"""
Test SQL: SELECT 1 FROM all WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'all'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.678474] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\validate_documentation_quality.py:388
Status: FAILED
Original SQL: "action": "Update tunnel documentation to prefer LocalTunnel",
Test SQL: SELECT 1 FROM tunnel WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'tunnel'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.704170] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\verification_pipeline.py:14
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.708577] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\verification_pipeline.py:15
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.736762] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\backups\path_fixes_20250717_185445\verification_pipeline.py:16
Status: FAILED
Original SQL: from typing import Dict, List, Any, Tuple
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.748622] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py:5
Status: FAILED
Original SQL: from typing import Dict, Any, Generator
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.770479] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py:6
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.777767] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py:7
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.802096] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py:9
Status: FAILED
Original SQL: from n8n_builder.agents.integration.agent_integration_manager import AgentIntegrationManager, WorkflowPriority, AgentConfig
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.817551] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py:10
Status: FAILED
Original SQL: from n8n_builder.agents.integration.monitoring import MonitoringManager, MetricType, HealthStatus
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.833367] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py:11
Status: FAILED
Original SQL: from n8n_builder.agents.integration.security import SecurityManager, PermissionLevel
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.838744] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py:12
Status: FAILED
Original SQL: from n8n_builder.agents.integration.error_recovery import ErrorRecoveryManager, CircuitState
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.864542] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py:13
Status: FAILED
Original SQL: from n8n_builder.agents.integration.ui_controller import AgentUIController
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.878467] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py:14
Status: FAILED
Original SQL: from n8n_builder.agents.integration.event_stream_manager import EventStreamManager
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.895676] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py:15
Status: FAILED
Original SQL: from n8n_builder.agents.integration.message_broker import MessageBroker
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.904861] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\conftest.py:16
Status: FAILED
Original SQL: from n8n_builder.agents.integration.state_manager import StateManager
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.927699] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\investigate_healer_disconnect.py:17
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.932546] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\investigate_healer_disconnect.py:18
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.961136] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\investigate_healer_disconnect.py:23
Status: FAILED
Original SQL: from Self_Healer.core.error_monitor import ErrorMonitor
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.968159] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\investigate_healer_disconnect.py:24
Status: FAILED
Original SQL: from Self_Healer.core.knowledge_integration import KnowledgeBaseIntegrator
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:24.992273] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\quick_healer_check.py:12
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.011029] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\quick_healer_check.py:17
Status: FAILED
Original SQL: from Self_Healer.core.error_monitor import ErrorMonitor
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.032687] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py:9
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.047796] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py:10
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.071026] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py:15
Status: FAILED
Original SQL: from test_system_health import run_system_health_check
Test SQL: SELECT 1 FROM test_system_health WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'test_system_health'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.085440] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py:16
Status: FAILED
Original SQL: from test_stored_procedures import run_all_tests as run_stored_procedure_tests
Test SQL: SELECT 1 FROM test_stored_procedures WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'test_stored_procedures'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.099104] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py:100
Status: FAILED
Original SQL: from n8n_builder.n8n_builder import N8NBuilder
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.111526] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py:101
Status: FAILED
Original SQL: from Self_Healer.core.knowledge_database_wrapper import SelfHealerKnowledgeDB
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.130326] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py:102
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.148493] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py:112
Status: FAILED
Original SQL: from n8n_builder.config import Config
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.172787] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py:133
Status: FAILED
Original SQL: from n8n_builder.n8n_builder import N8NBuilder
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.178548] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py:166
Status: FAILED
Original SQL: """Determine overall status from integration test results."""
Test SQL: SELECT 1 FROM integration WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'integration'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.204793] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\run_system_tests.py:175
Status: FAILED
Original SQL: """Calculate duration from timestamp string."""
Test SQL: SELECT 1 FROM timestamp WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'timestamp'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.210800] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_actual_response.py:10
Status: FAILED
Original SQL: from n8n_builder.n8n_builder import N8NBuilder
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.239981] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_actual_response.py:22
Status: FAILED
Original SQL: Next, I need to add a connection from Process Data's main output to the new email node. The source is "process-data", target is "email-node", type "main", index 0. That's an "add_connection" action.
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.259066] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_actual_response.py:24
Status: FAILED
Original SQL: Then, modify the existing connection from Process Data to Webhook Response. The current connection in the workflow connects Process Data's main to Webhook Response. So I need to change that to point to the new email node instead. Using "modify_connection", source_node is "process-data", old_target is "webhook-response", new_target is "email-node".
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.274946] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_actual_response.py:28
Status: FAILED
Original SQL: The example provided by the user replaces the connection from Process Data to the new email node, removing the original connection to Webhook Response. That makes sense if the goal is to have the email sent upon completion instead of the webhook response. So modifying the existing connection from Process Data's main to point to the email node instead of the webhook response.
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.289351] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_actual_response.py:34
Status: FAILED
Original SQL: 3. Modify the existing connection from Process Data to Webhook Response, changing it to Email node.
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.299905] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_actual_response.py:46
Status: FAILED
Original SQL: In the user's example, they modified the connection from Process Data to replace Webhook Response with Email. So in their case, the flow becomes Trigger → Process → Email, and the HTTP response is no longer connected. But maybe that's acceptable if the goal is just to send an email upon completion. Alternatively, perhaps both should be connected in series. However, the user's example shows replacing, so I'll follow that.
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.328969] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_actual_response.py:51
Status: FAILED
Original SQL: - Add a connection from Process Data to Email.
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.339487] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_actual_response.py:52
Status: FAILED
Original SQL: - Modify the existing connection from Process Data to point to Email instead of Webhook Response.
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.365100] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_integration.py:14
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.382888] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_integration.py:61
Status: FAILED
Original SQL: from n8n_builder.enhanced_prompt_builder import EnhancedPromptBuilder
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.399224] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_integration.py:125
Status: FAILED
Original SQL: from n8n_builder.n8n_builder import N8NBuilder
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.408819] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_integration.py:196
Status: FAILED
Original SQL: from n8n_builder.mcp_research_tool import N8NResearchTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.423803] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_integration.py:231
Status: FAILED
Original SQL: from n8n_builder.research_validator import ResearchValidator
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.444413] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_integration.py:232
Status: FAILED
Original SQL: from n8n_builder.mcp_research_tool import ResearchResult
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.468033] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_self_healer_flow.py:12
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.486224] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_self_healer_flow.py:13
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.493806] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_complete_self_healer_flow.py:41
Status: FAILED
Original SQL: from Self_Healer.core.healer_manager import SelfHealerManager
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.520640] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_db.py:2
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.531139] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_db.py:11
Status: SUCCESS
Original SQL: direct_query = "SELECT * FROM REF_Entity WHERE Name LIKE 'Session_%'"
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:25.552576] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_db.py:35
Status: FAILED
Original SQL: from Self_Healer.core.knowledge_database_wrapper import SelfHealerKnowledgeDB
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.569875] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_improvements.py:16
Status: FAILED
Original SQL: from Self_Healer.dashboard.dashboard import SelfHealerDashboard
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.586500] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_improvements.py:17
Status: FAILED
Original SQL: from Self_Healer.core.healer_manager import SelfHealerManager
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.602143] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_integration.py:2
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.617411] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_integration.py:9
Status: FAILED
Original SQL: from Self_Healer.core.knowledge_database_wrapper import SelfHealerKnowledgeDB
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.637546] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_integration.py:70
Status: FAILED
Original SQL: print("The dashboard should now show sessions from the KnowledgeBase.")
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.651320] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dependencies.py:6
Status: FAILED
Original SQL: from fastapi import FastAPI
Test SQL: SELECT 1 FROM fastapi WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.677625] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dependencies.py:7
Status: FAILED
Original SQL: from pydantic import BaseModel
Test SQL: SELECT 1 FROM pydantic WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pydantic'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.696279] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dependencies.py:8
Status: FAILED
Original SQL: from dotenv import load_dotenv
Test SQL: SELECT 1 FROM dotenv WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dotenv'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.711013] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dependencies.py:10
Status: FAILED
Original SQL: from rich import print
Test SQL: SELECT 1 FROM rich WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'rich'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.733821] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py:4
Status: FAILED
Original SQL: from n8n_builder.n8n_builder import N8NBuilder
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.750283] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py:9
Status: FAILED
Original SQL: First, I need to recall how n8n workflows work. The current setup has three nodes: Webhook Trigger, Process Data (a function node), and Webhook Response. The connections are from Webhook Trigger to Process Data, then from Process Data back to Webhook Response.
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.768592] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py:11
Status: FAILED
Original SQL: The user wants an email sent after the process completes. So the email node should come after the Process Data node but before or instead of the Webhook Response. Wait, looking at the example they provided, when adding the email node at the end, they modified the connection from Process Data to point to the email node instead of Webhook Response. That makes sense because in their example, the original flow was Process Data -> Webhook Response, and after modification, it's Process Data -> Email Node.
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.773937] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py:17
Status: FAILED
Original SQL: 2. Create a connection from Process Data (main) to Email Node (main).
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.802037] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py:18
Status: FAILED
Original SQL: 3. Modify the existing connection from Process Data to Webhook Response, replacing it with the new Email Node connection.
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.806920] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py:20
Status: FAILED
Original SQL: Checking the example provided by the user: they added the email node at position [1000, 300], then connected Process Data's main index 0 to email-node's main index 0. Then modified the existing connection from Process Data (main) which was pointing to Webhook Response to now point to Email Node.
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.834598] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py:26
Status: FAILED
Original SQL: - Add a connection from Process Data (main) to Email Node (main).
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.851740] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py:27
Status: FAILED
Original SQL: - Modify the existing connection from Process Data's main to Webhook Response to instead point to Email Node.
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.868758] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py:33
Status: FAILED
Original SQL: 2. add_connection from Process Data to Email Node.
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.892808] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_enhanced_extraction.py:34
Status: FAILED
Original SQL: 3. modify_connection changing the target of Process Data's main connection from Webhook Response to Email Node.
Test SQL: SELECT 1 FROM Webhook WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Webhook'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.906089] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_env.py:3
Status: FAILED
Original SQL: from dotenv import load_dotenv
Test SQL: SELECT 1 FROM dotenv WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dotenv'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.930928] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_env.py:5
Status: FAILED
Original SQL: from fastapi import FastAPI
Test SQL: SELECT 1 FROM fastapi WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.946422] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_env.py:7
Status: FAILED
Original SQL: from pydantic import BaseModel
Test SQL: SELECT 1 FROM pydantic WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pydantic'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.966970] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_filesystem_utilities.py:14
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.973161] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_filesystem_utilities.py:16
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:25.991135] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_filesystem_utilities.py:21
Status: FAILED
Original SQL: from n8n_builder.project_manager import (
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.019033] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_filesystem_utilities.py:104
Status: FAILED
Original SQL: """Test reading from a project that doesn't exist."""
Test SQL: SELECT 1 FROM a WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.031365] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_filesystem_utilities.py:453
Status: FAILED
Original SQL: self.assertEqual(stats['total_projects'], 4)  # 3 new + 1 from setUp
Test SQL: SELECT 1 FROM setUp WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'setUp'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.053421] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_filesystem_utilities.py:514
Status: FAILED
Original SQL: """Test copying from a source that doesn't exist."""
Test SQL: SELECT 1 FROM a WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.069962] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_filesystem_utilities.py:549
Status: FAILED
Original SQL: self.assertEqual(workflow_info.name, "minimal")  # Derived from filename
Test SQL: SELECT 1 FROM filename WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'filename'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.086916] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_filesystem_utilities.py:613
Status: FAILED
Original SQL: """Test a complete workflow lifecycle from creation to deletion."""
Test SQL: SELECT 1 FROM creation WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'creation'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.107925] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:10
Status: FAILED
Original SQL: from n8n_builder.n8n_builder import N8NBuilder
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.113550] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:22
Status: FAILED
Original SQL: Next, I need to add a connection from Process Data's main output to the new email node. The source is "process-data", target is "email-node", type "main", index 0. That's an "add_connection" action.
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.139931] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:24
Status: FAILED
Original SQL: Then, modify the existing connection from Process Data to Webhook Response. The current connection in the workflow connects Process Data's main to Webhook Response. So I need to change that to point to the new email node instead. Using "modify_connection", source_node is "process-data", old_target is "webhook-response", new_target is "email-node".
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.162736] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:28
Status: FAILED
Original SQL: The example provided by the user replaces the connection from Process Data to the new email node, removing the original connection to Webhook Response. That makes sense if the goal is to have the email sent upon completion instead of the webhook response. So modifying the existing connection from Process Data's main to point to the email node instead of the webhook response should work.
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.180593] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:33
Status: FAILED
Original SQL: 2. Add a connection from Process Data to the email node
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.197119] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:34
Status: FAILED
Original SQL: 3. Modify the existing connection from Process Data to point to the email node instead of Webhook Response
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.211409] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:39
Status: FAILED
Original SQL: 2. Modify the connection from Process Data to point to the email node
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.228747] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:40
Status: FAILED
Original SQL: 3. Add a connection from the email node to the webhook response
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.254730] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:166
Status: FAILED
Original SQL: Next, I need to add a connection from Process Data's main output to the new email node. The source is "process-data", target is "email-node", type "main", index 0. That's an "add_connection" action.
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.272736] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:168
Status: FAILED
Original SQL: Then, modify the existing connection from Process Data to Webhook Response. The current connection in the workflow connects Process Data's main to Webhook Response. So I need to change that to point to the new email node instead. Using "modify_connection", source_node is "process-data", old_target is "webhook-response", new_target is "email-node".
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.279676] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:172
Status: FAILED
Original SQL: The example provided by the user replaces the connection from Process Data to the new email node, removing the original connection to Webhook Response. That makes sense if the goal is to have the email sent upon completion instead of the webhook response. So modifying the existing connection from Process Data's main to point to the email node instead of the webhook response should work.
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.303911] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:177
Status: FAILED
Original SQL: 2. Add a connection from Process Data to the email node
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.314521] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:178
Status: FAILED
Original SQL: 3. Modify the existing connection from Process Data to point to the email node instead of Webhook Response
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.339916] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:183
Status: FAILED
Original SQL: 2. Modify the connection from Process Data to point to the email node
Test SQL: SELECT 1 FROM Process WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Process'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.351033] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:184
Status: FAILED
Original SQL: 3. Add a connection from the email node to the webhook response
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.368830] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:291
Status: FAILED
Original SQL: print("Step 1: Extract JSON from LLM response...")
Test SQL: SELECT 1 FROM LLM WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'LLM'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.394572] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:298
Status: FAILED
Original SQL: print("[FAIL] Could not extract JSON from response")
Test SQL: SELECT 1 FROM response WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'response'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.399625] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_full_modification.py:367
Status: FAILED
Original SQL: print("\n[OK] Validation passed - workflow should update successfully!")
Test SQL: SELECT 1 FROM successfully WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'successfully'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.429969] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py:9
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.448853] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py:10
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.457115] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py:16
Status: FAILED
Original SQL: from Self_Healer.core.error_monitor import ErrorMonitor
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.485473] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py:249
Status: FAILED
Original SQL: from Self_Healer.core.knowledge_integration import get_knowledge_integrator
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.491629] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py:257
Status: SUCCESS
Original SQL: test_query = "SELECT COUNT(*) as count FROM REF_Fact"
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: COUNT, count
----------------------------------------

[2025-08-17T23:59:26.520486] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_integration.py:22
Status: FAILED
Original SQL: from typing import Dict, List, Any, Optional
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.538401] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_integration.py:23
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.555306] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_integration.py:24
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.573001] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_integration.py:25
Status: FAILED
Original SQL: from unittest.mock import patch, Mock
Test SQL: SELECT 1 FROM unittest WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'unittest'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.590399] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_integration.py:32
Status: FAILED
Original SQL: from n8n_builder.n8n_builder import N8NBuilder, IterationMetrics
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.604375] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_integration.py:33
Status: FAILED
Original SQL: from n8n_builder.config import config
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.673790] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_iteration_methods.py:10
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.738025] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_iteration_methods.py:16
Status: FAILED
Original SQL: from n8n_builder.n8n_builder import N8NBuilder
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.784107] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_iteration_methods.py:24
Status: FAILED
Original SQL: """Load a test workflow from file."""
Test SQL: SELECT 1 FROM file WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'file'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.799033] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_iteration_methods.py:294
Status: FAILED
Original SQL: """Test creating a scheduled workflow from scratch."""
Test SQL: SELECT 1 FROM scratch WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'scratch'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.820561] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_iteration_methods.py:385
Status: FAILED
Original SQL: {"id": "6", "name": "Update Inventory", "type": "n8n-nodes-base.postgres", "parameters": {"operation": "update"}, "position": [500, 300]},
Test SQL: SELECT 1 FROM Inventory WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Inventory'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.830515] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_iteration_methods.py:394
Status: FAILED
Original SQL: "Process Payment": {"main": [[{"node": "Update Inventory", "type": "main", "index": 0}]]},
Test SQL: SELECT 1 FROM Inventory WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Inventory'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.851052] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_iteration_methods.py:395
Status: FAILED
Original SQL: "Update Inventory": {"main": [[{"node": "Send Confirmation", "type": "main", "index": 0}]]},
Test SQL: SELECT 1 FROM Inventory WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Inventory'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.891916] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:15
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.909734] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:16
Status: FAILED
Original SQL: from typing import Dict, Any, List
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.928093] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:21
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.946610] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:57
Status: FAILED
Original SQL: self.log_test_result("Database Connection", "FAIL", "", "No data returned from connection test")
Test SQL: SELECT 1 FROM connection WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'connection'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.961398] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:85
Status: FAILED
Original SQL: FROM sys.objects
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:26.978677] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:129
Status: SUCCESS
Original SQL: entities_query = "SELECT TOP 5 ID, Name FROM REF_Entity ORDER BY ID"
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: Name, ID
----------------------------------------

[2025-08-17T23:59:26.992300] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:136
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-17T23:59:27.018313] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:361
Status: SUCCESS
Original SQL: SELECT name FROM sys.objects
Test SQL: SELECT 1 FROM sys.objects
Result: SUCCESS - Tables: sys, Columns: name
----------------------------------------

[2025-08-17T23:59:27.033316] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:394
Status: FAILED
Original SQL: SELECT TABLE_NAME, TABLE_TYPE
Test SQL: SELECT TABLE_NAME, TABLE_TYPE
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'TABLE_NAME'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'TABLE_TYPE'. (207)")
----------------------------------------

[2025-08-17T23:59:27.054927] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:395
Status: FAILED
Original SQL: FROM INFORMATION_SCHEMA.TABLES
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.069508] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_live_self_healer.py:10
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.082433] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_live_self_healer.py:11
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.117048] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_live_self_healer.py:71
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.141736] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_log_rotation.py:13
Status: FAILED
Original SQL: from datetime import datetime, timedelta
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.158287] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_log_rotation.py:14
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.178734] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_log_rotation.py:19
Status: FAILED
Original SQL: from n8n_builder.log_rotation_manager import LogRotationManager, DatestampedTimedRotatingFileHandler
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.194457] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_database.py:8
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.212490] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_database.py:14
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool, get_mcp_database
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.222485] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_research.py:13
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.238917] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_research.py:28
Status: FAILED
Original SQL: from n8n_builder.mcp_research_tool import N8NResearchTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.270737] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_research.py:29
Status: FAILED
Original SQL: from n8n_builder.research_formatter import ResearchFormatter
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.280982] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_research.py:30
Status: FAILED
Original SQL: from n8n_builder.enhanced_prompt_builder import EnhancedPromptBuilder
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.304760] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_research.py:116
Status: FAILED
Original SQL: from n8n_builder.n8n_builder import N8NBuilder
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.320461] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_research.py:157
Status: FAILED
Original SQL: from n8n_builder.research_formatter import ResearchFormatter
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.331301] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_research.py:158
Status: FAILED
Original SQL: from n8n_builder.mcp_research_tool import ResearchResult
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.362742] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_n8n_builder_unit.py:8
Status: FAILED
Original SQL: from unittest.mock import Mock, patch, AsyncMock
Test SQL: SELECT 1 FROM unittest WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'unittest'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.369032] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_n8n_builder_unit.py:12
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.408170] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_n8n_builder_unit.py:13
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.439865] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_n8n_builder_unit.py:19
Status: FAILED
Original SQL: from n8n_builder.n8n_builder import N8NBuilder, IterationMetrics
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.458989] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_n8n_builder_unit.py:20
Status: FAILED
Original SQL: from n8n_builder.config import config
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.492785] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:16
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.508578] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:30
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
Test SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'COLUMN_NAME'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'DATA_TYPE'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'IS_NULLABLE'. (207)")
----------------------------------------

[2025-08-17T23:59:27.524855] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:31
Status: FAILED
Original SQL: FROM INFORMATION_SCHEMA.COLUMNS
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.535376] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:53
Status: SUCCESS
Original SQL: FROM REF_EntityValue
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:27.557334] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:67
Status: FAILED
Original SQL: SELECT TOP 5
Test SQL: SELECT TOP 5
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '5'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.579967] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:72
Status: SUCCESS
Original SQL: FROM REF_EntityValue
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:27.601274] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:132
Status: FAILED
Original SQL: from Self_Healer.core.knowledge_integration import SelfHealerKnowledgeIntegrator
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.616741] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:150
Status: FAILED
Original SQL: SELECT EntityValue, NumericValue, ValueUnits
Test SQL: SELECT EntityValue, NumericValue, ValueUnits
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityValue'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'NumericValue'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'ValueUnits'. (207)")
----------------------------------------

[2025-08-17T23:59:27.636236] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:151
Status: SUCCESS
Original SQL: FROM REF_EntityValue
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-17T23:59:27.649597] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:179
Status: FAILED
Original SQL: print("   2. Run update_procedures_numeric_values.sql to update procedures")
Test SQL: SELECT 1 FROM procedures WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'procedures'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.677477] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_api.py:14
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.695767] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_api.py:16
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.706383] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_api.py:17
Status: FAILED
Original SQL: from fastapi.testclient import TestClient
Test SQL: SELECT 1 FROM fastapi WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.731336] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_api.py:23
Status: FAILED
Original SQL: from n8n_builder.app import app
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.749838] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_api.py:24
Status: FAILED
Original SQL: from n8n_builder.project_manager import ProjectManager, FileSystemUtilities
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.772417] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_cleanup.py:12
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.794381] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_cleanup.py:13
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.816310] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_cleanup.py:18
Status: FAILED
Original SQL: from Scripts.project_cleanup_manager import ProjectCleanupManager
Test SQL: SELECT 1 FROM Scripts WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Scripts'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.832290] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_management.py:13
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.852006] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_management.py:15
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.870683] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_management.py:20
Status: FAILED
Original SQL: from n8n_builder.project_manager import (
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.894337] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_project_management.py:316
Status: FAILED
Original SQL: """Test ProjectInfo to/from dict conversion."""
Test SQL: SELECT 1 FROM dict WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dict'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.902616] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_research_quality.py:14
Status: FAILED
Original SQL: from typing import Dict, List, Any
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.931332] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_research_quality.py:15
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.946699] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_research_quality.py:63
Status: FAILED
Original SQL: "description": "Monitor Twitter mentions and automatically respond via Discord and update a tracking spreadsheet",
Test SQL: SELECT 1 FROM a WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.971828] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_research_quality.py:71
Status: FAILED
Original SQL: "description": "When an order is placed on Shopify, send order details to Slack, update inventory in Google Sheets, and trigger fulfillment",
Test SQL: SELECT 1 FROM inventory WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'inventory'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:27.990485] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_research_quality.py:98
Status: FAILED
Original SQL: from n8n_builder.enhanced_prompt_builder import EnhancedPromptBuilder
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.009196] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_research_quality.py:99
Status: FAILED
Original SQL: from n8n_builder.research_validator import ResearchValidator
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.029687] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_fix.py:10
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.048706] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_fix.py:11
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.068895] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_fix.py:38
Status: FAILED
Original SQL: from Self_Healer.core.error_monitor import ErrorMonitor
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.087227] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_fix.py:76
Status: FAILED
Original SQL: from Self_Healer.core.healer_manager import SelfHealerManager
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.097051] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_fix.py:128
Status: FAILED
Original SQL: from Self_Healer.core.knowledge_integration import get_knowledge_integrator
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.116059] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_integration.py:11
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.148842] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_integration.py:16
Status: FAILED
Original SQL: from n8n_builder.error_handler import EnhancedErrorHandler, ErrorDetail, ErrorCategory, ErrorSeverity
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.166540] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_integration.py:42
Status: FAILED
Original SQL: from core.healer_manager import SelfHealerManager
Test SQL: SELECT 1 FROM core WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'core'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.184821] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_integration.py:111
Status: FAILED
Original SQL: if i % 3 == 0:  # Update every 3 seconds
Test SQL: SELECT 1 FROM every WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'every'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.205128] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_integration.py:125
Status: FAILED
Original SQL: from dashboard.dashboard import SelfHealerDashboard
Test SQL: SELECT 1 FROM dashboard WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dashboard'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.220835] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_rescan.py:8
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.245446] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_self_healer_rescan.py:13
Status: FAILED
Original SQL: from Self_Healer.core.error_monitor import ErrorMonitor
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.264480] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_smart_logging.py:8
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.281576] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_smart_logging.py:14
Status: FAILED
Original SQL: from Self_Healer.core.error_monitor import ErrorMonitor
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.304465] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py:13
Status: FAILED
Original SQL: from Self_Healer.core.knowledge_database_wrapper import SelfHealerKnowledgeDB
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.322328] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py:14
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.338152] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py:57
Status: FAILED
Original SQL: SELECT name
Test SQL: SELECT name
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'name'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.361964] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py:58
Status: FAILED
Original SQL: FROM sys.objects
Test SQL: SELECT 1 FROM sys WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.385097] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_system_health.py:13
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.398835] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_system_health.py:14
Status: FAILED
Original SQL: from typing import Dict, List, Any, Optional
Test SQL: SELECT 1 FROM typing WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'typing'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.423829] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_system_health.py:15
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.446796] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_system_health.py:20
Status: FAILED
Original SQL: from n8n_builder.mcp_database_tool import MCPDatabaseTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.460817] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_system_health.py:21
Status: FAILED
Original SQL: from Self_Healer.core.knowledge_database_wrapper import SelfHealerKnowledgeDB
Test SQL: SELECT 1 FROM Self_Healer WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Self_Healer'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.486257] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_system_health.py:22
Status: FAILED
Original SQL: from n8n_builder.config import Config
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.504498] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_system_health.py:23
Status: FAILED
Original SQL: from n8n_builder.mcp_research_tool import MCPResearchTool
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.527056] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_version_management.py:14
Status: FAILED
Original SQL: from pathlib import Path
Test SQL: SELECT 1 FROM pathlib WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'pathlib'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.532570] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_version_management.py:16
Status: FAILED
Original SQL: from datetime import datetime
Test SQL: SELECT 1 FROM datetime WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'datetime'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.569717] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_version_management.py:17
Status: FAILED
Original SQL: from fastapi.testclient import TestClient
Test SQL: SELECT 1 FROM fastapi WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'fastapi'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.586088] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_version_management.py:23
Status: FAILED
Original SQL: from n8n_builder.app import app
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.615500] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_version_management.py:24
Status: FAILED
Original SQL: from n8n_builder.project_manager import ProjectManager, FileSystemUtilities
Test SQL: SELECT 1 FROM n8n_builder WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'n8n_builder'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-17T23:59:28.633144] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_version_management.py:190
Status: FAILED
Original SQL: """Test restoring a workflow from a specific version."""
Test SQL: SELECT 1 FROM a WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'a'. (208) (SQLExecDirectW)")
----------------------------------------


Session ended: 2025-08-17T23:59:28.669601
================================================================================
