SQL Validation Log - Session 20250818_000710
Started: 2025-08-18T00:07:10.646470
================================================================================

[2025-08-18T00:07:10.689206] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:109
Status: SUCCESS
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
               COUNT(e.ID) as EvidenceCount,
               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Test SQL: SELECT 1
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Result: SUCCESS - Tables: REF_Evidence, REF_Fact, Columns: FLOAT, CreateDate, DataSource, e, CAST, OverallAverage, AVG, Name, EvidenceCount, f, COUNT, ID, OVER, ValidityRating
----------------------------------------

[2025-08-18T00:07:10.692827] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:109
Status: SUCCESS
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
               COUNT(e.ID) as EvidenceCount,
               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Test SQL: SELECT 1
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Result: SUCCESS - Tables: REF_Evidence, REF_Fact, Columns: FLOAT, CreateDate, DataSource, e, CAST, OverallAverage, AVG, Name, EvidenceCount, f, COUNT, ID, OVER, ValidityRating
----------------------------------------

[2025-08-18T00:07:10.694329] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:194
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-18T00:07:10.696333] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:194
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-18T00:07:10.698333] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:206
Status: FAILED
Original SQL: SELECT ValidityRating FROM REF_Fact WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Fact WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.699835] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:218
Status: FAILED
Original SQL: SELECT COUNT(*) as EvidenceCount FROM REF_Evidence WHERE FactID = ?
Test SQL: SELECT 1 FROM REF_Evidence WHERE FactID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.701838] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:226
Status: FAILED
Original SQL: UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Fact WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.703864] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:260
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   'fact' as KnowledgeType
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.706315] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:260
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   'fact' as KnowledgeType
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.710314] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:276
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
                   'opinion' as KnowledgeType
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.713314] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:276
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
                   'opinion' as KnowledgeType
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.715096] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:292
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
                   f.ValidityRating, 'evidence' as KnowledgeType
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.717095] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:292
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
                   f.ValidityRating, 'evidence' as KnowledgeType
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.720489] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:329
Status: SUCCESS
Original SQL: SELECT 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END as Category,
            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Test SQL: SELECT 
            1
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Result: SUCCESS - Tables: REF_Fact, Columns: END, AVG, Name, Configuration, ELSE, CAST, THEN, f, JSON_Parsing, Database, LIKE, CASE, Other, Workflow, JSON, AvgEffectiveness, WHEN, Network, Category, FLOAT, ValidityRating
----------------------------------------

[2025-08-18T00:07:10.722488] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:329
Status: SUCCESS
Original SQL: SELECT 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END as Category,
            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Test SQL: SELECT 
            1
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Result: SUCCESS - Tables: REF_Fact, Columns: END, AVG, Name, Configuration, ELSE, CAST, THEN, f, JSON_Parsing, Database, LIKE, CASE, Other, Workflow, JSON, AvgEffectiveness, WHEN, Network, Category, FLOAT, ValidityRating
----------------------------------------

[2025-08-18T00:07:10.732544] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:241
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, ValidityRating, DataSource) 
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?)
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T00:07:10.734777] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:241
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, ValidityRating, DataSource) 
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?)
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T00:07:10.736777] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:267
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-18T00:07:10.738776] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:267
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-18T00:07:10.772879] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:374
Status: FAILED
Original SQL: SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Test SQL: SELECT 1
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.803711] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:374
Status: FAILED
Original SQL: SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Test SQL: SELECT 1
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.848334] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:396
Status: FAILED
Original SQL: UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Fact WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.897904] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:410
Status: FAILED
Original SQL: SELECT ID FROM REF_Entity WHERE Name = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.913101] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:419
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.915980] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:419
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.918979] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:430
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
                FROM REF_Evidence e
                WHERE e.FactID = ?
Test SQL: SELECT 1
                FROM REF_Evidence e
                WHERE e.FactID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.921408] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:430
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
                FROM REF_Evidence e
                WHERE e.FactID = ?
Test SQL: SELECT 1
                FROM REF_Evidence e
                WHERE e.FactID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.924413] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:439
Status: FAILED
Original SQL: SELECT a.Name as AttributeName, ev.EntityValue, eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Test SQL: SELECT 1
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.926412] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:439
Status: FAILED
Original SQL: SELECT a.Name as AttributeName, ev.EntityValue, eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Test SQL: SELECT 1
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.929413] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:465
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.932413] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:465
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.934415] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:477
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.937459] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:477
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.940394] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:552
Status: FAILED
Original SQL: SELECT ID FROM REF_Category WHERE Name = ?
Test SQL: SELECT 1 FROM REF_Category WHERE Name = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.942393] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:559
Status: SUCCESS
Original SQL: INSERT INTO REF_Category (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT 1 FROM REF_Category WHERE 1=0
Result: SUCCESS - Tables: REF_Category, Columns: 
----------------------------------------

[2025-08-18T00:07:10.946394] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:566
Status: FAILED
Original SQL: SELECT ID FROM REF_Entity WHERE Name = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.949393] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:573
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T00:07:10.951393] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:580
Status: FAILED
Original SQL: SELECT ID FROM REF_Attribute WHERE Name = ?
Test SQL: SELECT 1 FROM REF_Attribute WHERE Name = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.954461] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:587
Status: SUCCESS
Original SQL: INSERT INTO REF_Attribute (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-18T00:07:10.957092] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:594
Status: FAILED
Original SQL: SELECT ID FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?
Test SQL: SELECT 1 FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.960091] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:602
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)
            OUTPUT INSERTED.ID
            VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-18T00:07:10.963095] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:602
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)
            OUTPUT INSERTED.ID
            VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-18T00:07:10.965095] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:610
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue) OUTPUT INSERTED.ID VALUES (?, ?)
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-18T00:07:10.968095] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:620
Status: FAILED
Original SQL: SELECT ID FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.970094] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:620
Status: FAILED
Original SQL: SELECT ID FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.974095] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:630
Status: FAILED
Original SQL: INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)
        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:10.977674] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:630
Status: FAILED
Original SQL: INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)
        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:10.985872] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:855
Status: FAILED
Original SQL: SELECT ID, Name, CreateDate
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Test SQL: SELECT 1
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.988872] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:855
Status: FAILED
Original SQL: SELECT ID, Name, CreateDate
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Test SQL: SELECT 1
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.992592] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:872
Status: FAILED
Original SQL: SELECT
                a.Name as AttributeName,
                ev.EntityValue as AttributeValue,
                ev.NumericValue,
                ev.ValueUnits,
                ev.Name as ValueName,
                eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Test SQL: SELECT
                1
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:10.995215] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:872
Status: FAILED
Original SQL: SELECT
                a.Name as AttributeName,
                ev.EntityValue as AttributeValue,
                ev.NumericValue,
                ev.ValueUnits,
                ev.Name as ValueName,
                eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Test SQL: SELECT
                1
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.004598] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:914
Status: FAILED
Original SQL: SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.036864] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:914
Status: FAILED
Original SQL: SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.084210] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:926
Status: FAILED
Original SQL: SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.112213] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:926
Status: FAILED
Original SQL: SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.129779] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:991
Status: FAILED
Original SQL: SELECT Name, ValidityRating, DataSource, CreateDate
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.156310] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:991
Status: FAILED
Original SQL: SELECT Name, ValidityRating, DataSource, CreateDate
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.160610] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1009
Status: FAILED
Original SQL: SELECT Name, ValidityRating, Opinion, DataSource, CreateDate
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.163571] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1009
Status: FAILED
Original SQL: SELECT Name, ValidityRating, Opinion, DataSource, CreateDate
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.177543] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1021
Status: FAILED
Original SQL: SELECT DISTINCT e.Name, e.CreateDate
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.185118] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1021
Status: FAILED
Original SQL: SELECT DISTINCT e.Name, e.CreateDate
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.195746] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:34
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Test SQL: SELECT 1
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_EntityValue'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:11.199867] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:34
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Test SQL: SELECT 1
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_EntityValue'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:11.210658] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:66
Status: SUCCESS
Original SQL: SELECT name, create_date, modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Test SQL: SELECT 1
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: name, create_date, modify_date
----------------------------------------

[2025-08-18T00:07:11.221038] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:66
Status: SUCCESS
Original SQL: SELECT name, create_date, modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Test SQL: SELECT 1
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: name, create_date, modify_date
----------------------------------------

[2025-08-18T00:07:11.225500] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:93
Status: SUCCESS
Original SQL: SELECT name FROM sys.procedures WHERE name = '{proc_name}'
Test SQL: SELECT 1 FROM sys.procedures WHERE name = '{proc_name}'
Result: SUCCESS - Tables: sys, Columns: name
----------------------------------------

[2025-08-18T00:07:11.231212] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:106
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T00:07:11.234216] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:106
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T00:07:11.239260] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:119
Status: FAILED
Original SQL: SELECT COUNT(*) as row_count FROM {table_name}
Test SQL: SELECT 1 FROM {table_name}
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.246259] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:9
Status: SUCCESS
Original SQL: SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T00:07:11.249813] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:17
Status: SUCCESS
Original SQL: SELECT TOP 5 * FROM REF_Entity WHERE Name LIKE 'Session_%'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name LIKE 'Session_%'
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T00:07:11.256403] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:26
Status: SUCCESS
Original SQL: SELECT TOP 10 * FROM REF_Entity ORDER BY CreateDate DESC
Test SQL: SELECT 1 FROM REF_Entity ORDER BY CreateDate DESC
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T00:07:11.259404] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:33
Status: FAILED
Original SQL: SELECT TOP 5 * FROM XRF_EntityAttributeValue
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:11.271327] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py:48
Status: SUCCESS
Original SQL: SELECT name, create_date 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 1 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: name, create_date
----------------------------------------

[2025-08-18T00:07:11.315900] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py:48
Status: SUCCESS
Original SQL: SELECT name, create_date 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 1 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: name, create_date
----------------------------------------

[2025-08-18T00:07:11.337272] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:113
Status: FAILED
Original SQL: SELECT @count = COUNT(*) FROM
Test SQL: SELECT @count = COUNT(*) FROM
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable "@count". (137) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.414639] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py:117
Status: FAILED
Original SQL: SELECT * FROM \1 ORDER BY
Test SQL: SELECT 1 FROM \1 ORDER BY
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '\\1'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:11.427291] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:37
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T00:07:11.431294] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:37
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T00:07:11.436298] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:77
Status: SUCCESS
Original SQL: SELECT TOP 1 * FROM REF_Entity
Test SQL: SELECT 1 FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T00:07:11.439297] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:87
Status: SUCCESS
Original SQL: SELECT TOP 1 * FROM REF_Entity
Test SQL: SELECT 1 FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T00:07:11.449298] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:268
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-18T00:07:11.453349] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:269
Status: SUCCESS
Original SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-18T00:07:11.573806] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:208
Status: FAILED
Original SQL: SELECT * FROM REF_Entity WHERE Name = \'test\'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = \'test\'
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:11.580192] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:209
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T00:07:11.584207] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:209
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T00:07:11.588096] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:210
Status: FAILED
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.606907] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:211
Status: FAILED
Original SQL: DELETE FROM REF_Entity WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.621799] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:212
Status: FAILED
Original SQL: SELECT COUNT(*) FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID
Test SQL: SELECT 1 FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:11.658798] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:218
Status: FAILED
Original SQL: SELECT your option FROM the menu
Test SQL: SELECT 1 FROM the menu
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:11.681780] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:228
Status: FAILED
Original SQL: SELECT * FROM
Test SQL: SELECT * FROM
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'FROM'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:11.687019] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:230
Status: FAILED
Original SQL: SELECT name FROM users WHERE active = 1 ORDER BY name
Test SQL: SELECT 1 FROM users WHERE active = 1 ORDER BY name
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:11.712788] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:109
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE 1=0
Test SQL: SELECT 1 FROM {table_name} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.716838] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:120
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE {where_clause}
Test SQL: SELECT 1 FROM {table_name} WHERE {where_clause}
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.721838] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:122
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE 1=0
Test SQL: SELECT 1 FROM {table_name} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.725838] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:133
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE {where_clause}
Test SQL: SELECT 1 FROM {table_name} WHERE {where_clause}
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.729838] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:135
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE 1=0
Test SQL: SELECT 1 FROM {table_name} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.733881] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:142
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE 1=0
Test SQL: SELECT 1 FROM {table_name} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.737881] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:250
Status: FAILED
Original SQL: SELECT 1 FROM {tables[0]} WHERE 1=0
Test SQL: SELECT 1 FROM {tables[0]} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:11.758921] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py:36
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T00:07:11.764254] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py:36
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T00:07:11.815140] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:111
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Test SQL: SELECT 1 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T00:07:11.827177] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:111
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Test SQL: SELECT 1 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T00:07:11.844213] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:131
Status: SUCCESS
Original SQL: SELECT COLUMN_NAME, DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Test SQL: SELECT 1 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: COLUMN_NAME, TABLE_NAME, DATA_TYPE
----------------------------------------

[2025-08-18T00:07:11.864148] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:131
Status: SUCCESS
Original SQL: SELECT COLUMN_NAME, DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Test SQL: SELECT 1 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: COLUMN_NAME, TABLE_NAME, DATA_TYPE
----------------------------------------

[2025-08-18T00:07:11.887871] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:12
Status: SUCCESS
Original SQL: SELECT 
            name,
            create_date,
            modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 
            1
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: name, create_date, modify_date
----------------------------------------

[2025-08-18T00:07:11.909592] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:12
Status: SUCCESS
Original SQL: SELECT 
            name,
            create_date,
            modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 
            1
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: name, create_date, modify_date
----------------------------------------

[2025-08-18T00:07:11.937395] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:83
Status: SUCCESS
Original SQL: SELECT name FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 1 FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: name
----------------------------------------

[2025-08-18T00:07:11.960032] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:83
Status: SUCCESS
Original SQL: SELECT name FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 1 FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: name
----------------------------------------

[2025-08-18T00:07:11.996507] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_db.py:11
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name LIKE 'Session_%'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name LIKE 'Session_%'
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T00:07:12.047989] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py:257
Status: SUCCESS
Original SQL: SELECT COUNT(*) as count FROM REF_Fact
Test SQL: SELECT 1 FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: count, COUNT
----------------------------------------

[2025-08-18T00:07:12.086419] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:79
Status: FAILED
Original SQL: SELECT 
                name,
                create_date,
                modify_date,
                type_desc
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Test SQL: SELECT 
                1
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:12.106369] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:79
Status: FAILED
Original SQL: SELECT 
                name,
                create_date,
                modify_date,
                type_desc
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Test SQL: SELECT 
                1
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:12.141177] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:129
Status: SUCCESS
Original SQL: SELECT TOP 5 ID, Name FROM REF_Entity ORDER BY ID
Test SQL: SELECT 1 FROM REF_Entity ORDER BY ID
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-18T00:07:12.177756] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:135
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)
                OUTPUT INSERTED.ID, INSERTED.Name
                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T00:07:12.183911] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:135
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)
                OUTPUT INSERTED.ID, INSERTED.Name
                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T00:07:12.198303] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:360
Status: SUCCESS
Original SQL: SELECT name FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Test SQL: SELECT 1 FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Result: SUCCESS - Tables: sys, Columns: name, type
----------------------------------------

[2025-08-18T00:07:12.203102] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:360
Status: SUCCESS
Original SQL: SELECT name FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Test SQL: SELECT 1 FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Result: SUCCESS - Tables: sys, Columns: name, type
----------------------------------------

[2025-08-18T00:07:12.212252] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:393
Status: SUCCESS
Original SQL: SELECT TABLE_NAME, TABLE_TYPE
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Test SQL: SELECT 1
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T00:07:12.217287] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:393
Status: SUCCESS
Original SQL: SELECT TABLE_NAME, TABLE_TYPE
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Test SQL: SELECT 1
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T00:07:12.224100] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_database.py:180
Status: FAILED
Original SQL: SELECT COUNT(*) as total_rows FROM [{schema_name}].[{table_name}]
Test SQL: SELECT 1 FROM [{schema_name}].[{table_name}]
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:12.258784] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:29
Status: SUCCESS
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Test SQL: SELECT 1
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: COLUMN_NAME, IS_NULLABLE, TABLE_NAME, DATA_TYPE
----------------------------------------

[2025-08-18T00:07:12.281916] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:29
Status: SUCCESS
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Test SQL: SELECT 1
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: COLUMN_NAME, IS_NULLABLE, TABLE_NAME, DATA_TYPE
----------------------------------------

[2025-08-18T00:07:12.310603] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:48
Status: SUCCESS
Original SQL: SELECT 
            COUNT(*) as TotalRows,
            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,
            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows
        FROM REF_EntityValue
Test SQL: SELECT 
            1
        FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: NULL, IS, WHEN, UnitsRows, TotalRows, NumericValue, END, CASE, ValueUnits, THEN, COUNT, NumericRows, NOT
----------------------------------------

[2025-08-18T00:07:12.325839] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:48
Status: SUCCESS
Original SQL: SELECT 
            COUNT(*) as TotalRows,
            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,
            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows
        FROM REF_EntityValue
Test SQL: SELECT 
            1
        FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: NULL, IS, WHEN, UnitsRows, TotalRows, NumericValue, END, CASE, ValueUnits, THEN, COUNT, NumericRows, NOT
----------------------------------------

[2025-08-18T00:07:12.380249] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:66
Status: SUCCESS
Original SQL: SELECT TOP 5
            Name,
            EntityValue,
            NumericValue,
            ValueUnits
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Test SQL: SELECT 1
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Result: SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue, Name, EntityValue
----------------------------------------

[2025-08-18T00:07:12.427290] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:66
Status: SUCCESS
Original SQL: SELECT TOP 5
            Name,
            EntityValue,
            NumericValue,
            ValueUnits
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Test SQL: SELECT 1
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Result: SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue, Name, EntityValue
----------------------------------------

[2025-08-18T00:07:12.432628] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:149
Status: FAILED
Original SQL: SELECT EntityValue, NumericValue, ValueUnits
            FROM REF_EntityValue 
            WHERE ID = ?
Test SQL: SELECT 1
            FROM REF_EntityValue 
            WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:12.445848] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:149
Status: FAILED
Original SQL: SELECT EntityValue, NumericValue, ValueUnits
            FROM REF_EntityValue 
            WHERE ID = ?
Test SQL: SELECT 1
            FROM REF_EntityValue 
            WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:12.471344] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py:56
Status: FAILED
Original SQL: SELECT name 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Test SQL: SELECT 1 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:12.507088] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py:56
Status: FAILED
Original SQL: SELECT name 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Test SQL: SELECT 1 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------


Session ended: 2025-08-18T00:07:12.546544
================================================================================
