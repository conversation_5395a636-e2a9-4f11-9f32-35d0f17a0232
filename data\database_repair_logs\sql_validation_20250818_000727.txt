SQL Validation Log - Session 20250818_000727
Started: 2025-08-18T00:07:27.893103
================================================================================

[2025-08-18T00:07:27.955693] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:109
Status: SUCCESS
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
               COUNT(e.ID) as EvidenceCount,
               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Test SQL: SELECT 1
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Result: SUCCESS - Tables: REF_Evidence, REF_Fact, Columns: AVG, OverallAverage, CreateDate, e, COUNT, OVER, CAST, ID, f, DataSource, ValidityRating, FLOAT, EvidenceCount, Name
----------------------------------------

[2025-08-18T00:07:27.959462] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:109
Status: SUCCESS
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
               COUNT(e.ID) as EvidenceCount,
               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Test SQL: SELECT 1
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Result: SUCCESS - Tables: REF_Evidence, REF_Fact, Columns: AVG, OverallAverage, CreateDate, e, COUNT, OVER, CAST, ID, f, DataSource, ValidityRating, FLOAT, EvidenceCount, Name
----------------------------------------

[2025-08-18T00:07:27.962468] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:194
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-18T00:07:27.964996] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:194
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-18T00:07:27.967055] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:206
Status: FAILED
Original SQL: SELECT ValidityRating FROM REF_Fact WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Fact WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:27.969106] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:218
Status: FAILED
Original SQL: SELECT COUNT(*) as EvidenceCount FROM REF_Evidence WHERE FactID = ?
Test SQL: SELECT 1 FROM REF_Evidence WHERE FactID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:27.971157] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:226
Status: FAILED
Original SQL: UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Fact WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:27.973216] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:260
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   'fact' as KnowledgeType
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:27.975414] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:260
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   'fact' as KnowledgeType
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:27.977419] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:276
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
                   'opinion' as KnowledgeType
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:27.979357] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:276
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
                   'opinion' as KnowledgeType
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:27.982413] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:292
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
                   f.ValidityRating, 'evidence' as KnowledgeType
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:27.984410] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:292
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
                   f.ValidityRating, 'evidence' as KnowledgeType
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:27.988409] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:329
Status: SUCCESS
Original SQL: SELECT 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END as Category,
            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Test SQL: SELECT 
            1
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Result: SUCCESS - Tables: REF_Fact, Columns: Configuration, ELSE, WHEN, END, f, FLOAT, AVG, AvgEffectiveness, THEN, JSON_Parsing, Database, Other, Category, JSON, CASE, ValidityRating, Name, Workflow, CAST, Network, LIKE
----------------------------------------

[2025-08-18T00:07:27.990854] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:329
Status: SUCCESS
Original SQL: SELECT 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END as Category,
            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Test SQL: SELECT 
            1
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Result: SUCCESS - Tables: REF_Fact, Columns: Configuration, ELSE, WHEN, END, f, FLOAT, AVG, AvgEffectiveness, THEN, JSON_Parsing, Database, Other, Category, JSON, CASE, ValidityRating, Name, Workflow, CAST, Network, LIKE
----------------------------------------

[2025-08-18T00:07:27.999317] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:241
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, ValidityRating, DataSource) 
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?)
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T00:07:28.001862] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:241
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, ValidityRating, DataSource) 
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?)
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T00:07:28.003481] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:267
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-18T00:07:28.004984] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:267
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-18T00:07:28.007987] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:374
Status: FAILED
Original SQL: SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Test SQL: SELECT 1
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.009987] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:374
Status: FAILED
Original SQL: SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Test SQL: SELECT 1
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.011989] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:396
Status: FAILED
Original SQL: UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Fact WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.013991] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:410
Status: FAILED
Original SQL: SELECT ID FROM REF_Entity WHERE Name = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.016991] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:419
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.018991] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:419
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.022941] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:430
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
                FROM REF_Evidence e
                WHERE e.FactID = ?
Test SQL: SELECT 1
                FROM REF_Evidence e
                WHERE e.FactID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.025945] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:430
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
                FROM REF_Evidence e
                WHERE e.FactID = ?
Test SQL: SELECT 1
                FROM REF_Evidence e
                WHERE e.FactID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.028449] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:439
Status: FAILED
Original SQL: SELECT a.Name as AttributeName, ev.EntityValue, eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Test SQL: SELECT 1
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.030119] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:439
Status: FAILED
Original SQL: SELECT a.Name as AttributeName, ev.EntityValue, eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Test SQL: SELECT 1
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.033275] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:465
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.035912] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:465
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.038916] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:477
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.040916] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:477
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.043916] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:552
Status: FAILED
Original SQL: SELECT ID FROM REF_Category WHERE Name = ?
Test SQL: SELECT 1 FROM REF_Category WHERE Name = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.047174] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:559
Status: SUCCESS
Original SQL: INSERT INTO REF_Category (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT 1 FROM REF_Category WHERE 1=0
Result: SUCCESS - Tables: REF_Category, Columns: 
----------------------------------------

[2025-08-18T00:07:28.049174] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:566
Status: FAILED
Original SQL: SELECT ID FROM REF_Entity WHERE Name = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.052282] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:573
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T00:07:28.054763] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:580
Status: FAILED
Original SQL: SELECT ID FROM REF_Attribute WHERE Name = ?
Test SQL: SELECT 1 FROM REF_Attribute WHERE Name = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.057868] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:587
Status: SUCCESS
Original SQL: INSERT INTO REF_Attribute (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-18T00:07:28.059868] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:594
Status: FAILED
Original SQL: SELECT ID FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?
Test SQL: SELECT 1 FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.062455] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:602
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)
            OUTPUT INSERTED.ID
            VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-18T00:07:28.065458] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:602
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)
            OUTPUT INSERTED.ID
            VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-18T00:07:28.068634] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:610
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue) OUTPUT INSERTED.ID VALUES (?, ?)
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-18T00:07:28.071757] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:620
Status: FAILED
Original SQL: SELECT ID FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.074953] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:620
Status: FAILED
Original SQL: SELECT ID FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.077955] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:630
Status: FAILED
Original SQL: INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)
        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:28.080264] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:630
Status: FAILED
Original SQL: INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)
        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:28.089263] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:855
Status: FAILED
Original SQL: SELECT ID, Name, CreateDate
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Test SQL: SELECT 1
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.092266] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:855
Status: FAILED
Original SQL: SELECT ID, Name, CreateDate
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Test SQL: SELECT 1
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.095266] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:872
Status: FAILED
Original SQL: SELECT
                a.Name as AttributeName,
                ev.EntityValue as AttributeValue,
                ev.NumericValue,
                ev.ValueUnits,
                ev.Name as ValueName,
                eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Test SQL: SELECT
                1
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.098002] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:872
Status: FAILED
Original SQL: SELECT
                a.Name as AttributeName,
                ev.EntityValue as AttributeValue,
                ev.NumericValue,
                ev.ValueUnits,
                ev.Name as ValueName,
                eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Test SQL: SELECT
                1
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.100808] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:914
Status: FAILED
Original SQL: SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.103305] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:914
Status: FAILED
Original SQL: SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.106869] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:926
Status: FAILED
Original SQL: SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.109868] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:926
Status: FAILED
Original SQL: SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.112935] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:991
Status: FAILED
Original SQL: SELECT Name, ValidityRating, DataSource, CreateDate
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.115442] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:991
Status: FAILED
Original SQL: SELECT Name, ValidityRating, DataSource, CreateDate
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.118194] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1009
Status: FAILED
Original SQL: SELECT Name, ValidityRating, Opinion, DataSource, CreateDate
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.121197] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1009
Status: FAILED
Original SQL: SELECT Name, ValidityRating, Opinion, DataSource, CreateDate
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.125249] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1021
Status: FAILED
Original SQL: SELECT DISTINCT e.Name, e.CreateDate
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.128248] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1021
Status: FAILED
Original SQL: SELECT DISTINCT e.Name, e.CreateDate
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.136176] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:34
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Test SQL: SELECT 1
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_EntityValue'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:28.140176] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:34
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Test SQL: SELECT 1
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_EntityValue'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:28.152176] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:66
Status: SUCCESS
Original SQL: SELECT name, create_date, modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Test SQL: SELECT 1
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: modify_date, name, create_date
----------------------------------------

[2025-08-18T00:07:28.159226] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:66
Status: SUCCESS
Original SQL: SELECT name, create_date, modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Test SQL: SELECT 1
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: modify_date, name, create_date
----------------------------------------

[2025-08-18T00:07:28.166224] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:93
Status: SUCCESS
Original SQL: SELECT name FROM sys.procedures WHERE name = '{proc_name}'
Test SQL: SELECT 1 FROM sys.procedures WHERE name = '{proc_name}'
Result: SUCCESS - Tables: sys, Columns: name
----------------------------------------

[2025-08-18T00:07:28.173558] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:106
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-18T00:07:28.177626] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:106
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-18T00:07:28.180626] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:119
Status: FAILED
Original SQL: SELECT COUNT(*) as row_count FROM {table_name}
Test SQL: SELECT 1 FROM {table_name}
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.189324] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:9
Status: SUCCESS
Original SQL: SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-18T00:07:28.196738] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:17
Status: SUCCESS
Original SQL: SELECT TOP 5 * FROM REF_Entity WHERE Name LIKE 'Session_%'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name LIKE 'Session_%'
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T00:07:28.200412] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:26
Status: SUCCESS
Original SQL: SELECT TOP 10 * FROM REF_Entity ORDER BY CreateDate DESC
Test SQL: SELECT 1 FROM REF_Entity ORDER BY CreateDate DESC
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T00:07:28.203412] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:33
Status: FAILED
Original SQL: SELECT TOP 5 * FROM XRF_EntityAttributeValue
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:28.214123] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py:48
Status: SUCCESS
Original SQL: SELECT name, create_date 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 1 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: name, create_date
----------------------------------------

[2025-08-18T00:07:28.221742] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py:48
Status: SUCCESS
Original SQL: SELECT name, create_date 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 1 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: name, create_date
----------------------------------------

[2025-08-18T00:07:28.228743] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:113
Status: FAILED
Original SQL: SELECT @count = COUNT(*) FROM
Test SQL: SELECT @count = COUNT(*) FROM
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable "@count". (137) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.252255] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py:117
Status: FAILED
Original SQL: SELECT * FROM \1 ORDER BY
Test SQL: SELECT 1 FROM \1 ORDER BY
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '\\1'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:28.261254] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:37
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-18T00:07:28.264254] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:37
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-18T00:07:28.268504] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:77
Status: SUCCESS
Original SQL: SELECT TOP 1 * FROM REF_Entity
Test SQL: SELECT 1 FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T00:07:28.272503] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:87
Status: SUCCESS
Original SQL: SELECT TOP 1 * FROM REF_Entity
Test SQL: SELECT 1 FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T00:07:28.283553] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:268
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-18T00:07:28.287506] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:269
Status: SUCCESS
Original SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-18T00:07:28.331690] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:208
Status: FAILED
Original SQL: SELECT * FROM REF_Entity WHERE Name = \'test\'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = \'test\'
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:28.336187] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:209
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T00:07:28.340206] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:209
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T00:07:28.344493] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:210
Status: FAILED
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.377625] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:211
Status: FAILED
Original SQL: DELETE FROM REF_Entity WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.393709] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:212
Status: FAILED
Original SQL: SELECT COUNT(*) FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID
Test SQL: SELECT 1 FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:28.398708] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:218
Status: FAILED
Original SQL: SELECT your option FROM the menu
Test SQL: SELECT 1 FROM the menu
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:28.425287] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:228
Status: FAILED
Original SQL: SELECT * FROM
Test SQL: SELECT * FROM
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'FROM'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:28.432433] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:230
Status: FAILED
Original SQL: SELECT name FROM users WHERE active = 1 ORDER BY name
Test SQL: SELECT 1 FROM users WHERE active = 1 ORDER BY name
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:28.584555] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:109
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE 1=0
Test SQL: SELECT 1 FROM {table_name} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.600254] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:120
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE {where_clause}
Test SQL: SELECT 1 FROM {table_name} WHERE {where_clause}
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.605495] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:122
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE 1=0
Test SQL: SELECT 1 FROM {table_name} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.609519] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:133
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE {where_clause}
Test SQL: SELECT 1 FROM {table_name} WHERE {where_clause}
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.614519] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:135
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE 1=0
Test SQL: SELECT 1 FROM {table_name} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.619697] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:142
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE 1=0
Test SQL: SELECT 1 FROM {table_name} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.624704] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:250
Status: FAILED
Original SQL: SELECT 1 FROM {tables[0]} WHERE 1=0
Test SQL: SELECT 1 FROM {tables[0]} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.646804] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py:36
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-18T00:07:28.654566] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py:36
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-18T00:07:28.667565] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:111
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Test SQL: SELECT 1 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-18T00:07:28.672720] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:111
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Test SQL: SELECT 1 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-18T00:07:28.684719] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:131
Status: SUCCESS
Original SQL: SELECT COLUMN_NAME, DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Test SQL: SELECT 1 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: COLUMN_NAME, DATA_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T00:07:28.688768] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:131
Status: SUCCESS
Original SQL: SELECT COLUMN_NAME, DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Test SQL: SELECT 1 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: COLUMN_NAME, DATA_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T00:07:28.701552] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:12
Status: SUCCESS
Original SQL: SELECT 
            name,
            create_date,
            modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 
            1
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: modify_date, name, create_date
----------------------------------------

[2025-08-18T00:07:28.710476] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:12
Status: SUCCESS
Original SQL: SELECT 
            name,
            create_date,
            modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 
            1
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: modify_date, name, create_date
----------------------------------------

[2025-08-18T00:07:28.723015] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:83
Status: SUCCESS
Original SQL: SELECT name FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 1 FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: name
----------------------------------------

[2025-08-18T00:07:28.732195] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:83
Status: SUCCESS
Original SQL: SELECT name FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 1 FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: name
----------------------------------------

[2025-08-18T00:07:28.762569] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_db.py:11
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name LIKE 'Session_%'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name LIKE 'Session_%'
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T00:07:28.772589] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py:257
Status: SUCCESS
Original SQL: SELECT COUNT(*) as count FROM REF_Fact
Test SQL: SELECT 1 FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: COUNT, count
----------------------------------------

[2025-08-18T00:07:28.783869] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:79
Status: FAILED
Original SQL: SELECT 
                name,
                create_date,
                modify_date,
                type_desc
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Test SQL: SELECT 
                1
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.787549] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:79
Status: FAILED
Original SQL: SELECT 
                name,
                create_date,
                modify_date,
                type_desc
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Test SQL: SELECT 
                1
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.791900] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:129
Status: SUCCESS
Original SQL: SELECT TOP 5 ID, Name FROM REF_Entity ORDER BY ID
Test SQL: SELECT 1 FROM REF_Entity ORDER BY ID
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-18T00:07:28.796826] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:135
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)
                OUTPUT INSERTED.ID, INSERTED.Name
                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T00:07:28.800335] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:135
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)
                OUTPUT INSERTED.ID, INSERTED.Name
                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T00:07:28.825532] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:360
Status: SUCCESS
Original SQL: SELECT name FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Test SQL: SELECT 1 FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Result: SUCCESS - Tables: sys, Columns: name, type
----------------------------------------

[2025-08-18T00:07:28.833167] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:360
Status: SUCCESS
Original SQL: SELECT name FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Test SQL: SELECT 1 FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Result: SUCCESS - Tables: sys, Columns: name, type
----------------------------------------

[2025-08-18T00:07:28.844847] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:393
Status: SUCCESS
Original SQL: SELECT TABLE_NAME, TABLE_TYPE
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Test SQL: SELECT 1
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-18T00:07:28.849496] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:393
Status: SUCCESS
Original SQL: SELECT TABLE_NAME, TABLE_TYPE
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Test SQL: SELECT 1
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-18T00:07:28.855496] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_database.py:180
Status: FAILED
Original SQL: SELECT COUNT(*) as total_rows FROM [{schema_name}].[{table_name}]
Test SQL: SELECT 1 FROM [{schema_name}].[{table_name}]
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T00:07:28.874495] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:29
Status: SUCCESS
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Test SQL: SELECT 1
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: COLUMN_NAME, DATA_TYPE, IS_NULLABLE, TABLE_NAME
----------------------------------------

[2025-08-18T00:07:28.878496] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:29
Status: SUCCESS
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Test SQL: SELECT 1
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: COLUMN_NAME, DATA_TYPE, IS_NULLABLE, TABLE_NAME
----------------------------------------

[2025-08-18T00:07:28.884211] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:48
Status: SUCCESS
Original SQL: SELECT 
            COUNT(*) as TotalRows,
            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,
            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows
        FROM REF_EntityValue
Test SQL: SELECT 
            1
        FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: THEN, NumericValue, TotalRows, WHEN, COUNT, UnitsRows, NumericRows, END, ValueUnits, NULL, NOT, CASE, IS
----------------------------------------

[2025-08-18T00:07:28.888209] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:48
Status: SUCCESS
Original SQL: SELECT 
            COUNT(*) as TotalRows,
            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,
            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows
        FROM REF_EntityValue
Test SQL: SELECT 
            1
        FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: THEN, NumericValue, TotalRows, WHEN, COUNT, UnitsRows, NumericRows, END, ValueUnits, NULL, NOT, CASE, IS
----------------------------------------

[2025-08-18T00:07:28.894214] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:66
Status: SUCCESS
Original SQL: SELECT TOP 5
            Name,
            EntityValue,
            NumericValue,
            ValueUnits
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Test SQL: SELECT 1
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Result: SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, Name, EntityValue, NumericValue
----------------------------------------

[2025-08-18T00:07:28.898271] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:66
Status: SUCCESS
Original SQL: SELECT TOP 5
            Name,
            EntityValue,
            NumericValue,
            ValueUnits
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Test SQL: SELECT 1
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Result: SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, Name, EntityValue, NumericValue
----------------------------------------

[2025-08-18T00:07:28.902880] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:149
Status: FAILED
Original SQL: SELECT EntityValue, NumericValue, ValueUnits
            FROM REF_EntityValue 
            WHERE ID = ?
Test SQL: SELECT 1
            FROM REF_EntityValue 
            WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.907965] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:149
Status: FAILED
Original SQL: SELECT EntityValue, NumericValue, ValueUnits
            FROM REF_EntityValue 
            WHERE ID = ?
Test SQL: SELECT 1
            FROM REF_EntityValue 
            WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.917097] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py:56
Status: FAILED
Original SQL: SELECT name 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Test SQL: SELECT 1 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T00:07:28.920554] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py:56
Status: FAILED
Original SQL: SELECT name 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Test SQL: SELECT 1 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------


Session ended: 2025-08-18T00:07:28.933437
================================================================================
