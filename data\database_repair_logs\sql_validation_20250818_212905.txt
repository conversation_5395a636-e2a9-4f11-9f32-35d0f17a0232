SQL Validation Log - Session 20250818_212905
Started: 2025-08-18T21:29:05.126237
================================================================================

[2025-08-18T21:29:05.250202] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:109
Status: SUCCESS
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
               COUNT(e.ID) as EvidenceCount,
               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Test SQL: SELECT 1
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Result: SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: e, f, FLOAT, OverallAverage, CreateDate, OVER, EvidenceCount, COUNT, ValidityRating, Name, ID, DataSource, CAST, AVG
----------------------------------------

[2025-08-18T21:29:05.251900] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:109
Status: SUCCESS
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
               COUNT(e.ID) as EvidenceCount,
               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Test SQL: SELECT 1
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Result: SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: e, f, FLOAT, OverallAverage, CreateDate, OVER, EvidenceCount, COUNT, ValidityRating, Name, ID, DataSource, CAST, AVG
----------------------------------------

[2025-08-18T21:29:05.255194] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:194
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-18T21:29:05.257195] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:194
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-18T21:29:05.259196] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:206
Status: FAILED
Original SQL: SELECT ValidityRating FROM REF_Fact WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Fact WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.261195] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:218
Status: FAILED
Original SQL: SELECT COUNT(*) as EvidenceCount FROM REF_Evidence WHERE FactID = ?
Test SQL: SELECT 1 FROM REF_Evidence WHERE FactID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.263199] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:226
Status: FAILED
Original SQL: UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Fact WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.265199] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:260
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   'fact' as KnowledgeType
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.267198] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:260
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   'fact' as KnowledgeType
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.269383] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:276
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
                   'opinion' as KnowledgeType
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.271383] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:276
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
                   'opinion' as KnowledgeType
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.273568] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:292
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
                   f.ValidityRating, 'evidence' as KnowledgeType
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.275851] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:292
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
                   f.ValidityRating, 'evidence' as KnowledgeType
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.278832] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:329
Status: SUCCESS
Original SQL: SELECT 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END as Category,
            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Test SQL: SELECT 
            1
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Result: SUCCESS - Tables: REF_Fact, Columns: FLOAT, LIKE, CASE, THEN, Other, f, JSON, Category, ValidityRating, Configuration, AvgEffectiveness, ELSE, END, Name, AVG, Workflow, Database, CAST, WHEN, JSON_Parsing, Network
----------------------------------------

[2025-08-18T21:29:05.280832] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:329
Status: SUCCESS
Original SQL: SELECT 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END as Category,
            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Test SQL: SELECT 
            1
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Result: SUCCESS - Tables: REF_Fact, Columns: FLOAT, LIKE, CASE, THEN, Other, f, JSON, Category, ValidityRating, Configuration, AvgEffectiveness, ELSE, END, Name, AVG, Workflow, Database, CAST, WHEN, JSON_Parsing, Network
----------------------------------------

[2025-08-18T21:29:05.285831] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:241
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, ValidityRating, DataSource) 
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?)
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T21:29:05.288533] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:241
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, ValidityRating, DataSource) 
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?)
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T21:29:05.290536] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:267
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-18T21:29:05.292636] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:267
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_Evidence WHERE 1=0
Result: SUCCESS - Tables: REF_Evidence, Columns: 
----------------------------------------

[2025-08-18T21:29:05.295123] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:374
Status: FAILED
Original SQL: SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Test SQL: SELECT 1
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.301127] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:374
Status: FAILED
Original SQL: SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Test SQL: SELECT 1
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.303439] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:396
Status: FAILED
Original SQL: UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Fact WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.305443] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:410
Status: FAILED
Original SQL: SELECT ID FROM REF_Entity WHERE Name = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.308014] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:419
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.311014] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:419
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.313899] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:430
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
                FROM REF_Evidence e
                WHERE e.FactID = ?
Test SQL: SELECT 1
                FROM REF_Evidence e
                WHERE e.FactID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.316902] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:430
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
                FROM REF_Evidence e
                WHERE e.FactID = ?
Test SQL: SELECT 1
                FROM REF_Evidence e
                WHERE e.FactID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.318902] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:439
Status: FAILED
Original SQL: SELECT a.Name as AttributeName, ev.EntityValue, eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Test SQL: SELECT 1
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.321095] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:439
Status: FAILED
Original SQL: SELECT a.Name as AttributeName, ev.EntityValue, eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Test SQL: SELECT 1
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.323247] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:465
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.325635] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:465
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.329039] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:477
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.331044] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:477
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.333343] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:552
Status: FAILED
Original SQL: SELECT ID FROM REF_Category WHERE Name = ?
Test SQL: SELECT 1 FROM REF_Category WHERE Name = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.336964] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:559
Status: SUCCESS
Original SQL: INSERT INTO REF_Category (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT 1 FROM REF_Category WHERE 1=0
Result: SUCCESS - Tables: REF_Category, Columns: 
----------------------------------------

[2025-08-18T21:29:05.339337] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:566
Status: FAILED
Original SQL: SELECT ID FROM REF_Entity WHERE Name = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.340738] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:573
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:05.344714] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:580
Status: FAILED
Original SQL: SELECT ID FROM REF_Attribute WHERE Name = ?
Test SQL: SELECT 1 FROM REF_Attribute WHERE Name = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.346719] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:587
Status: SUCCESS
Original SQL: INSERT INTO REF_Attribute (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT 1 FROM REF_Attribute WHERE 1=0
Result: SUCCESS - Tables: REF_Attribute, Columns: 
----------------------------------------

[2025-08-18T21:29:05.349719] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:594
Status: FAILED
Original SQL: SELECT ID FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?
Test SQL: SELECT 1 FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.351718] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:602
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)
            OUTPUT INSERTED.ID
            VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-18T21:29:05.353744] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:602
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)
            OUTPUT INSERTED.ID
            VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-18T21:29:05.356640] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:610
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue) OUTPUT INSERTED.ID VALUES (?, ?)
Test SQL: SELECT 1 FROM REF_EntityValue WHERE 1=0
Result: SUCCESS - Tables: REF_EntityValue, Columns: 
----------------------------------------

[2025-08-18T21:29:05.360705] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:620
Status: FAILED
Original SQL: SELECT ID FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.366706] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:620
Status: FAILED
Original SQL: SELECT ID FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.368706] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:630
Status: FAILED
Original SQL: INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)
        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.372140] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:630
Status: FAILED
Original SQL: INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)
        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.380144] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:855
Status: FAILED
Original SQL: SELECT ID, Name, CreateDate
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Test SQL: SELECT 1
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.383144] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:855
Status: FAILED
Original SQL: SELECT ID, Name, CreateDate
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Test SQL: SELECT 1
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.385696] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:872
Status: FAILED
Original SQL: SELECT
                a.Name as AttributeName,
                ev.EntityValue as AttributeValue,
                ev.NumericValue,
                ev.ValueUnits,
                ev.Name as ValueName,
                eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Test SQL: SELECT
                1
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.387965] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:872
Status: FAILED
Original SQL: SELECT
                a.Name as AttributeName,
                ev.EntityValue as AttributeValue,
                ev.NumericValue,
                ev.ValueUnits,
                ev.Name as ValueName,
                eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Test SQL: SELECT
                1
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.390807] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:914
Status: FAILED
Original SQL: SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.393355] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:914
Status: FAILED
Original SQL: SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.397359] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:926
Status: FAILED
Original SQL: SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.400359] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:926
Status: FAILED
Original SQL: SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.402358] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:991
Status: FAILED
Original SQL: SELECT Name, ValidityRating, DataSource, CreateDate
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.405660] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:991
Status: FAILED
Original SQL: SELECT Name, ValidityRating, DataSource, CreateDate
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.409662] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1009
Status: FAILED
Original SQL: SELECT Name, ValidityRating, Opinion, DataSource, CreateDate
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.413016] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1009
Status: FAILED
Original SQL: SELECT Name, ValidityRating, Opinion, DataSource, CreateDate
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT 1
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.416020] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1021
Status: FAILED
Original SQL: SELECT DISTINCT e.Name, e.CreateDate
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.419497] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1021
Status: FAILED
Original SQL: SELECT DISTINCT e.Name, e.CreateDate
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Test SQL: SELECT 1
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.424500] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:34
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Test SQL: SELECT 1
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_EntityValue'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.427862] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:34
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Test SQL: SELECT 1
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_EntityValue'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.434122] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:66
Status: SUCCESS
Original SQL: SELECT name, create_date, modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Test SQL: SELECT 1
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: create_date, modify_date, name
----------------------------------------

[2025-08-18T21:29:05.440122] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:66
Status: SUCCESS
Original SQL: SELECT name, create_date, modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Test SQL: SELECT 1
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: create_date, modify_date, name
----------------------------------------

[2025-08-18T21:29:05.443123] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:93
Status: SUCCESS
Original SQL: SELECT name FROM sys.procedures WHERE name = '{proc_name}'
Test SQL: SELECT 1 FROM sys.procedures WHERE name = '{proc_name}'
Result: SUCCESS - Tables: sys, Columns: name
----------------------------------------

[2025-08-18T21:29:05.445501] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:106
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T21:29:05.448561] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:106
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T21:29:05.452853] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:119
Status: FAILED
Original SQL: SELECT COUNT(*) as row_count FROM {table_name}
Test SQL: SELECT 1 FROM {table_name}
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.456852] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:9
Status: SUCCESS
Original SQL: SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME
Test SQL: SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T21:29:05.459934] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:17
Status: SUCCESS
Original SQL: SELECT TOP 5 * FROM REF_Entity WHERE Name LIKE 'Session_%'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name LIKE 'Session_%'
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:05.462683] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:26
Status: SUCCESS
Original SQL: SELECT TOP 10 * FROM REF_Entity ORDER BY CreateDate DESC
Test SQL: SELECT 1 FROM REF_Entity ORDER BY CreateDate DESC
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:05.466047] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:33
Status: FAILED
Original SQL: SELECT TOP 5 * FROM XRF_EntityAttributeValue
Test SQL: SELECT 1 FROM XRF_EntityAttributeValue
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.474386] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py:48
Status: SUCCESS
Original SQL: SELECT name, create_date 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 1 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: create_date, name
----------------------------------------

[2025-08-18T21:29:05.479894] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py:48
Status: SUCCESS
Original SQL: SELECT name, create_date 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 1 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: create_date, name
----------------------------------------

[2025-08-18T21:29:05.484980] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:113
Status: FAILED
Original SQL: SELECT @count = COUNT(*) FROM
Test SQL: SELECT @count = COUNT(*) FROM
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable "@count". (137) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.505164] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py:117
Status: FAILED
Original SQL: SELECT * FROM \1 ORDER BY
Test SQL: SELECT 1 FROM \1 ORDER BY
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '\\1'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.509269] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:37
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T21:29:05.512713] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:37
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T21:29:05.516275] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:77
Status: SUCCESS
Original SQL: SELECT TOP 1 * FROM REF_Entity
Test SQL: SELECT 1 FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:05.519872] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:87
Status: SUCCESS
Original SQL: SELECT TOP 1 * FROM REF_Entity
Test SQL: SELECT 1 FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:05.527023] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:268
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-18T21:29:05.531026] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:269
Status: SUCCESS
Original SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-18T21:29:05.536464] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:31
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-18T21:29:05.539564] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:35
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) 
             VALUES (?, ?, ?)
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T21:29:05.542564] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:35
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) 
             VALUES (?, ?, ?)
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T21:29:05.546613] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:39
Status: FAILED
Original SQL: SELECT your option from the menu
Test SQL: SELECT 1 from the menu
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.552394] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:53
Status: FAILED
Original SQL: SELECT * FROM users WHERE active = 1
Test SQL: SELECT 1 FROM users WHERE active = 1
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.555395] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:54
Status: FAILED
Original SQL: UPDATE users SET status = ? WHERE id = ?
Test SQL: SELECT 1 FROM users WHERE id = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.559808] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:55
Status: FAILED
Original SQL: DELETE FROM logs WHERE created_at < ?
Test SQL: SELECT 1 FROM logs WHERE created_at < ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.618111] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:208
Status: FAILED
Original SQL: SELECT * FROM REF_Entity WHERE Name = \'test\'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = \'test\'
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.621611] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:209
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T21:29:05.625863] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:209
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T21:29:05.629862] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:210
Status: FAILED
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.633869] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:211
Status: FAILED
Original SQL: DELETE FROM REF_Entity WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.637870] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:212
Status: FAILED
Original SQL: SELECT COUNT(*) FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID
Test SQL: SELECT 1 FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.642157] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:218
Status: FAILED
Original SQL: SELECT your option FROM the menu
Test SQL: SELECT 1 FROM the menu
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.646392] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:228
Status: FAILED
Original SQL: SELECT * FROM
Test SQL: SELECT * FROM
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'FROM'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.650397] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:230
Status: FAILED
Original SQL: SELECT name FROM users WHERE active = 1 ORDER BY name
Test SQL: SELECT 1 FROM users WHERE active = 1 ORDER BY name
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.675465] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:109
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE 1=0
Test SQL: SELECT 1 FROM {table_name} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.680706] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:120
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE {where_clause}
Test SQL: SELECT 1 FROM {table_name} WHERE {where_clause}
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.685582] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:122
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE 1=0
Test SQL: SELECT 1 FROM {table_name} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.689777] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:133
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE {where_clause}
Test SQL: SELECT 1 FROM {table_name} WHERE {where_clause}
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.694313] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:135
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE 1=0
Test SQL: SELECT 1 FROM {table_name} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.698313] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:142
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE 1=0
Test SQL: SELECT 1 FROM {table_name} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.705311] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:250
Status: FAILED
Original SQL: SELECT 1 FROM {tables[0]} WHERE 1=0
Test SQL: SELECT 1 FROM {tables[0]} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.710918] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:39
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-18T21:29:05.716662] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:42
Status: FAILED
Original SQL: SELECT your option from the menu
Test SQL: SELECT 1 from the menu
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.720661] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:45
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) 
    VALUES (?, ?, ?)
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T21:29:05.724661] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:45
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) 
    VALUES (?, ?, ?)
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T21:29:05.728740] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:54
Status: FAILED
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.735739] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py:36
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T21:29:05.740741] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py:36
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Test SQL: SELECT 1 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T21:29:05.749827] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:111
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Test SQL: SELECT 1 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T21:29:05.755827] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:111
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Test SQL: SELECT 1 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T21:29:05.760828] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:131
Status: SUCCESS
Original SQL: SELECT COLUMN_NAME, DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Test SQL: SELECT 1 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: DATA_TYPE, COLUMN_NAME, TABLE_NAME
----------------------------------------

[2025-08-18T21:29:05.765835] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:131
Status: SUCCESS
Original SQL: SELECT COLUMN_NAME, DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Test SQL: SELECT 1 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: DATA_TYPE, COLUMN_NAME, TABLE_NAME
----------------------------------------

[2025-08-18T21:29:05.776834] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:12
Status: SUCCESS
Original SQL: SELECT 
            name,
            create_date,
            modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 
            1
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: create_date, modify_date, name
----------------------------------------

[2025-08-18T21:29:05.785905] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:12
Status: SUCCESS
Original SQL: SELECT 
            name,
            create_date,
            modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 
            1
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: create_date, modify_date, name
----------------------------------------

[2025-08-18T21:29:05.794407] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:83
Status: SUCCESS
Original SQL: SELECT name FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 1 FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: name
----------------------------------------

[2025-08-18T21:29:05.803473] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:83
Status: SUCCESS
Original SQL: SELECT name FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 1 FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: sys, Columns: name
----------------------------------------

[2025-08-18T21:29:05.825706] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:45
Status: FAILED
Original SQL: SELECT your option from the menu
Test SQL: SELECT 1 from the menu
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.829884] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:66
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT 1 FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:05.833884] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:67
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-18T21:29:05.837932] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:68
Status: SUCCESS
Original SQL: SELECT COUNT(*) FROM REF_Entity
Test SQL: SELECT 1 FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: COUNT
----------------------------------------

[2025-08-18T21:29:05.842933] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:71
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Value) VALUES (?, ?)
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:05.846597] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:72
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) VALUES (1, 'test', 'value')
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T21:29:05.851602] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:75
Status: FAILED
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.856648] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:76
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Status = 'active' WHERE Name = 'test'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-18T21:29:05.860648] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:79
Status: FAILED
Original SQL: DELETE FROM REF_Entity WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.865582] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:80
Status: FAILED
Original SQL: DELETE FROM REF_Entity WHERE CreatedDate < '2023-01-01'
Test SQL: SELECT 1 FROM REF_Entity WHERE CreatedDate < '2023-01-01'
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.872879] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:90
Status: FAILED
Original SQL: SELECT e.ID, e.Name, f.Value
    FROM REF_Entity e
    JOIN REF_Fact f ON e.ID = f.EntityID
    WHERE e.Status = 'active'
    ORDER BY e.Name
Test SQL: SELECT 1
    FROM REF_Entity e
    JOIN REF_Fact f ON e.ID = f.EntityID
    WHERE e.Status = 'active'
    ORDER BY e.Name
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)")
----------------------------------------

[2025-08-18T21:29:05.883469] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:90
Status: FAILED
Original SQL: SELECT e.ID, e.Name, f.Value
    FROM REF_Entity e
    JOIN REF_Fact f ON e.ID = f.EntityID
    WHERE e.Status = 'active'
    ORDER BY e.Name
Test SQL: SELECT 1
    FROM REF_Entity e
    JOIN REF_Fact f ON e.ID = f.EntityID
    WHERE e.Status = 'active'
    ORDER BY e.Name
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)")
----------------------------------------

[2025-08-18T21:29:05.890626] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:99
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, Status, CreatedDate)
    VALUES 
        ('Entity1', 'First entity', 'active', GETDATE()),
        ('Entity2', 'Second entity', 'inactive', GETDATE()),
        ('Entity3', 'Third entity', 'active', GETDATE())
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:05.896626] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:99
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, Status, CreatedDate)
    VALUES 
        ('Entity1', 'First entity', 'active', GETDATE()),
        ('Entity2', 'Second entity', 'inactive', GETDATE()),
        ('Entity3', 'Third entity', 'active', GETDATE())
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:05.904133] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:108
Status: FAILED
Original SQL: UPDATE REF_Entity 
    SET Status = 'archived',
        ModifiedDate = GETDATE()
    WHERE CreatedDate < DATEADD(year, -1, GETDATE())
        AND Status = 'inactive'
Test SQL: SELECT 1 FROM REF_Entity WHERE CreatedDate < DATEADD(year, -1, GETDATE())
        AND Status = 'inactive'
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)")
----------------------------------------

[2025-08-18T21:29:05.910641] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:108
Status: FAILED
Original SQL: UPDATE REF_Entity 
    SET Status = 'archived',
        ModifiedDate = GETDATE()
    WHERE CreatedDate < DATEADD(year, -1, GETDATE())
        AND Status = 'inactive'
Test SQL: SELECT 1 FROM REF_Entity WHERE CreatedDate < DATEADD(year, -1, GETDATE())
        AND Status = 'inactive'
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)")
----------------------------------------

[2025-08-18T21:29:05.917414] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:117
Status: FAILED
Original SQL: DELETE FROM REF_Fact 
    WHERE EntityID IN (
        SELECT ID FROM REF_Entity 
        WHERE Status = 'deleted'
    )
Test SQL: SELECT 1 FROM REF_Fact WHERE EntityID IN (
        SELECT ID FROM REF_Entity 
        WHERE Status = 'deleted'
    )
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)")
----------------------------------------

[2025-08-18T21:29:05.922199] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:117
Status: FAILED
Original SQL: DELETE FROM REF_Fact 
    WHERE EntityID IN (
        SELECT ID FROM REF_Entity 
        WHERE Status = 'deleted'
    )
Test SQL: SELECT 1 FROM REF_Fact WHERE EntityID IN (
        SELECT ID FROM REF_Entity 
        WHERE Status = 'deleted'
    )
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)")
----------------------------------------

[2025-08-18T21:29:05.928298] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:135
Status: FAILED
Original SQL: SELECT 
        e.ID,
        e.Name,
        e.Description,
        
        f.Value,
        f.CreatedDate
        
    FROM REF_Entity e
    
    LEFT JOIN REF_Fact f 
        ON e.ID = f.EntityID
        
    WHERE e.Status = 'active'
        AND f.Value IS NOT NULL
        
    ORDER BY 
        e.Name ASC,
        f.CreatedDate DESC
Test SQL: SELECT 
        1
        
    FROM REF_Entity e
    
    LEFT JOIN REF_Fact f 
        ON e.ID = f.EntityID
        
    WHERE e.Status = 'active'
        AND f.Value IS NOT NULL
        
    ORDER BY 
        e.Name ASC,
        f.CreatedDate DESC
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207)")
----------------------------------------

[2025-08-18T21:29:05.933297] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:135
Status: FAILED
Original SQL: SELECT 
        e.ID,
        e.Name,
        e.Description,
        
        f.Value,
        f.CreatedDate
        
    FROM REF_Entity e
    
    LEFT JOIN REF_Fact f 
        ON e.ID = f.EntityID
        
    WHERE e.Status = 'active'
        AND f.Value IS NOT NULL
        
    ORDER BY 
        e.Name ASC,
        f.CreatedDate DESC
Test SQL: SELECT 
        1
        
    FROM REF_Entity e
    
    LEFT JOIN REF_Fact f 
        ON e.ID = f.EntityID
        
    WHERE e.Status = 'active'
        AND f.Value IS NOT NULL
        
    ORDER BY 
        e.Name ASC,
        f.CreatedDate DESC
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207)")
----------------------------------------

[2025-08-18T21:29:05.938599] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:158
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity 
    (
        Name,
        Description,
        Status,
        CreatedDate
    )
    VALUES 
    (
        ?,
        ?,
        'active',
        GETDATE()
    )
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:05.943661] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:158
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity 
    (
        Name,
        Description,
        Status,
        CreatedDate
    )
    VALUES 
    (
        ?,
        ?,
        'active',
        GETDATE()
    )
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:05.948661] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:194
Status: SUCCESS
Original SQL: SELECT 'This contains SELECT keyword' FROM REF_Entity
Test SQL: SELECT 1 FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: keyword, SELECT, contains, This
----------------------------------------

[2025-08-18T21:29:05.952632] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:195
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name) VALUES ('UPDATE this later')
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: this, REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:05.957631] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:198
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity  -- This is a comment with SELECT
    WHERE Name = 'test'  /* Another comment with INSERT */
Test SQL: SELECT 1 FROM REF_Entity  -- This is a comment with SELECT
    WHERE Name = 'test'  /* Another comment with INSERT */
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-18T21:29:05.962681] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:198
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity  -- This is a comment with SELECT
    WHERE Name = 'test'  /* Another comment with INSERT */
Test SQL: SELECT 1 FROM REF_Entity  -- This is a comment with SELECT
    WHERE Name = 'test'  /* Another comment with INSERT */
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-18T21:29:05.967796] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:205
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-18T21:29:05.972874] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:208
Status: FAILED
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'It\\'s a test'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = 'It\\'s a test'
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 's'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Unclosed quotation mark after the character string ''. (105)")
----------------------------------------

[2025-08-18T21:29:05.978436] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:211
Status: FAILED
Original SQL: SELECT 
        e1.ID as Entity1_ID,
        e1.Name as Entity1_Name,
        e2.ID as Entity2_ID,
        e2.Name as Entity2_Name,
        f1.Value as Fact1_Value,
        f2.Value as Fact2_Value
    FROM REF_Entity e1
    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID
    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID
    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID
    WHERE e1.Status = 'active'
        AND f1.Type = 'primary'
        AND (e2.Status IS NULL OR e2.Status = 'active')
    ORDER BY e1.Name, e2.Name
    LIMIT 100
Test SQL: SELECT 
        1
    FROM REF_Entity e1
    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID
    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID
    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID
    WHERE e1.Status = 'active'
        AND f1.Type = 'primary'
        AND (e2.Status IS NULL OR e2.Status = 'active')
    ORDER BY e1.Name, e2.Name
    LIMIT 100
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'LIMIT'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.983440] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:211
Status: FAILED
Original SQL: SELECT 
        e1.ID as Entity1_ID,
        e1.Name as Entity1_Name,
        e2.ID as Entity2_ID,
        e2.Name as Entity2_Name,
        f1.Value as Fact1_Value,
        f2.Value as Fact2_Value
    FROM REF_Entity e1
    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID
    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID
    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID
    WHERE e1.Status = 'active'
        AND f1.Type = 'primary'
        AND (e2.Status IS NULL OR e2.Status = 'active')
    ORDER BY e1.Name, e2.Name
    LIMIT 100
Test SQL: SELECT 
        1
    FROM REF_Entity e1
    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID
    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID
    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID
    WHERE e1.Status = 'active'
        AND f1.Type = 'primary'
        AND (e2.Status IS NULL OR e2.Status = 'active')
    ORDER BY e1.Name, e2.Name
    LIMIT 100
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'LIMIT'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.988644] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:236
Status: FAILED
Original SQL: SELECT * FROM users WHERE id = ?
Test SQL: SELECT 1 FROM users WHERE id = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:05.994299] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:237
Status: FAILED
Original SQL: INSERT INTO users (name, email) VALUES (?, ?)
Test SQL: SELECT 1 FROM users WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:05.999298] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:238
Status: FAILED
Original SQL: UPDATE users SET email = ? WHERE id = ?
Test SQL: SELECT 1 FROM users WHERE id = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.004298] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:239
Status: FAILED
Original SQL: DELETE FROM users WHERE id = ?
Test SQL: SELECT 1 FROM users WHERE id = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.010368] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:243
Status: FAILED
Original SQL: SELECT COUNT(*) FROM transactions WHERE DATE(created_at) = CURDATE()
Test SQL: SELECT 1 FROM transactions WHERE DATE(created_at) = CURDATE()
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]'DATE' is not a recognized built-in function name. (195) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.015401] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:244
Status: FAILED
Original SQL: SELECT SUM(amount) FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())
Test SQL: SELECT 1 FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]'CURDATE' is not a recognized built-in function name. (195) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.019400] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:269
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT 1 FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:06.025098] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:285
Status: FAILED
Original SQL: SELECT * FROM
Test SQL: SELECT * FROM
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'FROM'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.029257] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:286
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:06.034118] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:287
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:06.039386] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:291
Status: FAILED
Original SQL: SELECT INSERT UPDATE DELETE FROM WHERE
Test SQL: SELECT 1 FROM WHERE
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'WHERE'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.045390] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:305
Status: FAILED
Original SQL: SELECT COUNT(*) FROM REF_Entity WHERE Status = 'test'
Test SQL: SELECT 1 FROM REF_Entity WHERE Status = 'test'
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.050435] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:309
Status: SUCCESS
Original SQL: SELECT 
        'Test completed successfully' as message,
        COUNT(*) as total_entities
    FROM REF_Entity
Test SQL: SELECT 
        1
    FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: COUNT, Test, message, completed, successfully, total_entities
----------------------------------------

[2025-08-18T21:29:06.055647] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:309
Status: SUCCESS
Original SQL: SELECT 
        'Test completed successfully' as message,
        COUNT(*) as total_entities
    FROM REF_Entity
Test SQL: SELECT 
        1
    FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: COUNT, Test, message, completed, successfully, total_entities
----------------------------------------

[2025-08-18T21:29:06.061812] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:21
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT 1 FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:06.067334] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:22
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity VALUES (?, ?)
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:06.073351] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:23
Status: FAILED
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.078352] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:32
Status: FAILED
Original SQL: SELECT * FROM REF_Entity 
WHERE Status = 'active'
Test SQL: SELECT 1 FROM REF_Entity 
WHERE Status = 'active'
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.083802] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:32
Status: FAILED
Original SQL: SELECT * FROM REF_Entity 
WHERE Status = 'active'
Test SQL: SELECT 1 FROM REF_Entity 
WHERE Status = 'active'
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.089783] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:27
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT 1 FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:06.094004] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:28
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-18T21:29:06.099061] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:29
Status: SUCCESS
Original SQL: SELECT COUNT(*) FROM REF_Entity
Test SQL: SELECT 1 FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: COUNT
----------------------------------------

[2025-08-18T21:29:06.105061] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:30
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Value) VALUES (?, ?)
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:06.110085] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:31
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) VALUES (1, 'test', 'value')
Test SQL: SELECT 1 FROM REF_Fact WHERE 1=0
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-18T21:29:06.115864] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:32
Status: FAILED
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.120867] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:33
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Status = 'active' WHERE Name = 'test'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-18T21:29:06.125938] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:34
Status: FAILED
Original SQL: DELETE FROM REF_Entity WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.131939] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:35
Status: FAILED
Original SQL: DELETE FROM REF_Entity WHERE CreatedDate < '2023-01-01'
Test SQL: SELECT 1 FROM REF_Entity WHERE CreatedDate < '2023-01-01'
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.137464] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:40
Status: SUCCESS
Original SQL: SELECT 'This contains SELECT keyword' FROM REF_Entity
Test SQL: SELECT 1 FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: keyword, SELECT, contains, This
----------------------------------------

[2025-08-18T21:29:06.142467] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:41
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name) VALUES ('UPDATE this later')
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: this, REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:06.148686] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:42
Status: FAILED
Original SQL: SELECT * FROM REF_Entity WHERE Name = \"test\"
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = \"test\"
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.153686] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:43
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-18T21:29:06.159076] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:44
Status: FAILED
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'It\\'s a test'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name = 'It\\'s a test'
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 's'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Unclosed quotation mark after the character string ''. (105)")
----------------------------------------

[2025-08-18T21:29:06.165078] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:47
Status: FAILED
Original SQL: SELECT * FROM users WHERE id = ?
Test SQL: SELECT 1 FROM users WHERE id = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.171666] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:48
Status: FAILED
Original SQL: INSERT INTO users (name, email) VALUES (?, ?)
Test SQL: SELECT 1 FROM users WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.177067] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:49
Status: FAILED
Original SQL: UPDATE users SET email = ? WHERE id = ?
Test SQL: SELECT 1 FROM users WHERE id = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.182991] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:50
Status: FAILED
Original SQL: DELETE FROM users WHERE id = ?
Test SQL: SELECT 1 FROM users WHERE id = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.189028] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:51
Status: FAILED
Original SQL: SELECT COUNT(*) FROM transactions WHERE DATE(created_at) = CURDATE()
Test SQL: SELECT 1 FROM transactions WHERE DATE(created_at) = CURDATE()
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]'DATE' is not a recognized built-in function name. (195) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.196032] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:52
Status: FAILED
Original SQL: SELECT SUM(amount) FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())
Test SQL: SELECT 1 FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]'CURDATE' is not a recognized built-in function name. (195) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.201501] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:67
Status: FAILED
Original SQL: SELECT your option from the menu
Test SQL: SELECT 1 from the menu
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.207833] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:72
Status: FAILED
Original SQL: SELECT * FROM
Test SQL: SELECT * FROM
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'FROM'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.212749] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:73
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:06.218026] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:74
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:06.226228] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:194
Status: FAILED
Original SQL: SELECT 
    e.ID,
    e.Name

FROM REF_Entity e

WHERE e.Status = 'active'

ORDER BY e.Name
Test SQL: SELECT 
    1

FROM REF_Entity e

WHERE e.Status = 'active'

ORDER BY e.Name
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.233329] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:194
Status: FAILED
Original SQL: SELECT 
    e.ID,
    e.Name

FROM REF_Entity e

WHERE e.Status = 'active'

ORDER BY e.Name
Test SQL: SELECT 
    1

FROM REF_Entity e

WHERE e.Status = 'active'

ORDER BY e.Name
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.239786] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:210
Status: FAILED
Original SQL: SELECT * FROM users WHERE name = 'John'
Test SQL: SELECT 1 FROM users WHERE name = 'John'
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.245543] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:218
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT 1 FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:06.251542] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:219
Status: FAILED
Original SQL: SELECT * FROM REF_Entity WHERE ID = ?
Test SQL: SELECT 1 FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.260408] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_db.py:11
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name LIKE 'Session_%'
Test SQL: SELECT 1 FROM REF_Entity WHERE Name LIKE 'Session_%'
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:06.269671] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py:257
Status: SUCCESS
Original SQL: SELECT COUNT(*) as count FROM REF_Fact
Test SQL: SELECT 1 FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: COUNT, count
----------------------------------------

[2025-08-18T21:29:06.282839] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:79
Status: FAILED
Original SQL: SELECT 
                name,
                create_date,
                modify_date,
                type_desc
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Test SQL: SELECT 
                1
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.288302] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:79
Status: FAILED
Original SQL: SELECT 
                name,
                create_date,
                modify_date,
                type_desc
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Test SQL: SELECT 
                1
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.294603] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:129
Status: SUCCESS
Original SQL: SELECT TOP 5 ID, Name FROM REF_Entity ORDER BY ID
Test SQL: SELECT 1 FROM REF_Entity ORDER BY ID
Result: SUCCESS - Tables: REF_Entity, Columns: Name, ID
----------------------------------------

[2025-08-18T21:29:06.299858] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:135
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)
                OUTPUT INSERTED.ID, INSERTED.Name
                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:06.305975] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:135
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)
                OUTPUT INSERTED.ID, INSERTED.Name
                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())
Test SQL: SELECT 1 FROM REF_Entity WHERE 1=0
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-18T21:29:06.310986] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:360
Status: SUCCESS
Original SQL: SELECT name FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Test SQL: SELECT 1 FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Result: SUCCESS - Tables: sys, Columns: type, name
----------------------------------------

[2025-08-18T21:29:06.318131] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:360
Status: SUCCESS
Original SQL: SELECT name FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Test SQL: SELECT 1 FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Result: SUCCESS - Tables: sys, Columns: type, name
----------------------------------------

[2025-08-18T21:29:06.322819] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:393
Status: SUCCESS
Original SQL: SELECT TABLE_NAME, TABLE_TYPE
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Test SQL: SELECT 1
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T21:29:06.329310] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:393
Status: SUCCESS
Original SQL: SELECT TABLE_NAME, TABLE_TYPE
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Test SQL: SELECT 1
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME
----------------------------------------

[2025-08-18T21:29:06.362903] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_database.py:180
Status: FAILED
Original SQL: SELECT COUNT(*) as total_rows FROM [{schema_name}].[{table_name}]
Test SQL: SELECT 1 FROM [{schema_name}].[{table_name}]
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.374141] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:29
Status: SUCCESS
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Test SQL: SELECT 1
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: DATA_TYPE, COLUMN_NAME, IS_NULLABLE, TABLE_NAME
----------------------------------------

[2025-08-18T21:29:06.382131] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:29
Status: SUCCESS
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Test SQL: SELECT 1
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: DATA_TYPE, COLUMN_NAME, IS_NULLABLE, TABLE_NAME
----------------------------------------

[2025-08-18T21:29:06.387251] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:48
Status: SUCCESS
Original SQL: SELECT 
            COUNT(*) as TotalRows,
            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,
            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows
        FROM REF_EntityValue
Test SQL: SELECT 
            1
        FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: NumericValue, NOT, NULL, ValueUnits, COUNT, CASE, UnitsRows, IS, THEN, WHEN, NumericRows, END, TotalRows
----------------------------------------

[2025-08-18T21:29:06.394104] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:48
Status: SUCCESS
Original SQL: SELECT 
            COUNT(*) as TotalRows,
            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,
            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows
        FROM REF_EntityValue
Test SQL: SELECT 
            1
        FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: NumericValue, NOT, NULL, ValueUnits, COUNT, CASE, UnitsRows, IS, THEN, WHEN, NumericRows, END, TotalRows
----------------------------------------

[2025-08-18T21:29:06.400147] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:66
Status: SUCCESS
Original SQL: SELECT TOP 5
            Name,
            EntityValue,
            NumericValue,
            ValueUnits
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Test SQL: SELECT 1
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Result: SUCCESS - Tables: REF_EntityValue, Columns: NumericValue, ValueUnits, EntityValue, Name
----------------------------------------

[2025-08-18T21:29:06.405891] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:66
Status: SUCCESS
Original SQL: SELECT TOP 5
            Name,
            EntityValue,
            NumericValue,
            ValueUnits
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Test SQL: SELECT 1
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Result: SUCCESS - Tables: REF_EntityValue, Columns: NumericValue, ValueUnits, EntityValue, Name
----------------------------------------

[2025-08-18T21:29:06.411888] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:149
Status: FAILED
Original SQL: SELECT EntityValue, NumericValue, ValueUnits
            FROM REF_EntityValue 
            WHERE ID = ?
Test SQL: SELECT 1
            FROM REF_EntityValue 
            WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.418234] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:149
Status: FAILED
Original SQL: SELECT EntityValue, NumericValue, ValueUnits
            FROM REF_EntityValue 
            WHERE ID = ?
Test SQL: SELECT 1
            FROM REF_EntityValue 
            WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.430234] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py:56
Status: FAILED
Original SQL: SELECT name 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Test SQL: SELECT 1 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.438813] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py:56
Status: FAILED
Original SQL: SELECT name 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Test SQL: SELECT 1 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.451587] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_server.py:276
Status: FAILED
Original SQL: SELECT TOP 20 
                FactID, FactType, FactText, ValidityRating, 
                CreatedDate, LastUpdated, Source
            FROM REF_FACT 
            WHERE FactText LIKE ? 
            AND ValidityRating >= ?
Test SQL: SELECT 1
            FROM REF_FACT 
            WHERE FactText LIKE ? 
            AND ValidityRating >= ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.458719] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_server.py:276
Status: FAILED
Original SQL: SELECT TOP 20 
                FactID, FactType, FactText, ValidityRating, 
                CreatedDate, LastUpdated, Source
            FROM REF_FACT 
            WHERE FactText LIKE ? 
            AND ValidityRating >= ?
Test SQL: SELECT 1
            FROM REF_FACT 
            WHERE FactText LIKE ? 
            AND ValidityRating >= ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.467303] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:139
Status: SUCCESS
Original SQL: SELECT 
                TABLE_SCHEMA,
                TABLE_NAME,
                TABLE_TYPE
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Test SQL: SELECT 
                1
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME, TABLE_SCHEMA
----------------------------------------

[2025-08-18T21:29:06.474835] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:139
Status: SUCCESS
Original SQL: SELECT 
                TABLE_SCHEMA,
                TABLE_NAME,
                TABLE_TYPE
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Test SQL: SELECT 
                1
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_TYPE, TABLE_NAME, TABLE_SCHEMA
----------------------------------------

[2025-08-18T21:29:06.481406] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:161
Status: SUCCESS
Original SQL: SELECT 
                TABLE_SCHEMA,
                TABLE_NAME
            FROM INFORMATION_SCHEMA.VIEWS
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Test SQL: SELECT 
                1
            FROM INFORMATION_SCHEMA.VIEWS
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_SCHEMA
----------------------------------------

[2025-08-18T21:29:06.488965] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:161
Status: SUCCESS
Original SQL: SELECT 
                TABLE_SCHEMA,
                TABLE_NAME
            FROM INFORMATION_SCHEMA.VIEWS
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Test SQL: SELECT 
                1
            FROM INFORMATION_SCHEMA.VIEWS
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: TABLE_NAME, TABLE_SCHEMA
----------------------------------------

[2025-08-18T21:29:06.497966] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:181
Status: SUCCESS
Original SQL: SELECT 
                ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'PROCEDURE'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Test SQL: SELECT 
                1
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'PROCEDURE'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: ROUTINE_TYPE, ROUTINE_SCHEMA, ROUTINE_NAME
----------------------------------------

[2025-08-18T21:29:06.507281] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:181
Status: SUCCESS
Original SQL: SELECT 
                ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'PROCEDURE'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Test SQL: SELECT 
                1
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'PROCEDURE'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: ROUTINE_TYPE, ROUTINE_SCHEMA, ROUTINE_NAME
----------------------------------------

[2025-08-18T21:29:06.514787] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:203
Status: SUCCESS
Original SQL: SELECT 
                ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'FUNCTION'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Test SQL: SELECT 
                1
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'FUNCTION'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: ROUTINE_TYPE, ROUTINE_SCHEMA, ROUTINE_NAME
----------------------------------------

[2025-08-18T21:29:06.524332] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:203
Status: SUCCESS
Original SQL: SELECT 
                ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'FUNCTION'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Test SQL: SELECT 
                1
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'FUNCTION'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Result: SUCCESS - Tables: INFORMATION_SCHEMA, Columns: ROUTINE_TYPE, ROUTINE_SCHEMA, ROUTINE_NAME
----------------------------------------

[2025-08-18T21:29:06.530332] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:255
Status: FAILED
Original SQL: SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                CHARACTER_MAXIMUM_LENGTH,
                NUMERIC_PRECISION,
                NUMERIC_SCALE,
                ORDINAL_POSITION
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?
            ORDER BY ORDINAL_POSITION
Test SQL: SELECT 
                1
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?
            ORDER BY ORDINAL_POSITION
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.536775] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:255
Status: FAILED
Original SQL: SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                CHARACTER_MAXIMUM_LENGTH,
                NUMERIC_PRECISION,
                NUMERIC_SCALE,
                ORDINAL_POSITION
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?
            ORDER BY ORDINAL_POSITION
Test SQL: SELECT 
                1
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?
            ORDER BY ORDINAL_POSITION
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.542754] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:287
Status: FAILED
Original SQL: SELECT
                i.name as index_name,
                i.type_desc as index_type,
                i.is_unique,
                i.is_primary_key,
                STRING_AGG(c.name, ', ') WITHIN GROUP (ORDER BY ic.key_ordinal) as columns
            FROM sys.indexes i
            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
            INNER JOIN sys.tables t ON i.object_id = t.object_id
            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
            WHERE t.name = ? AND s.name = ?
            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key
            ORDER BY i.name
Test SQL: SELECT
                1
            FROM sys.indexes i
            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
            INNER JOIN sys.tables t ON i.object_id = t.object_id
            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
            WHERE t.name = ? AND s.name = ?
            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key
            ORDER BY i.name
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.549057] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:287
Status: FAILED
Original SQL: SELECT
                i.name as index_name,
                i.type_desc as index_type,
                i.is_unique,
                i.is_primary_key,
                STRING_AGG(c.name, ', ') WITHIN GROUP (ORDER BY ic.key_ordinal) as columns
            FROM sys.indexes i
            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
            INNER JOIN sys.tables t ON i.object_id = t.object_id
            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
            WHERE t.name = ? AND s.name = ?
            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key
            ORDER BY i.name
Test SQL: SELECT
                1
            FROM sys.indexes i
            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
            INNER JOIN sys.tables t ON i.object_id = t.object_id
            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
            WHERE t.name = ? AND s.name = ?
            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key
            ORDER BY i.name
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.555058] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:318
Status: FAILED
Original SQL: SELECT
                fk.name as foreign_key_name,
                OBJECT_SCHEMA_NAME(fk.parent_object_id) as schema_name,
                OBJECT_NAME(fk.parent_object_id) as table_name,
                COL_NAME(fkc.parent_object_id, fkc.parent_column_id) as column_name,
                OBJECT_SCHEMA_NAME(fk.referenced_object_id) as referenced_schema,
                OBJECT_NAME(fk.referenced_object_id) as referenced_table,
                COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) as referenced_column
            FROM sys.foreign_keys fk
            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?
            ORDER BY fk.name
Test SQL: SELECT
                1
            FROM sys.foreign_keys fk
            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?
            ORDER BY fk.name
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.562123] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:318
Status: FAILED
Original SQL: SELECT
                fk.name as foreign_key_name,
                OBJECT_SCHEMA_NAME(fk.parent_object_id) as schema_name,
                OBJECT_NAME(fk.parent_object_id) as table_name,
                COL_NAME(fkc.parent_object_id, fkc.parent_column_id) as column_name,
                OBJECT_SCHEMA_NAME(fk.referenced_object_id) as referenced_schema,
                OBJECT_NAME(fk.referenced_object_id) as referenced_table,
                COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) as referenced_column
            FROM sys.foreign_keys fk
            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?
            ORDER BY fk.name
Test SQL: SELECT
                1
            FROM sys.foreign_keys fk
            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?
            ORDER BY fk.name
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-18T21:29:06.568401] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:348
Status: FAILED
Original SQL: SELECT COUNT(*) as row_count FROM [{schema_name}].[{table_name}]
Test SQL: SELECT 1 FROM [{schema_name}].[{table_name}]
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.575711] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:439
Status: FAILED
Original SQL: SELECT TOP {limit} * FROM [{schema_name}].[{table_name}]
Test SQL: SELECT 1 FROM [{schema_name}].[{table_name}]
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.605798] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\distlib\manifest.py:258
Status: FAILED
Original SQL: Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.

        Patterns are not quite the same as implemented by the 'fnmatch'
        module: '*' and '?'  match non-special characters, where "special"
        is platform-dependent: slash on Unix; colon, slash, and backslash on
        DOS/Windows; and colon on Mac OS.

        If 'anchor' is true (the default), then the pattern match is more
        stringent: "*.py" will match "foo.py" but not "foo/bar.py".  If
        'anchor' is false, both of these will match.

        If 'prefix' is supplied, then only filenames starting with 'prefix'
        (itself a pattern) and ending with 'pattern', with anything in between
        them, will match.  'anchor' is ignored in this case.

        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and
        'pattern' is assumed to be either a string containing a regex or a
        regex object -- no translation is done, the regex is just compiled
        and used as-is.

        Selected strings will be added to self.files.

        Return True if files are found.
Test SQL: Select 1 from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.

        Patterns are not quite the same as implemented by the 'fnmatch'
        module: '*' and '?'  match non-special characters, where "special"
        is platform-dependent: slash on Unix; colon, slash, and backslash on
        DOS/Windows; and colon on Mac OS.

        If 'anchor' is true (the default), then the pattern match is more
        stringent: "*.py" will match "foo.py" but not "foo/bar.py".  If
        'anchor' is false, both of these will match.

        If 'prefix' is supplied, then only filenames starting with 'prefix'
        (itself a pattern) and ending with 'pattern', with anything in between
        them, will match.  'anchor' is ignored in this case.

        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and
        'pattern' is assumed to be either a string containing a regex or a
        regex object -- no translation is done, the regex is just compiled
        and used as-is.

        Selected strings will be added to self.files.

        Return True if files are found.
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'self.files'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)")
----------------------------------------

[2025-08-18T21:29:06.613909] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\distlib\manifest.py:258
Status: FAILED
Original SQL: Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.

        Patterns are not quite the same as implemented by the 'fnmatch'
        module: '*' and '?'  match non-special characters, where
Test SQL: Select 1 from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.

        Patterns are not quite the same as implemented by the 'fnmatch'
        module: '*' and '?'  match non-special characters, where
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'self.files'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.636317] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:208
Status: FAILED
Original SQL: Update just the width, return a copy.

        Args:
            width (int): New width (sets both min_width and max_width)

        Returns:
            ~ConsoleOptions: New console options instance.
Test SQL: SELECT 1 FROM just WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.643318] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:208
Status: FAILED
Original SQL: Update just the width, return a copy.

        Args:
            width (int): New width (sets both min_width and max_width)

        Returns:
            ~ConsoleOptions: New console options instance.
Test SQL: SELECT 1 FROM just WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.651317] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:244
Status: FAILED
Original SQL: Update the width and height, and return a copy.

        Args:
            width (int): New width (sets both min_width and max_width).
            height (int): New height.

        Returns:
            ~ConsoleOptions: New console options instance.
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.657130] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:244
Status: FAILED
Original SQL: Update the width and height, and return a copy.

        Args:
            width (int): New width (sets both min_width and max_width).
            height (int): New height.

        Returns:
            ~ConsoleOptions: New console options instance.
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.670572] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:1789
Status: FAILED
Original SQL: Update the screen at a given offset.

        Args:
            renderable (RenderableType): A Rich renderable.
            region (Region, optional): Region of screen to update, or None for entire screen. Defaults to None.
            x (int, optional): x offset. Defaults to 0.
            y (int, optional): y offset. Defaults to 0.

        Raises:
            errors.NoAltScreen: If the Console isn't in alt screen mode.
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.677708] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:1789
Status: FAILED
Original SQL: Update the screen at a given offset.

        Args:
            renderable (RenderableType): A Rich renderable.
            region (Region, optional): Region of screen to update, or None for entire screen. Defaults to None.
            x (int, optional): x offset. Defaults to 0.
            y (int, optional): y offset. Defaults to 0.

        Raises:
            errors.NoAltScreen: If the Console isn't in alt screen mode.
Test SQL: SELECT 1 FROM the WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.684866] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:1819
Status: FAILED
Original SQL: Update lines of the screen at a given offset.

        Args:
            lines (List[List[Segment]]): Rendered lines (as produced by :meth:`~rich.Console.render_lines`).
            x (int, optional): x offset (column no). Defaults to 0.
            y (int, optional): y offset (column no). Defaults to 0.

        Raises:
            errors.NoAltScreen: If the Console isn't in alt screen mode.
Test SQL: SELECT 1 FROM lines WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.690865] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:1819
Status: FAILED
Original SQL: Update lines of the screen at a given offset.

        Args:
            lines (List[List[Segment]]): Rendered lines (as produced by :meth:`~rich.Console.render_lines`).
            x (int, optional): x offset (column no). Defaults to 0.
            y (int, optional): y offset (column no). Defaults to 0.

        Raises:
            errors.NoAltScreen: If the Console isn't in alt screen mode.
Test SQL: SELECT 1 FROM lines WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.728070] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\progress.py:1413
Status: FAILED
Original SQL: Update information associated with a task.

        Args:
            task_id (TaskID): Task id (returned by add_task).
            total (float, optional): Updates task.total if not None.
            completed (float, optional): Updates task.completed if not None.
            advance (float, optional): Add a value to task.completed if not None.
            description (str, optional): Change task description if not None.
            visible (bool, optional): Set visible flag if not None.
            refresh (bool): Force a refresh of progress information. Default is False.
            **fields (Any): Additional data fields required for rendering.
Test SQL: SELECT 1 FROM information WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.738083] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\progress.py:1413
Status: FAILED
Original SQL: Update information associated with a task.

        Args:
            task_id (TaskID): Task id (returned by add_task).
            total (float, optional): Updates task.total if not None.
            completed (float, optional): Updates task.completed if not None.
            advance (float, optional): Add a value to task.completed if not None.
            description (str, optional): Change task description if not None.
            visible (bool, optional): Set visible flag if not None.
            refresh (bool): Force a refresh of progress information. Default is False.
            **fields (Any): Additional data fields required for rendering.
Test SQL: SELECT 1 FROM information WHERE 1=0
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.832794] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_distutils\filelist.py:203
Status: FAILED
Original SQL: Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns
        are not quite the same as implemented by the 'fnmatch' module: '*'
        and '?'  match non-special characters, where "special" is platform-
        dependent: slash on Unix; colon, slash, and backslash on
        DOS/Windows; and colon on Mac OS.

        If 'anchor' is true (the default), then the pattern match is more
        stringent: "*.py" will match "foo.py" but not "foo/bar.py".  If
        'anchor' is false, both of these will match.

        If 'prefix' is supplied, then only filenames starting with 'prefix'
        (itself a pattern) and ending with 'pattern', with anything in between
        them, will match.  'anchor' is ignored in this case.

        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and
        'pattern' is assumed to be either a string containing a regex or a
        regex object -- no translation is done, the regex is just compiled
        and used as-is.

        Selected strings will be added to self.files.

        Return True if files are found, False otherwise.
Test SQL: Select 1 from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns
        are not quite the same as implemented by the 'fnmatch' module: '*'
        and '?'  match non-special characters, where "special" is platform-
        dependent: slash on Unix; colon, slash, and backslash on
        DOS/Windows; and colon on Mac OS.

        If 'anchor' is true (the default), then the pattern match is more
        stringent: "*.py" will match "foo.py" but not "foo/bar.py".  If
        'anchor' is false, both of these will match.

        If 'prefix' is supplied, then only filenames starting with 'prefix'
        (itself a pattern) and ending with 'pattern', with anything in between
        them, will match.  'anchor' is ignored in this case.

        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and
        'pattern' is assumed to be either a string containing a regex or a
        regex object -- no translation is done, the regex is just compiled
        and used as-is.

        Selected strings will be added to self.files.

        Return True if files are found, False otherwise.
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'self.files'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)")
----------------------------------------

[2025-08-18T21:29:06.841178] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_distutils\filelist.py:203
Status: FAILED
Original SQL: Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns
        are not quite the same as implemented by the 'fnmatch' module: '*'
        and '?'  match non-special characters, where
Test SQL: Select 1 from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns
        are not quite the same as implemented by the 'fnmatch' module: '*'
        and '?'  match non-special characters, where
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'self.files'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.851730] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_vendor\importlib_metadata\__init__.py:349
Status: FAILED
Original SQL: Select entry points from self that match the
        given parameters (typically group and/or name).
Test SQL: Select 1 from self that match the
        given parameters (typically group and/or name).
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'match'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-18T21:29:06.859465] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_vendor\importlib_metadata\__init__.py:349
Status: FAILED
Original SQL: Select entry points from self that match the
        given parameters (typically group and/or name).
Test SQL: Select 1 from self that match the
        given parameters (typically group and/or name).
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'match'. (102) (SQLExecDirectW)")
----------------------------------------


Session ended: 2025-08-18T21:29:06.884007
================================================================================
