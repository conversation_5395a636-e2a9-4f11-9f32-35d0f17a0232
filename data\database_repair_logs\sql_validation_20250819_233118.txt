SQL Validation Log - Session 20250819_233118
Started: 2025-08-19T23:31:18.752701
================================================================================

[2025-08-19T23:31:19.078797] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:109
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
               COUNT(e.ID) as EvidenceCount,
               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Test SQL: SELECT TOP 1 f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
               COUNT(e.ID) as EvidenceCount,
               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Column 'REF_Fact.ID' is invalid in the select list because it is not contained in either an aggregate function or the GROUP BY clause. (8120) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.078797] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:109
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
               COUNT(e.ID) as EvidenceCount,
               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Test SQL: SELECT TOP 1 f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
               COUNT(e.ID) as EvidenceCount,
               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Column 'REF_Fact.ID' is invalid in the select list because it is not contained in either an aggregate function or the GROUP BY clause. (8120) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.092329] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:194
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Evidence, Columns: Evidence, Name, DataSource, FactID
----------------------------------------

[2025-08-19T23:31:19.094831] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:194
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Evidence, Columns: Evidence, Name, DataSource, FactID
----------------------------------------

[2025-08-19T23:31:19.097638] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:206
Status: FAILED
Original SQL: SELECT ValidityRating FROM REF_Fact WHERE ID = ?
Test SQL: SELECT TOP 1 ValidityRating FROM REF_Fact WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.101918] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:218
Status: FAILED
Original SQL: SELECT COUNT(*) as EvidenceCount FROM REF_Evidence WHERE FactID = ?
Test SQL: SELECT TOP 1 COUNT(*) as EvidenceCount FROM REF_Evidence WHERE FactID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.104918] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:226
Status: FAILED
Original SQL: UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?
Test SQL: SELECT TOP 1 * FROM REF_Fact WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.106209] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:260
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   'fact' as KnowledgeType
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 1 f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   'fact' as KnowledgeType
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.109086] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:260
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   'fact' as KnowledgeType
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 1 f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   'fact' as KnowledgeType
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.113046] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:276
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
                   'opinion' as KnowledgeType
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT TOP 1 o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
                   'opinion' as KnowledgeType
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.116182] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:276
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
                   'opinion' as KnowledgeType
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT TOP 1 o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
                   'opinion' as KnowledgeType
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.121200] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:292
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
                   f.ValidityRating, 'evidence' as KnowledgeType
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 1 e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
                   f.ValidityRating, 'evidence' as KnowledgeType
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.122200] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:292
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
                   f.ValidityRating, 'evidence' as KnowledgeType
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 1 e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
                   f.ValidityRating, 'evidence' as KnowledgeType
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.126452] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:329
Status: SUCCESS
Original SQL: SELECT 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END as Category,
            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Test SQL: SELECT 
            TOP 1 CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END as Category,
            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Result: SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource
----------------------------------------

[2025-08-19T23:31:19.133013] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:329
Status: SUCCESS
Original SQL: SELECT 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END as Category,
            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Test SQL: SELECT 
            TOP 1 CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END as Category,
            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Result: SUCCESS - Tables: REF_Fact, Columns: ValidityRating, DataSource
----------------------------------------

[2025-08-19T23:31:19.139922] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:241
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, ValidityRating, DataSource) 
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: ValidityRating, Name, DataSource
----------------------------------------

[2025-08-19T23:31:19.144281] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:241
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, ValidityRating, DataSource) 
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: ValidityRating, Name, DataSource
----------------------------------------

[2025-08-19T23:31:19.144281] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:267
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Evidence, Columns: Evidence, Name, DataSource, FactID
----------------------------------------

[2025-08-19T23:31:19.147787] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:267
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Evidence, Columns: Evidence, Name, DataSource, FactID
----------------------------------------

[2025-08-19T23:31:19.152404] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:374
Status: FAILED
Original SQL: SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Test SQL: SELECT TOP 1 f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.154117] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:374
Status: FAILED
Original SQL: SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Test SQL: SELECT TOP 1 f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.157453] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:396
Status: FAILED
Original SQL: UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?
Test SQL: SELECT TOP 1 * FROM REF_Fact WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.159913] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:410
Status: FAILED
Original SQL: SELECT ID FROM REF_Entity WHERE Name = ?
Test SQL: SELECT TOP 1 ID FROM REF_Entity WHERE Name = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.162896] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:419
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Test SQL: SELECT TOP 1 f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.165223] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:419
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Test SQL: SELECT TOP 1 f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.168548] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:430
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
                FROM REF_Evidence e
                WHERE e.FactID = ?
Test SQL: SELECT TOP 1 e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
                FROM REF_Evidence e
                WHERE e.FactID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.170980] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:430
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
                FROM REF_Evidence e
                WHERE e.FactID = ?
Test SQL: SELECT TOP 1 e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
                FROM REF_Evidence e
                WHERE e.FactID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.173910] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:439
Status: FAILED
Original SQL: SELECT a.Name as AttributeName, ev.EntityValue, eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Test SQL: SELECT TOP 1 a.Name as AttributeName, ev.EntityValue, eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.177601] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:439
Status: FAILED
Original SQL: SELECT a.Name as AttributeName, ev.EntityValue, eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Test SQL: SELECT TOP 1 a.Name as AttributeName, ev.EntityValue, eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.180866] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:465
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Test SQL: SELECT TOP 1 f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.183236] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:465
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Test SQL: SELECT TOP 1 f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.186073] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:477
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT TOP 1 o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.186073] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:477
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT TOP 1 o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.191952] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:552
Status: FAILED
Original SQL: SELECT ID FROM REF_Category WHERE Name = ?
Test SQL: SELECT TOP 1 ID FROM REF_Category WHERE Name = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.194753] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:559
Status: SUCCESS
Original SQL: INSERT INTO REF_Category (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT TOP 0 * FROM REF_Category
Result: SUCCESS - Tables: REF_Category, Columns: Name
----------------------------------------

[2025-08-19T23:31:19.197752] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:566
Status: FAILED
Original SQL: SELECT ID FROM REF_Entity WHERE Name = ?
Test SQL: SELECT TOP 1 ID FROM REF_Entity WHERE Name = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.200192] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:573
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-19T23:31:19.203811] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:580
Status: FAILED
Original SQL: SELECT ID FROM REF_Attribute WHERE Name = ?
Test SQL: SELECT TOP 1 ID FROM REF_Attribute WHERE Name = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.207153] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:587
Status: SUCCESS
Original SQL: INSERT INTO REF_Attribute (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT TOP 0 * FROM REF_Attribute
Result: SUCCESS - Tables: REF_Attribute, Columns: Name
----------------------------------------

[2025-08-19T23:31:19.209686] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:594
Status: FAILED
Original SQL: SELECT ID FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?
Test SQL: SELECT TOP 1 ID FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.211546] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:602
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)
            OUTPUT INSERTED.ID
            VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, EntityValue, Name, NumericValue
----------------------------------------

[2025-08-19T23:31:19.211546] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:602
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)
            OUTPUT INSERTED.ID
            VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, EntityValue, Name, NumericValue
----------------------------------------

[2025-08-19T23:31:19.211546] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:610
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue) OUTPUT INSERTED.ID VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, Name
----------------------------------------

[2025-08-19T23:31:19.219906] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:620
Status: FAILED
Original SQL: SELECT ID FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Test SQL: SELECT TOP 1 ID FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.219906] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:620
Status: FAILED
Original SQL: SELECT ID FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Test SQL: SELECT TOP 1 ID FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.226941] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:630
Status: FAILED
Original SQL: INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)
        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM XRF_EntityAttributeValue
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.229806] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:630
Status: FAILED
Original SQL: INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)
        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM XRF_EntityAttributeValue
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.238783] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:855
Status: FAILED
Original SQL: SELECT ID, Name, CreateDate
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Test SQL: SELECT TOP 1 ID, Name, CreateDate
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.241750] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:855
Status: FAILED
Original SQL: SELECT ID, Name, CreateDate
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Test SQL: SELECT TOP 1 ID, Name, CreateDate
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.245465] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:872
Status: FAILED
Original SQL: SELECT
                a.Name as AttributeName,
                ev.EntityValue as AttributeValue,
                ev.NumericValue,
                ev.ValueUnits,
                ev.Name as ValueName,
                eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Test SQL: SELECT
                TOP 1 a.Name as AttributeName,
                ev.EntityValue as AttributeValue,
                ev.NumericValue,
                ev.ValueUnits,
                ev.Name as ValueName,
                eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.248272] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:872
Status: FAILED
Original SQL: SELECT
                a.Name as AttributeName,
                ev.EntityValue as AttributeValue,
                ev.NumericValue,
                ev.ValueUnits,
                ev.Name as ValueName,
                eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Test SQL: SELECT
                TOP 1 a.Name as AttributeName,
                ev.EntityValue as AttributeValue,
                ev.NumericValue,
                ev.ValueUnits,
                ev.Name as ValueName,
                eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.250711] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:914
Status: FAILED
Original SQL: SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 1 f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.253538] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:914
Status: FAILED
Original SQL: SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 1 f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.256992] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:926
Status: FAILED
Original SQL: SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Test SQL: SELECT TOP 1 e.Name, e.Evidence, e.DataSource, e.CreateDate
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.260422] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:926
Status: FAILED
Original SQL: SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Test SQL: SELECT TOP 1 e.Name, e.Evidence, e.DataSource, e.CreateDate
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.263803] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:991
Status: FAILED
Original SQL: SELECT Name, ValidityRating, DataSource, CreateDate
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT TOP 1 Name, ValidityRating, DataSource, CreateDate
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.266156] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:991
Status: FAILED
Original SQL: SELECT Name, ValidityRating, DataSource, CreateDate
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT TOP 1 Name, ValidityRating, DataSource, CreateDate
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.271486] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1009
Status: FAILED
Original SQL: SELECT Name, ValidityRating, Opinion, DataSource, CreateDate
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT TOP 1 Name, ValidityRating, Opinion, DataSource, CreateDate
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.274418] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1009
Status: FAILED
Original SQL: SELECT Name, ValidityRating, Opinion, DataSource, CreateDate
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT TOP 1 Name, ValidityRating, Opinion, DataSource, CreateDate
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.275418] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1021
Status: FAILED
Original SQL: SELECT DISTINCT e.Name, e.CreateDate
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Test SQL: SELECT TOP 1 DISTINCT e.Name, e.CreateDate
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.280362] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1021
Status: FAILED
Original SQL: SELECT DISTINCT e.Name, e.CreateDate
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Test SQL: SELECT TOP 1 DISTINCT e.Name, e.CreateDate
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.289405] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:34
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Test SQL: SELECT TOP 1 COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_EntityValue'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.292680] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:34
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Test SQL: SELECT TOP 1 COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'REF_EntityValue'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.300104] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:66
Status: SUCCESS
Original SQL: SELECT name, create_date, modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Test SQL: SELECT TOP 1 name, create_date, modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Result: SUCCESS - Tables: procedures, Columns: name, create_date, modify_date
----------------------------------------

[2025-08-19T23:31:19.304221] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:66
Status: SUCCESS
Original SQL: SELECT name, create_date, modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Test SQL: SELECT TOP 1 name, create_date, modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Result: SUCCESS - Tables: procedures, Columns: name, create_date, modify_date
----------------------------------------

[2025-08-19T23:31:19.305221] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:93
Status: SUCCESS
Original SQL: SELECT name FROM sys.procedures WHERE name = '{proc_name}'
Test SQL: SELECT TOP 1 name FROM sys.procedures WHERE name = '{proc_name}'
Result: SUCCESS - Tables: procedures, Columns: name
----------------------------------------

[2025-08-19T23:31:19.313736] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:106
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 1 TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: TABLES, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-19T23:31:19.319814] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:106
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 1 TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: TABLES, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-19T23:31:19.323042] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:119
Status: FAILED
Original SQL: SELECT COUNT(*) as row_count FROM {table_name}
Test SQL: SELECT TOP 1 COUNT(*) as row_count FROM {table_name}
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.329645] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:9
Status: SUCCESS
Original SQL: SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME
Test SQL: SELECT TOP 1 TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME
Result: SUCCESS - Tables: TABLES, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-19T23:31:19.333718] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:17
Status: FAILED
Original SQL: SELECT TOP 5 * FROM REF_Entity WHERE Name LIKE 'Session_%'
Test SQL: SELECT TOP 1 TOP 5 * FROM REF_Entity WHERE Name LIKE 'Session_%'
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'TOP'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.336718] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:26
Status: FAILED
Original SQL: SELECT TOP 10 * FROM REF_Entity ORDER BY CreateDate DESC
Test SQL: SELECT TOP 1 TOP 10 * FROM REF_Entity ORDER BY CreateDate DESC
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'TOP'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.341230] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:33
Status: FAILED
Original SQL: SELECT TOP 5 * FROM XRF_EntityAttributeValue
Test SQL: SELECT TOP 1 TOP 5 * FROM XRF_EntityAttributeValue
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'TOP'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.348187] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py:48
Status: SUCCESS
Original SQL: SELECT name, create_date 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT TOP 1 name, create_date 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: procedures, Columns: name, create_date
----------------------------------------

[2025-08-19T23:31:19.350187] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py:48
Status: SUCCESS
Original SQL: SELECT name, create_date 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT TOP 1 name, create_date 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: procedures, Columns: name, create_date
----------------------------------------

[2025-08-19T23:31:19.356409] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:113
Status: FAILED
Original SQL: SELECT @count = COUNT(*) FROM
Test SQL: SELECT @count = COUNT(*) FROM
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable "@count". (137) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.377364] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py:117
Status: FAILED
Original SQL: SELECT * FROM \1 ORDER BY
Test SQL: SELECT TOP 1 * FROM \1 ORDER BY
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '\\1'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.384730] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:37
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 1 TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: TABLES, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-19T23:31:19.387350] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:37
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 1 TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: TABLES, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-19T23:31:19.391941] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:77
Status: FAILED
Original SQL: SELECT TOP 1 * FROM REF_Entity
Test SQL: SELECT TOP 1 TOP 1 * FROM REF_Entity
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'TOP'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.397282] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:87
Status: FAILED
Original SQL: SELECT TOP 1 * FROM REF_Entity
Test SQL: SELECT TOP 1 TOP 1 * FROM REF_Entity
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'TOP'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.405705] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:268
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-19T23:31:19.409711] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:269
Status: SUCCESS
Original SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 1 1 FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-19T23:31:19.412004] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:31
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-19T23:31:19.416353] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:35
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) 
             VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Value, Name, EntityID
----------------------------------------

[2025-08-19T23:31:19.420786] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:35
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) 
             VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Value, Name, EntityID
----------------------------------------

[2025-08-19T23:31:19.425444] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:39
Status: FAILED
Original SQL: SELECT your option from the menu
Test SQL: SELECT TOP 1 your option from the menu
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'from'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.430448] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:53
Status: FAILED
Original SQL: SELECT * FROM users WHERE active = 1
Test SQL: SELECT TOP 1 * FROM users WHERE active = 1
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.434228] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:54
Status: FAILED
Original SQL: UPDATE users SET status = ? WHERE id = ?
Test SQL: SELECT TOP 1 * FROM users WHERE id = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.437228] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:55
Status: FAILED
Original SQL: DELETE FROM logs WHERE created_at < ?
Test SQL: SELECT TOP 1 * FROM logs WHERE created_at < ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.478163] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:208
Status: FAILED
Original SQL: SELECT * FROM REF_Entity WHERE Name = \'test\'
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE Name = \'test\'
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.481640] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:209
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Value, Name
----------------------------------------

[2025-08-19T23:31:19.486249] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:209
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Value, Name
----------------------------------------

[2025-08-19T23:31:19.490023] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:210
Status: FAILED
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.493023] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:211
Status: FAILED
Original SQL: DELETE FROM REF_Entity WHERE ID = ?
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.498058] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:212
Status: FAILED
Original SQL: SELECT COUNT(*) FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID
Test SQL: SELECT TOP 1 COUNT(*) FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.500360] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:218
Status: FAILED
Original SQL: SELECT your option FROM the menu
Test SQL: SELECT TOP 1 your option FROM the menu
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'FROM'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.506129] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:228
Status: FAILED
Original SQL: SELECT * FROM
Test SQL: SELECT * FROM
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'FROM'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.508714] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:230
Status: FAILED
Original SQL: SELECT name FROM users WHERE active = 1 ORDER BY name
Test SQL: SELECT TOP 1 name FROM users WHERE active = 1 ORDER BY name
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.536328] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:122
Status: FAILED
Original SQL: SELECT\s+(.+?)(?=\s+FROM)
Test SQL: SELECT\s+(.+?)(?=\s+FROM)
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.541126] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:145
Status: FAILED
Original SQL: DELETE FROM table
Test SQL: SELECT 1 FROM table WHERE 1=0
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'table'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.545132] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:158
Status: FAILED
Original SQL: SELECT Name FROM Users
Test SQL: SELECT TOP 1 Name FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.548608] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:576
Status: FAILED
Original SQL: SELECT TOP 0 * FROM {table_name}
Test SQL: SELECT TOP 1 TOP 0 * FROM {table_name}
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.551819] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:587
Status: FAILED
Original SQL: SELECT TOP 1 * FROM {table_name} WHERE {where_clause}
Test SQL: SELECT TOP 1 TOP 1 * FROM {table_name} WHERE {where_clause}
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.551819] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:589
Status: FAILED
Original SQL: SELECT TOP 0 * FROM {table_name}
Test SQL: SELECT TOP 1 TOP 0 * FROM {table_name}
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.562181] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:600
Status: FAILED
Original SQL: SELECT TOP 1 * FROM {table_name} WHERE {where_clause}
Test SQL: SELECT TOP 1 TOP 1 * FROM {table_name} WHERE {where_clause}
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.566654] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:602
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE 1=0
Test SQL: SELECT TOP 1 1 FROM {table_name} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.570769] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:609
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE 1=0
Test SQL: SELECT TOP 1 1 FROM {table_name} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.574108] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:717
Status: FAILED
Original SQL: SELECT 1 FROM {tables[0]} WHERE 1=0
Test SQL: SELECT TOP 1 1 FROM {tables[0]} WHERE 1=0
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.581203] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:39
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-19T23:31:19.585475] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:42
Status: FAILED
Original SQL: SELECT your option from the menu
Test SQL: SELECT TOP 1 your option from the menu
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'from'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.588231] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:45
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) 
    VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Value, Name, EntityID
----------------------------------------

[2025-08-19T23:31:19.593240] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:45
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) 
    VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Value, Name, EntityID
----------------------------------------

[2025-08-19T23:31:19.597703] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:54
Status: FAILED
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.608005] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py:36
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 1 TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: TABLES, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-19T23:31:19.616187] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py:36
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 1 TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Result: SUCCESS - Tables: TABLES, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-19T23:31:19.619427] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:111
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Test SQL: SELECT TOP 1 TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Result: SUCCESS - Tables: TABLES, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-19T23:31:19.632570] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:111
Status: SUCCESS
Original SQL: SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Test SQL: SELECT TOP 1 TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Result: SUCCESS - Tables: TABLES, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-19T23:31:19.643721] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:131
Status: SUCCESS
Original SQL: SELECT COLUMN_NAME, DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Test SQL: SELECT TOP 1 COLUMN_NAME, DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Result: SUCCESS - Tables: COLUMNS, Columns: DATA_TYPE, COLUMN_NAME, TABLE_NAME
----------------------------------------

[2025-08-19T23:31:19.647577] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:131
Status: SUCCESS
Original SQL: SELECT COLUMN_NAME, DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Test SQL: SELECT TOP 1 COLUMN_NAME, DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Result: SUCCESS - Tables: COLUMNS, Columns: DATA_TYPE, COLUMN_NAME, TABLE_NAME
----------------------------------------

[2025-08-19T23:31:19.651725] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:12
Status: SUCCESS
Original SQL: SELECT 
            name,
            create_date,
            modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 
            TOP 1 name,
            create_date,
            modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: procedures, Columns: name, create_date, modify_date
----------------------------------------

[2025-08-19T23:31:19.662817] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:12
Status: SUCCESS
Original SQL: SELECT 
            name,
            create_date,
            modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT 
            TOP 1 name,
            create_date,
            modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: procedures, Columns: name, create_date, modify_date
----------------------------------------

[2025-08-19T23:31:19.667917] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:83
Status: SUCCESS
Original SQL: SELECT name FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT TOP 1 name FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: procedures, Columns: name
----------------------------------------

[2025-08-19T23:31:19.677925] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:83
Status: SUCCESS
Original SQL: SELECT name FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT TOP 1 name FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Result: SUCCESS - Tables: procedures, Columns: name
----------------------------------------

[2025-08-19T23:31:19.701578] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:45
Status: FAILED
Original SQL: SELECT your option from the menu
Test SQL: SELECT TOP 1 your option from the menu
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'from'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.708166] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:66
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *
----------------------------------------

[2025-08-19T23:31:19.712992] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:67
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-19T23:31:19.715432] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:68
Status: SUCCESS
Original SQL: SELECT COUNT(*) FROM REF_Entity
Test SQL: SELECT TOP 1 COUNT(*) FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-19T23:31:19.720609] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:71
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Value) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Value, Name
----------------------------------------

[2025-08-19T23:31:19.724930] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:72
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) VALUES (1, 'test', 'value')
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Value, Name, EntityID
----------------------------------------

[2025-08-19T23:31:19.728937] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:75
Status: FAILED
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.739151] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:76
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Status = 'active' WHERE Name = 'test'
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: Status, Name
----------------------------------------

[2025-08-19T23:31:19.743643] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:79
Status: FAILED
Original SQL: DELETE FROM REF_Entity WHERE ID = ?
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.746292] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:80
Status: FAILED
Original SQL: DELETE FROM REF_Entity WHERE CreatedDate < '2023-01-01'
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE CreatedDate < '2023-01-01'
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.753978] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:90
Status: FAILED
Original SQL: SELECT e.ID, e.Name, f.Value
    FROM REF_Entity e
    JOIN REF_Fact f ON e.ID = f.EntityID
    WHERE e.Status = 'active'
    ORDER BY e.Name
Test SQL: SELECT TOP 1 e.ID, e.Name, f.Value
    FROM REF_Entity e
    JOIN REF_Fact f ON e.ID = f.EntityID
    WHERE e.Status = 'active'
    ORDER BY e.Name
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207)")
----------------------------------------

[2025-08-19T23:31:19.769841] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:90
Status: FAILED
Original SQL: SELECT e.ID, e.Name, f.Value
    FROM REF_Entity e
    JOIN REF_Fact f ON e.ID = f.EntityID
    WHERE e.Status = 'active'
    ORDER BY e.Name
Test SQL: SELECT TOP 1 e.ID, e.Name, f.Value
    FROM REF_Entity e
    JOIN REF_Fact f ON e.ID = f.EntityID
    WHERE e.Status = 'active'
    ORDER BY e.Name
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207)")
----------------------------------------

[2025-08-19T23:31:19.773818] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:99
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, Status, CreatedDate)
    VALUES 
        ('Entity1', 'First entity', 'active', GETDATE()),
        ('Entity2', 'Second entity', 'inactive', GETDATE()),
        ('Entity3', 'Third entity', 'active', GETDATE())
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreatedDate, Status, Name, Description
----------------------------------------

[2025-08-19T23:31:19.779522] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:99
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, Status, CreatedDate)
    VALUES 
        ('Entity1', 'First entity', 'active', GETDATE()),
        ('Entity2', 'Second entity', 'inactive', GETDATE()),
        ('Entity3', 'Third entity', 'active', GETDATE())
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreatedDate, Status, Name, Description
----------------------------------------

[2025-08-19T23:31:19.784942] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:108
Status: FAILED
Original SQL: UPDATE REF_Entity 
    SET Status = 'archived',
        ModifiedDate = GETDATE()
    WHERE CreatedDate < DATEADD(year, -1, GETDATE())
        AND Status = 'inactive'
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE CreatedDate < DATEADD(year, -1, GETDATE())
        AND Status = 'inactive'
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)")
----------------------------------------

[2025-08-19T23:31:19.790484] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:108
Status: FAILED
Original SQL: UPDATE REF_Entity 
    SET Status = 'archived',
        ModifiedDate = GETDATE()
    WHERE CreatedDate < DATEADD(year, -1, GETDATE())
        AND Status = 'inactive'
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE CreatedDate < DATEADD(year, -1, GETDATE())
        AND Status = 'inactive'
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)")
----------------------------------------

[2025-08-19T23:31:19.795364] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:117
Status: FAILED
Original SQL: DELETE FROM REF_Fact 
    WHERE EntityID IN (
        SELECT ID FROM REF_Entity 
        WHERE Status = 'deleted'
    )
Test SQL: SELECT TOP 1 * FROM REF_Fact WHERE EntityID IN (
        SELECT ID FROM REF_Entity 
        WHERE Status = 'deleted'
    )
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)")
----------------------------------------

[2025-08-19T23:31:19.800418] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:117
Status: FAILED
Original SQL: DELETE FROM REF_Fact 
    WHERE EntityID IN (
        SELECT ID FROM REF_Entity 
        WHERE Status = 'deleted'
    )
Test SQL: SELECT TOP 1 * FROM REF_Fact WHERE EntityID IN (
        SELECT ID FROM REF_Entity 
        WHERE Status = 'deleted'
    )
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207)")
----------------------------------------

[2025-08-19T23:31:19.805430] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:135
Status: FAILED
Original SQL: SELECT 
        e.ID,
        e.Name,
        e.Description,
        
        f.Value,
        f.CreatedDate
        
    FROM REF_Entity e
    
    LEFT JOIN REF_Fact f 
        ON e.ID = f.EntityID
        
    WHERE e.Status = 'active'
        AND f.Value IS NOT NULL
        
    ORDER BY 
        e.Name ASC,
        f.CreatedDate DESC
Test SQL: SELECT 
        TOP 1 e.ID,
        e.Name,
        e.Description,
        
        f.Value,
        f.CreatedDate
        
    FROM REF_Entity e
    
    LEFT JOIN REF_Fact f 
        ON e.ID = f.EntityID
        
    WHERE e.Status = 'active'
        AND f.Value IS NOT NULL
        
    ORDER BY 
        e.Name ASC,
        f.CreatedDate DESC
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Description'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207)")
----------------------------------------

[2025-08-19T23:31:19.811397] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:135
Status: FAILED
Original SQL: SELECT 
        e.ID,
        e.Name,
        e.Description,
        
        f.Value,
        f.CreatedDate
        
    FROM REF_Entity e
    
    LEFT JOIN REF_Fact f 
        ON e.ID = f.EntityID
        
    WHERE e.Status = 'active'
        AND f.Value IS NOT NULL
        
    ORDER BY 
        e.Name ASC,
        f.CreatedDate DESC
Test SQL: SELECT 
        TOP 1 e.ID,
        e.Name,
        e.Description,
        
        f.Value,
        f.CreatedDate
        
    FROM REF_Entity e
    
    LEFT JOIN REF_Fact f 
        ON e.ID = f.EntityID
        
    WHERE e.Status = 'active'
        AND f.Value IS NOT NULL
        
    ORDER BY 
        e.Name ASC,
        f.CreatedDate DESC
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Description'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Value'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207)")
----------------------------------------

[2025-08-19T23:31:19.811397] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:158
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity 
    (
        Name,
        Description,
        Status,
        CreatedDate
    )
    VALUES 
    (
        ?,
        ?,
        'active',
        GETDATE()
    )
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreatedDate, Status, Name, Description
----------------------------------------

[2025-08-19T23:31:19.811397] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:158
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity 
    (
        Name,
        Description,
        Status,
        CreatedDate
    )
    VALUES 
    (
        ?,
        ?,
        'active',
        GETDATE()
    )
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreatedDate, Status, Name, Description
----------------------------------------

[2025-08-19T23:31:19.811397] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:194
Status: SUCCESS
Original SQL: SELECT 'This contains SELECT keyword' FROM REF_Entity
Test SQL: SELECT TOP 1 'This contains SELECT keyword' FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: This, keyword, contains
----------------------------------------

[2025-08-19T23:31:19.831951] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:195
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name) VALUES ('UPDATE this later')
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-19T23:31:19.833060] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:198
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity  -- This is a comment with SELECT
    WHERE Name = 'test'  /* Another comment with INSERT */
Test SQL: SELECT TOP 1 * FROM REF_Entity  -- This is a comment with SELECT
    WHERE Name = 'test'  /* Another comment with INSERT */
Result: SUCCESS - Tables: REF_Entity, Columns: with, *, Name
----------------------------------------

[2025-08-19T23:31:19.833060] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:198
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity  -- This is a comment with SELECT
    WHERE Name = 'test'  /* Another comment with INSERT */
Test SQL: SELECT TOP 1 * FROM REF_Entity  -- This is a comment with SELECT
    WHERE Name = 'test'  /* Another comment with INSERT */
Result: SUCCESS - Tables: REF_Entity, Columns: with, *, Name
----------------------------------------

[2025-08-19T23:31:19.833060] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:205
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-19T23:31:19.848686] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:208
Status: FAILED
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'It\\'s a test'
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE Name = 'It\\'s a test'
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 's'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Unclosed quotation mark after the character string ''. (105)")
----------------------------------------

[2025-08-19T23:31:19.848686] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:211
Status: FAILED
Original SQL: SELECT 
        e1.ID as Entity1_ID,
        e1.Name as Entity1_Name,
        e2.ID as Entity2_ID,
        e2.Name as Entity2_Name,
        f1.Value as Fact1_Value,
        f2.Value as Fact2_Value
    FROM REF_Entity e1
    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID
    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID
    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID
    WHERE e1.Status = 'active'
        AND f1.Type = 'primary'
        AND (e2.Status IS NULL OR e2.Status = 'active')
    ORDER BY e1.Name, e2.Name
    LIMIT 100
Test SQL: SELECT 
        TOP 1 e1.ID as Entity1_ID,
        e1.Name as Entity1_Name,
        e2.ID as Entity2_ID,
        e2.Name as Entity2_Name,
        f1.Value as Fact1_Value,
        f2.Value as Fact2_Value
    FROM REF_Entity e1
    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID
    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID
    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID
    WHERE e1.Status = 'active'
        AND f1.Type = 'primary'
        AND (e2.Status IS NULL OR e2.Status = 'active')
    ORDER BY e1.Name, e2.Name
    LIMIT 100
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'LIMIT'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.862524] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:211
Status: FAILED
Original SQL: SELECT 
        e1.ID as Entity1_ID,
        e1.Name as Entity1_Name,
        e2.ID as Entity2_ID,
        e2.Name as Entity2_Name,
        f1.Value as Fact1_Value,
        f2.Value as Fact2_Value
    FROM REF_Entity e1
    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID
    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID
    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID
    WHERE e1.Status = 'active'
        AND f1.Type = 'primary'
        AND (e2.Status IS NULL OR e2.Status = 'active')
    ORDER BY e1.Name, e2.Name
    LIMIT 100
Test SQL: SELECT 
        TOP 1 e1.ID as Entity1_ID,
        e1.Name as Entity1_Name,
        e2.ID as Entity2_ID,
        e2.Name as Entity2_Name,
        f1.Value as Fact1_Value,
        f2.Value as Fact2_Value
    FROM REF_Entity e1
    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID
    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID
    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID
    WHERE e1.Status = 'active'
        AND f1.Type = 'primary'
        AND (e2.Status IS NULL OR e2.Status = 'active')
    ORDER BY e1.Name, e2.Name
    LIMIT 100
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'LIMIT'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.866370] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:236
Status: FAILED
Original SQL: SELECT * FROM users WHERE id = ?
Test SQL: SELECT TOP 1 * FROM users WHERE id = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.872765] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:237
Status: FAILED
Original SQL: INSERT INTO users (name, email) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.872765] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:238
Status: FAILED
Original SQL: UPDATE users SET email = ? WHERE id = ?
Test SQL: SELECT TOP 1 * FROM users WHERE id = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.883128] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:239
Status: FAILED
Original SQL: DELETE FROM users WHERE id = ?
Test SQL: SELECT TOP 1 * FROM users WHERE id = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.887270] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:243
Status: FAILED
Original SQL: SELECT COUNT(*) FROM transactions WHERE DATE(created_at) = CURDATE()
Test SQL: SELECT TOP 1 COUNT(*) FROM transactions WHERE DATE(created_at) = CURDATE()
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]'DATE' is not a recognized built-in function name. (195) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.894142] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:244
Status: FAILED
Original SQL: SELECT SUM(amount) FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())
Test SQL: SELECT TOP 1 SUM(amount) FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]'CURDATE' is not a recognized built-in function name. (195) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.899370] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:269
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *
----------------------------------------

[2025-08-19T23:31:19.903139] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:285
Status: FAILED
Original SQL: SELECT * FROM
Test SQL: SELECT * FROM
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'FROM'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.909548] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:286
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-19T23:31:19.914137] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:287
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-19T23:31:19.919836] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:291
Status: FAILED
Original SQL: SELECT INSERT UPDATE DELETE FROM WHERE
Test SQL: SELECT TOP 1 INSERT UPDATE DELETE FROM WHERE
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'INSERT'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.925797] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:305
Status: FAILED
Original SQL: SELECT COUNT(*) FROM REF_Entity WHERE Status = 'test'
Test SQL: SELECT TOP 1 COUNT(*) FROM REF_Entity WHERE Status = 'test'
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.930967] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:309
Status: SUCCESS
Original SQL: SELECT 
        'Test completed successfully' as message,
        COUNT(*) as total_entities
    FROM REF_Entity
Test SQL: SELECT 
        TOP 1 'Test completed successfully' as message,
        COUNT(*) as total_entities
    FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: successfully, Test, completed
----------------------------------------

[2025-08-19T23:31:19.938424] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:309
Status: SUCCESS
Original SQL: SELECT 
        'Test completed successfully' as message,
        COUNT(*) as total_entities
    FROM REF_Entity
Test SQL: SELECT 
        TOP 1 'Test completed successfully' as message,
        COUNT(*) as total_entities
    FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: successfully, Test, completed
----------------------------------------

[2025-08-19T23:31:19.945598] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:21
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *
----------------------------------------

[2025-08-19T23:31:19.948680] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:22
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-19T23:31:19.956014] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:23
Status: FAILED
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:19.962586] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:32
Status: FAILED
Original SQL: SELECT * FROM REF_Entity 
WHERE Status = 'active'
Test SQL: SELECT TOP 1 * FROM REF_Entity 
WHERE Status = 'active'
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.968461] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:32
Status: FAILED
Original SQL: SELECT * FROM REF_Entity 
WHERE Status = 'active'
Test SQL: SELECT TOP 1 * FROM REF_Entity 
WHERE Status = 'active'
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.975187] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:62
Status: FAILED
Original SQL: SELECT Name, ID FROM Users
Test SQL: SELECT TOP 1 Name, ID FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.979100] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:71
Status: FAILED
Original SQL: SELECT u.Name, u.ID FROM Users u
Test SQL: SELECT TOP 1 u.Name, u.ID FROM Users u
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.986167] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:80
Status: FAILED
Original SQL: SELECT u.Name, u.ID FROM Users AS u
Test SQL: SELECT TOP 1 u.Name, u.ID FROM Users AS u
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.992751] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:89
Status: FAILED
Original SQL: SELECT * FROM Products
Test SQL: SELECT TOP 1 * FROM Products
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Products'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:19.998656] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:98
Status: FAILED
Original SQL: SELECT * FROM Products WHERE Price > 100
Test SQL: SELECT TOP 1 * FROM Products WHERE Price > 100
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Products'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.003894] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:107
Status: FAILED
Original SQL: SELECT * FROM Products p WHERE p.Active = 1
Test SQL: SELECT TOP 1 * FROM Products p WHERE p.Active = 1
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Products'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.010996] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:116
Status: FAILED
Original SQL: SELECT Name, Email FROM Users WHERE Active = 1
Test SQL: SELECT TOP 1 Name, Email FROM Users WHERE Active = 1
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.016113] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:125
Status: FAILED
Original SQL: SELECT u.Name, u.Email FROM Users u WHERE u.Active = 1
Test SQL: SELECT TOP 1 u.Name, u.Email FROM Users u WHERE u.Active = 1
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.022966] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:135
Status: FAILED
Original SQL: SELECT u.Name FROM dbo.Users u WHERE u.Status = 'Active'
Test SQL: SELECT TOP 1 u.Name FROM dbo.Users u WHERE u.Status = 'Active'
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo.Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.022966] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:149
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u INNER JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 1 u.Name, p.Title FROM Users u INNER JOIN Posts p ON u.ID = p.UserID
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.037652] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:158
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 1 u.Name, p.Title FROM Users u JOIN Posts p ON u.ID = p.UserID
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.044308] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:168
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u LEFT JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 1 u.Name, p.Title FROM Users u LEFT JOIN Posts p ON u.ID = p.UserID
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.050074] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:177
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u LEFT OUTER JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 1 u.Name, p.Title FROM Users u LEFT OUTER JOIN Posts p ON u.ID = p.UserID
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.055885] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:187
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u RIGHT JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 1 u.Name, p.Title FROM Users u RIGHT JOIN Posts p ON u.ID = p.UserID
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.062523] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:197
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u FULL JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 1 u.Name, p.Title FROM Users u FULL JOIN Posts p ON u.ID = p.UserID
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.068281] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:207
Status: FAILED
Original SQL: SELECT u.Name, p.Title, c.Content FROM Users u JOIN Posts p ON u.ID = p.UserID JOIN Comments c ON p.ID = c.PostID
Test SQL: SELECT TOP 1 u.Name, p.Title, c.Content FROM Users u JOIN Posts p ON u.ID = p.UserID JOIN Comments c ON p.ID = c.PostID
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.073793] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:217
Status: FAILED
Original SQL: SELECT u.Name, p.Title, c.Content FROM Users u LEFT JOIN Posts p ON u.ID = p.UserID INNER JOIN Comments c ON p.ID = c.PostID
Test SQL: SELECT TOP 1 u.Name, p.Title, c.Content FROM Users u LEFT JOIN Posts p ON u.ID = p.UserID INNER JOIN Comments c ON p.ID = c.PostID
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.078648] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:227
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u, Posts p WHERE u.ID = p.UserID
Test SQL: SELECT TOP 1 u.Name, p.Title FROM Users u, Posts p WHERE u.ID = p.UserID
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.086640] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:236
Status: FAILED
Original SQL: SELECT t1.Name, t2.Title, t3.Content FROM Table1 t1, Table2 t2, Table3 t3 WHERE t1.ID = t2.T1_ID AND t2.ID = t3.T2_ID
Test SQL: SELECT TOP 1 t1.Name, t2.Title, t3.Content FROM Table1 t1, Table2 t2, Table3 t3 WHERE t1.ID = t2.T1_ID AND t2.ID = t3.T2_ID
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Table1'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.092275] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:250
Status: FAILED
Original SQL: SELECT Department, COUNT(*) as EmployeeCount FROM Employees GROUP BY Department
Test SQL: SELECT TOP 1 Department, COUNT(*) as EmployeeCount FROM Employees GROUP BY Department
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.097914] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:259
Status: FAILED
Original SQL: SELECT Department, JobTitle, COUNT(*) as Count FROM Employees GROUP BY Department, JobTitle
Test SQL: SELECT TOP 1 Department, JobTitle, COUNT(*) as Count FROM Employees GROUP BY Department, JobTitle
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.104055] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:269
Status: FAILED
Original SQL: SELECT Department, COUNT(*) as EmployeeCount FROM Employees GROUP BY Department HAVING COUNT(*) > 5
Test SQL: SELECT TOP 1 Department, COUNT(*) as EmployeeCount FROM Employees GROUP BY Department HAVING COUNT(*) > 5
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.109894] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:279
Status: FAILED
Original SQL: SELECT Name, Salary FROM Employees ORDER BY Salary DESC
Test SQL: SELECT TOP 1 Name, Salary FROM Employees ORDER BY Salary DESC
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.116212] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:288
Status: FAILED
Original SQL: SELECT Name, Department, Salary FROM Employees ORDER BY Department ASC, Salary DESC
Test SQL: SELECT TOP 1 Name, Department, Salary FROM Employees ORDER BY Department ASC, Salary DESC
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.122517] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:298
Status: FAILED
Original SQL: SELECT e.Department, COUNT(e.ID) as EmployeeCount, AVG(e.Salary) as AvgSalary FROM Employees e WHERE e.Active = 1 GROUP BY e.Department HAVING COUNT(e.ID) > 3 ORDER BY AvgSalary DESC
Test SQL: SELECT TOP 1 e.Department, COUNT(e.ID) as EmployeeCount, AVG(e.Salary) as AvgSalary FROM Employees e WHERE e.Active = 1 GROUP BY e.Department HAVING COUNT(e.ID) > 3 ORDER BY AvgSalary DESC
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.129113] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:308
Status: FAILED
Original SQL: SELECT d.Name, COUNT(e.ID) as EmployeeCount FROM Departments d LEFT JOIN Employees e ON d.ID = e.DepartmentID GROUP BY d.Name
Test SQL: SELECT TOP 1 d.Name, COUNT(e.ID) as EmployeeCount FROM Departments d LEFT JOIN Employees e ON d.ID = e.DepartmentID GROUP BY d.Name
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Departments'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.133112] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:318
Status: SUCCESS
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, COUNT(e.ID) as EvidenceCount, AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage FROM REF_Fact f LEFT JOIN REF_Evidence e ON f.ID = e.FactID WHERE f.DataSource LIKE '%Self-Healer%' GROUP BY f.ID, f.Name, f.ValidityRating
Test SQL: SELECT TOP 1 f.ID, f.Name, f.ValidityRating, COUNT(e.ID) as EvidenceCount, AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage FROM REF_Fact f LEFT JOIN REF_Evidence e ON f.ID = e.FactID WHERE f.DataSource LIKE '%Self-Healer%' GROUP BY f.ID, f.Name, f.ValidityRating
Result: SUCCESS - Tables: REF_Evidence, REF_Fact, Columns: FactID, ID, Name, ValidityRating, DataSource
----------------------------------------

[2025-08-19T23:31:20.142217] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:328
Status: FAILED
Original SQL: SELECT Name, Salary FROM Employees ORDER BY Salary DESC LIMIT 10
Test SQL: SELECT TOP 1 Name, Salary FROM Employees ORDER BY Salary DESC LIMIT 10
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'LIMIT'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.149525] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:342
Status: FAILED
Original SQL: INSERT INTO Users (Name, Email) VALUES ('John Doe', '<EMAIL>')
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.155303] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:351
Status: FAILED
Original SQL: INSERT INTO Users VALUES ('John Doe', '<EMAIL>', 1)
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.159177] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:361
Status: FAILED
Original SQL: INSERT INTO ActiveUsers (Name, Email) SELECT Name, Email FROM Users WHERE Active = 1
Test SQL: SELECT TOP 0 * FROM ActiveUsers
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'ActiveUsers'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.167483] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:371
Status: FAILED
Original SQL: INSERT INTO dbo.Users (Name, Email, Status) VALUES ('Jane Doe', '<EMAIL>', 'Active')
Test SQL: SELECT TOP 0 * FROM dbo
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.176763] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:385
Status: FAILED
Original SQL: UPDATE Users SET Name = 'Jane Doe' WHERE ID = 1
Test SQL: SELECT TOP 1 * FROM Users WHERE ID = 1
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.180765] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:394
Status: FAILED
Original SQL: UPDATE Users SET Name = 'Jane Doe', Email = '<EMAIL>', Status = 'Active' WHERE ID = 1
Test SQL: SELECT TOP 1 * FROM Users WHERE ID = 1
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.188538] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:404
Status: FAILED
Original SQL: UPDATE Users u SET u.Name = 'Jane Doe', u.Status = 'Active' WHERE u.ID = 1
Test SQL: SELECT TOP 1 * FROM Users WHERE u.ID = 1
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.195770] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:414
Status: FAILED
Original SQL: UPDATE Users u SET u.Status = 'Inactive' FROM Users u JOIN Departments d ON u.DepartmentID = d.ID WHERE d.Name = 'Closed'
Test SQL: SELECT TOP 1 * FROM Users WHERE d.Name = 'Closed'
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.200938] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:424
Status: FAILED
Original SQL: UPDATE dbo.Users SET Status = 'Inactive' WHERE LastLogin < '2023-01-01'
Test SQL: SELECT TOP 1 * FROM dbo WHERE LastLogin < '2023-01-01'
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.207429] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:438
Status: FAILED
Original SQL: DELETE FROM Users WHERE ID = 1
Test SQL: SELECT TOP 1 * FROM Users WHERE ID = 1
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.214553] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:447
Status: FAILED
Original SQL: DELETE FROM Users WHERE Status = 'Inactive' AND LastLogin < '2023-01-01'
Test SQL: SELECT TOP 1 * FROM Users WHERE Status = 'Inactive' AND LastLogin < '2023-01-01'
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.220172] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:457
Status: FAILED
Original SQL: DELETE FROM Users u WHERE u.Status = 'Inactive'
Test SQL: SELECT TOP 1 * FROM Users WHERE u.Status = 'Inactive'
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.224494] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:467
Status: FAILED
Original SQL: DELETE FROM dbo.Users WHERE Status = 'Deleted'
Test SQL: SELECT TOP 1 * FROM dbo WHERE Status = 'Deleted'
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.232330] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:481
Status: FAILED
Original SQL: SELECT    u.Name   ,   u.Email   FROM    Users   u   WHERE   u.Active   =   1
Test SQL: SELECT    TOP 1 u.Name   ,   u.Email   FROM    Users   u   WHERE   u.Active   =   1
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.238620] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:491
Status: FAILED
Original SQL: select u.Name, u.Email from Users u where u.Active = 1
Test SQL: select TOP 1 u.Name, u.Email from Users u where u.Active = 1
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.238620] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:501
Status: FAILED
Original SQL: SELECT rf.ID, rf.Name FROM REF_Fact rf JOIN XRF_Fact_Evidence xfe ON rf.ID = xfe.FactID
Test SQL: SELECT TOP 1 rf.ID, rf.Name FROM REF_Fact rf JOIN XRF_Fact_Evidence xfe ON rf.ID = xfe.FactID
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_Fact_Evidence'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.251711] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:511
Status: FAILED
Original SQL: SELECT t1.Field1, t2.Field2 FROM Table1 t1 JOIN Table2 t2 ON t1.ID = t2.Table1ID
Test SQL: SELECT TOP 1 t1.Field1, t2.Field2 FROM Table1 t1 JOIN Table2 t2 ON t1.ID = t2.Table1ID
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Table1'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.259024] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:521
Status: FAILED
Original SQL: SELECT employee.Name, department.Title FROM Employees employee JOIN Departments department ON employee.DeptID = department.ID
Test SQL: SELECT TOP 1 employee.Name, department.Title FROM Employees employee JOIN Departments department ON employee.DeptID = department.ID
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.273990] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:27
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *
----------------------------------------

[2025-08-19T23:31:20.287753] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:28
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-19T23:31:20.296050] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:29
Status: SUCCESS
Original SQL: SELECT COUNT(*) FROM REF_Entity
Test SQL: SELECT TOP 1 COUNT(*) FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-19T23:31:20.303710] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:30
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Value) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Value, Name
----------------------------------------

[2025-08-19T23:31:20.309925] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:31
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) VALUES (1, 'test', 'value')
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Value, Name, EntityID
----------------------------------------

[2025-08-19T23:31:20.318106] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:32
Status: FAILED
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.324277] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:33
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Status = 'active' WHERE Name = 'test'
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: Status, Name
----------------------------------------

[2025-08-19T23:31:20.329956] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:34
Status: FAILED
Original SQL: DELETE FROM REF_Entity WHERE ID = ?
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.336647] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:35
Status: FAILED
Original SQL: DELETE FROM REF_Entity WHERE CreatedDate < '2023-01-01'
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE CreatedDate < '2023-01-01'
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'CreatedDate'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.342446] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:40
Status: SUCCESS
Original SQL: SELECT 'This contains SELECT keyword' FROM REF_Entity
Test SQL: SELECT TOP 1 'This contains SELECT keyword' FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: This, keyword, contains
----------------------------------------

[2025-08-19T23:31:20.342446] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:41
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name) VALUES ('UPDATE this later')
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-19T23:31:20.342446] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:42
Status: FAILED
Original SQL: SELECT * FROM REF_Entity WHERE Name = \"test\"
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE Name = \"test\"
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.359813] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:43
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE Name = 'test'
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-19T23:31:20.365934] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:44
Status: FAILED
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'It\\'s a test'
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE Name = 'It\\'s a test'
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 's'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Unclosed quotation mark after the character string ''. (105)")
----------------------------------------

[2025-08-19T23:31:20.367935] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:47
Status: FAILED
Original SQL: SELECT * FROM users WHERE id = ?
Test SQL: SELECT TOP 1 * FROM users WHERE id = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.378004] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:48
Status: FAILED
Original SQL: INSERT INTO users (name, email) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.378004] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:49
Status: FAILED
Original SQL: UPDATE users SET email = ? WHERE id = ?
Test SQL: SELECT TOP 1 * FROM users WHERE id = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.391353] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:50
Status: FAILED
Original SQL: DELETE FROM users WHERE id = ?
Test SQL: SELECT TOP 1 * FROM users WHERE id = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.399276] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:51
Status: FAILED
Original SQL: SELECT COUNT(*) FROM transactions WHERE DATE(created_at) = CURDATE()
Test SQL: SELECT TOP 1 COUNT(*) FROM transactions WHERE DATE(created_at) = CURDATE()
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]'DATE' is not a recognized built-in function name. (195) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.404612] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:52
Status: FAILED
Original SQL: SELECT SUM(amount) FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())
Test SQL: SELECT TOP 1 SUM(amount) FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]'CURDATE' is not a recognized built-in function name. (195) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.410919] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:67
Status: FAILED
Original SQL: SELECT your option from the menu
Test SQL: SELECT TOP 1 your option from the menu
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'from'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.420010] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:72
Status: FAILED
Original SQL: SELECT * FROM
Test SQL: SELECT * FROM
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'FROM'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.426337] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:73
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-19T23:31:20.431380] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:74
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-19T23:31:20.431380] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:194
Status: FAILED
Original SQL: SELECT 
    e.ID,
    e.Name

FROM REF_Entity e

WHERE e.Status = 'active'

ORDER BY e.Name
Test SQL: SELECT 
    TOP 1 e.ID,
    e.Name

FROM REF_Entity e

WHERE e.Status = 'active'

ORDER BY e.Name
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.444193] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:194
Status: FAILED
Original SQL: SELECT 
    e.ID,
    e.Name

FROM REF_Entity e

WHERE e.Status = 'active'

ORDER BY e.Name
Test SQL: SELECT 
    TOP 1 e.ID,
    e.Name

FROM REF_Entity e

WHERE e.Status = 'active'

ORDER BY e.Name
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.446797] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:210
Status: FAILED
Original SQL: SELECT * FROM users WHERE name = 'John'
Test SQL: SELECT TOP 1 * FROM users WHERE name = 'John'
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.458416] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:218
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *
----------------------------------------

[2025-08-19T23:31:20.461920] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:219
Status: FAILED
Original SQL: SELECT * FROM REF_Entity WHERE ID = ?
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.474720] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_db.py:11
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name LIKE 'Session_%'
Test SQL: SELECT TOP 1 * FROM REF_Entity WHERE Name LIKE 'Session_%'
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-19T23:31:20.483038] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py:257
Status: SUCCESS
Original SQL: SELECT COUNT(*) as count FROM REF_Fact
Test SQL: SELECT TOP 1 COUNT(*) as count FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-19T23:31:20.499913] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:79
Status: FAILED
Original SQL: SELECT 
                name,
                create_date,
                modify_date,
                type_desc
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Test SQL: SELECT 
                TOP 1 name,
                create_date,
                modify_date,
                type_desc
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.505361] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:79
Status: FAILED
Original SQL: SELECT 
                name,
                create_date,
                modify_date,
                type_desc
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Test SQL: SELECT 
                TOP 1 name,
                create_date,
                modify_date,
                type_desc
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.513360] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:129
Status: FAILED
Original SQL: SELECT TOP 5 ID, Name FROM REF_Entity ORDER BY ID
Test SQL: SELECT TOP 1 TOP 5 ID, Name FROM REF_Entity ORDER BY ID
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'TOP'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.519702] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:135
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)
                OUTPUT INSERTED.ID, INSERTED.Name
                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreateDate, Name, Description, ModifyDate
----------------------------------------

[2025-08-19T23:31:20.525616] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:135
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)
                OUTPUT INSERTED.ID, INSERTED.Name
                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreateDate, Name, Description, ModifyDate
----------------------------------------

[2025-08-19T23:31:20.531453] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:360
Status: SUCCESS
Original SQL: SELECT name FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Test SQL: SELECT TOP 1 name FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Result: SUCCESS - Tables: objects, Columns: name, type
----------------------------------------

[2025-08-19T23:31:20.541282] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:360
Status: SUCCESS
Original SQL: SELECT name FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Test SQL: SELECT TOP 1 name FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Result: SUCCESS - Tables: objects, Columns: name, type
----------------------------------------

[2025-08-19T23:31:20.550054] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:393
Status: SUCCESS
Original SQL: SELECT TABLE_NAME, TABLE_TYPE
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Test SQL: SELECT TOP 1 TABLE_NAME, TABLE_TYPE
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Result: SUCCESS - Tables: TABLES, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-19T23:31:20.554027] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:393
Status: SUCCESS
Original SQL: SELECT TABLE_NAME, TABLE_TYPE
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Test SQL: SELECT TOP 1 TABLE_NAME, TABLE_TYPE
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Result: SUCCESS - Tables: TABLES, Columns: TABLE_NAME, TABLE_TYPE
----------------------------------------

[2025-08-19T23:31:20.560721] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_database.py:180
Status: FAILED
Original SQL: SELECT COUNT(*) as total_rows FROM [{schema_name}].[{table_name}]
Test SQL: SELECT TOP 1 COUNT(*) as total_rows FROM [{schema_name}].[{table_name}]
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.585262] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:29
Status: SUCCESS
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Test SQL: SELECT TOP 1 COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Result: SUCCESS - Tables: COLUMNS, Columns: DATA_TYPE, COLUMN_NAME, IS_NULLABLE, TABLE_NAME
----------------------------------------

[2025-08-19T23:31:20.589870] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:29
Status: SUCCESS
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Test SQL: SELECT TOP 1 COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Result: SUCCESS - Tables: COLUMNS, Columns: DATA_TYPE, COLUMN_NAME, IS_NULLABLE, TABLE_NAME
----------------------------------------

[2025-08-19T23:31:20.601016] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:48
Status: SUCCESS
Original SQL: SELECT 
            COUNT(*) as TotalRows,
            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,
            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows
        FROM REF_EntityValue
Test SQL: SELECT 
            TOP 1 COUNT(*) as TotalRows,
            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,
            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows
        FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue
----------------------------------------

[2025-08-19T23:31:20.604530] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:48
Status: SUCCESS
Original SQL: SELECT 
            COUNT(*) as TotalRows,
            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,
            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows
        FROM REF_EntityValue
Test SQL: SELECT 
            TOP 1 COUNT(*) as TotalRows,
            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,
            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows
        FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: ValueUnits, NumericValue
----------------------------------------

[2025-08-19T23:31:20.613017] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:66
Status: FAILED
Original SQL: SELECT TOP 5
            Name,
            EntityValue,
            NumericValue,
            ValueUnits
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Test SQL: SELECT TOP 1 TOP 5
            Name,
            EntityValue,
            NumericValue,
            ValueUnits
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'TOP'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.613017] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:66
Status: FAILED
Original SQL: SELECT TOP 5
            Name,
            EntityValue,
            NumericValue,
            ValueUnits
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Test SQL: SELECT TOP 1 TOP 5
            Name,
            EntityValue,
            NumericValue,
            ValueUnits
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'TOP'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.631842] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:149
Status: FAILED
Original SQL: SELECT EntityValue, NumericValue, ValueUnits
            FROM REF_EntityValue 
            WHERE ID = ?
Test SQL: SELECT TOP 1 EntityValue, NumericValue, ValueUnits
            FROM REF_EntityValue 
            WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.637336] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:149
Status: FAILED
Original SQL: SELECT EntityValue, NumericValue, ValueUnits
            FROM REF_EntityValue 
            WHERE ID = ?
Test SQL: SELECT TOP 1 EntityValue, NumericValue, ValueUnits
            FROM REF_EntityValue 
            WHERE ID = ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.649536] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py:56
Status: FAILED
Original SQL: SELECT name 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Test SQL: SELECT TOP 1 name 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.655830] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py:56
Status: FAILED
Original SQL: SELECT name 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Test SQL: SELECT TOP 1 name 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.671106] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_server.py:276
Status: FAILED
Original SQL: SELECT TOP 20 
                FactID, FactType, FactText, ValidityRating, 
                CreatedDate, LastUpdated, Source
            FROM REF_FACT 
            WHERE FactText LIKE ? 
            AND ValidityRating >= ?
Test SQL: SELECT TOP 1 TOP 20 
                FactID, FactType, FactText, ValidityRating, 
                CreatedDate, LastUpdated, Source
            FROM REF_FACT 
            WHERE FactText LIKE ? 
            AND ValidityRating >= ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.675622] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_server.py:276
Status: FAILED
Original SQL: SELECT TOP 20 
                FactID, FactType, FactText, ValidityRating, 
                CreatedDate, LastUpdated, Source
            FROM REF_FACT 
            WHERE FactText LIKE ? 
            AND ValidityRating >= ?
Test SQL: SELECT TOP 1 TOP 20 
                FactID, FactType, FactText, ValidityRating, 
                CreatedDate, LastUpdated, Source
            FROM REF_FACT 
            WHERE FactText LIKE ? 
            AND ValidityRating >= ?
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.691902] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:139
Status: SUCCESS
Original SQL: SELECT 
                TABLE_SCHEMA,
                TABLE_NAME,
                TABLE_TYPE
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Test SQL: SELECT 
                TOP 1 TABLE_SCHEMA,
                TABLE_NAME,
                TABLE_TYPE
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Result: SUCCESS - Tables: TABLES, Columns: TABLE_NAME, TABLE_SCHEMA, TABLE_TYPE
----------------------------------------

[2025-08-19T23:31:20.694424] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:139
Status: SUCCESS
Original SQL: SELECT 
                TABLE_SCHEMA,
                TABLE_NAME,
                TABLE_TYPE
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Test SQL: SELECT 
                TOP 1 TABLE_SCHEMA,
                TABLE_NAME,
                TABLE_TYPE
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Result: SUCCESS - Tables: TABLES, Columns: TABLE_NAME, TABLE_SCHEMA, TABLE_TYPE
----------------------------------------

[2025-08-19T23:31:20.709912] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:161
Status: SUCCESS
Original SQL: SELECT 
                TABLE_SCHEMA,
                TABLE_NAME
            FROM INFORMATION_SCHEMA.VIEWS
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Test SQL: SELECT 
                TOP 1 TABLE_SCHEMA,
                TABLE_NAME
            FROM INFORMATION_SCHEMA.VIEWS
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Result: SUCCESS - Tables: VIEWS, Columns: TABLE_NAME, TABLE_SCHEMA
----------------------------------------

[2025-08-19T23:31:20.715937] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:161
Status: SUCCESS
Original SQL: SELECT 
                TABLE_SCHEMA,
                TABLE_NAME
            FROM INFORMATION_SCHEMA.VIEWS
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Test SQL: SELECT 
                TOP 1 TABLE_SCHEMA,
                TABLE_NAME
            FROM INFORMATION_SCHEMA.VIEWS
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Result: SUCCESS - Tables: VIEWS, Columns: TABLE_NAME, TABLE_SCHEMA
----------------------------------------

[2025-08-19T23:31:20.737819] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:181
Status: SUCCESS
Original SQL: SELECT 
                ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'PROCEDURE'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Test SQL: SELECT 
                TOP 1 ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'PROCEDURE'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Result: SUCCESS - Tables: ROUTINES, Columns: ROUTINE_NAME, ROUTINE_TYPE, ROUTINE_SCHEMA
----------------------------------------

[2025-08-19T23:31:20.749876] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:181
Status: SUCCESS
Original SQL: SELECT 
                ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'PROCEDURE'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Test SQL: SELECT 
                TOP 1 ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'PROCEDURE'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Result: SUCCESS - Tables: ROUTINES, Columns: ROUTINE_NAME, ROUTINE_TYPE, ROUTINE_SCHEMA
----------------------------------------

[2025-08-19T23:31:20.752880] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:203
Status: SUCCESS
Original SQL: SELECT 
                ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'FUNCTION'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Test SQL: SELECT 
                TOP 1 ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'FUNCTION'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Result: SUCCESS - Tables: ROUTINES, Columns: ROUTINE_NAME, ROUTINE_TYPE, ROUTINE_SCHEMA
----------------------------------------

[2025-08-19T23:31:20.767311] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:203
Status: SUCCESS
Original SQL: SELECT 
                ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'FUNCTION'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Test SQL: SELECT 
                TOP 1 ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'FUNCTION'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Result: SUCCESS - Tables: ROUTINES, Columns: ROUTINE_NAME, ROUTINE_TYPE, ROUTINE_SCHEMA
----------------------------------------

[2025-08-19T23:31:20.780466] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:255
Status: FAILED
Original SQL: SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                CHARACTER_MAXIMUM_LENGTH,
                NUMERIC_PRECISION,
                NUMERIC_SCALE,
                ORDINAL_POSITION
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?
            ORDER BY ORDINAL_POSITION
Test SQL: SELECT 
                TOP 1 COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                CHARACTER_MAXIMUM_LENGTH,
                NUMERIC_PRECISION,
                NUMERIC_SCALE,
                ORDINAL_POSITION
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?
            ORDER BY ORDINAL_POSITION
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.790126] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:255
Status: FAILED
Original SQL: SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                CHARACTER_MAXIMUM_LENGTH,
                NUMERIC_PRECISION,
                NUMERIC_SCALE,
                ORDINAL_POSITION
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?
            ORDER BY ORDINAL_POSITION
Test SQL: SELECT 
                TOP 1 COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                CHARACTER_MAXIMUM_LENGTH,
                NUMERIC_PRECISION,
                NUMERIC_SCALE,
                ORDINAL_POSITION
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?
            ORDER BY ORDINAL_POSITION
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.800421] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:287
Status: FAILED
Original SQL: SELECT
                i.name as index_name,
                i.type_desc as index_type,
                i.is_unique,
                i.is_primary_key,
                STRING_AGG(c.name, ', ') WITHIN GROUP (ORDER BY ic.key_ordinal) as columns
            FROM sys.indexes i
            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
            INNER JOIN sys.tables t ON i.object_id = t.object_id
            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
            WHERE t.name = ? AND s.name = ?
            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key
            ORDER BY i.name
Test SQL: SELECT
                TOP 1 i.name as index_name,
                i.type_desc as index_type,
                i.is_unique,
                i.is_primary_key,
                STRING_AGG(c.name, ', ') WITHIN GROUP (ORDER BY ic.key_ordinal) as columns
            FROM sys.indexes i
            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
            INNER JOIN sys.tables t ON i.object_id = t.object_id
            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
            WHERE t.name = ? AND s.name = ?
            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key
            ORDER BY i.name
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.808331] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:287
Status: FAILED
Original SQL: SELECT
                i.name as index_name,
                i.type_desc as index_type,
                i.is_unique,
                i.is_primary_key,
                STRING_AGG(c.name, ', ') WITHIN GROUP (ORDER BY ic.key_ordinal) as columns
            FROM sys.indexes i
            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
            INNER JOIN sys.tables t ON i.object_id = t.object_id
            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
            WHERE t.name = ? AND s.name = ?
            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key
            ORDER BY i.name
Test SQL: SELECT
                TOP 1 i.name as index_name,
                i.type_desc as index_type,
                i.is_unique,
                i.is_primary_key,
                STRING_AGG(c.name, ', ') WITHIN GROUP (ORDER BY ic.key_ordinal) as columns
            FROM sys.indexes i
            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
            INNER JOIN sys.tables t ON i.object_id = t.object_id
            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
            WHERE t.name = ? AND s.name = ?
            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key
            ORDER BY i.name
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.813614] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:318
Status: FAILED
Original SQL: SELECT
                fk.name as foreign_key_name,
                OBJECT_SCHEMA_NAME(fk.parent_object_id) as schema_name,
                OBJECT_NAME(fk.parent_object_id) as table_name,
                COL_NAME(fkc.parent_object_id, fkc.parent_column_id) as column_name,
                OBJECT_SCHEMA_NAME(fk.referenced_object_id) as referenced_schema,
                OBJECT_NAME(fk.referenced_object_id) as referenced_table,
                COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) as referenced_column
            FROM sys.foreign_keys fk
            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?
            ORDER BY fk.name
Test SQL: SELECT
                TOP 1 fk.name as foreign_key_name,
                OBJECT_SCHEMA_NAME(fk.parent_object_id) as schema_name,
                OBJECT_NAME(fk.parent_object_id) as table_name,
                COL_NAME(fkc.parent_object_id, fkc.parent_column_id) as column_name,
                OBJECT_SCHEMA_NAME(fk.referenced_object_id) as referenced_schema,
                OBJECT_NAME(fk.referenced_object_id) as referenced_table,
                COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) as referenced_column
            FROM sys.foreign_keys fk
            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?
            ORDER BY fk.name
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.821600] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:318
Status: FAILED
Original SQL: SELECT
                fk.name as foreign_key_name,
                OBJECT_SCHEMA_NAME(fk.parent_object_id) as schema_name,
                OBJECT_NAME(fk.parent_object_id) as table_name,
                COL_NAME(fkc.parent_object_id, fkc.parent_column_id) as column_name,
                OBJECT_SCHEMA_NAME(fk.referenced_object_id) as referenced_schema,
                OBJECT_NAME(fk.referenced_object_id) as referenced_table,
                COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) as referenced_column
            FROM sys.foreign_keys fk
            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?
            ORDER BY fk.name
Test SQL: SELECT
                TOP 1 fk.name as foreign_key_name,
                OBJECT_SCHEMA_NAME(fk.parent_object_id) as schema_name,
                OBJECT_NAME(fk.parent_object_id) as table_name,
                COL_NAME(fkc.parent_object_id, fkc.parent_column_id) as column_name,
                OBJECT_SCHEMA_NAME(fk.referenced_object_id) as referenced_schema,
                OBJECT_NAME(fk.referenced_object_id) as referenced_table,
                COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) as referenced_column
            FROM sys.foreign_keys fk
            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?
            ORDER BY fk.name
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.831923] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:348
Status: FAILED
Original SQL: SELECT COUNT(*) as row_count FROM [{schema_name}].[{table_name}]
Test SQL: SELECT TOP 1 COUNT(*) as row_count FROM [{schema_name}].[{table_name}]
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.839573] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:439
Status: FAILED
Original SQL: SELECT TOP {limit} * FROM [{schema_name}].[{table_name}]
Test SQL: SELECT TOP 1 TOP {limit} * FROM [{schema_name}].[{table_name}]
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-19T23:31:20.870036] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\distlib\manifest.py:258
Status: FAILED
Original SQL: Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.

        Patterns are not quite the same as implemented by the 'fnmatch'
        module: '*' and '?'  match non-special characters, where "special"
        is platform-dependent: slash on Unix; colon, slash, and backslash on
        DOS/Windows; and colon on Mac OS.

        If 'anchor' is true (the default), then the pattern match is more
        stringent: "*.py" will match "foo.py" but not "foo/bar.py".  If
        'anchor' is false, both of these will match.

        If 'prefix' is supplied, then only filenames starting with 'prefix'
        (itself a pattern) and ending with 'pattern', with anything in between
        them, will match.  'anchor' is ignored in this case.

        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and
        'pattern' is assumed to be either a string containing a regex or a
        regex object -- no translation is done, the regex is just compiled
        and used as-is.

        Selected strings will be added to self.files.

        Return True if files are found.
Test SQL: Select TOP 1 strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.

        Patterns are not quite the same as implemented by the 'fnmatch'
        module: '*' and '?'  match non-special characters, where "special"
        is platform-dependent: slash on Unix; colon, slash, and backslash on
        DOS/Windows; and colon on Mac OS.

        If 'anchor' is true (the default), then the pattern match is more
        stringent: "*.py" will match "foo.py" but not "foo/bar.py".  If
        'anchor' is false, both of these will match.

        If 'prefix' is supplied, then only filenames starting with 'prefix'
        (itself a pattern) and ending with 'pattern', with anything in between
        them, will match.  'anchor' is ignored in this case.

        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and
        'pattern' is assumed to be either a string containing a regex or a
        regex object -- no translation is done, the regex is just compiled
        and used as-is.

        Selected strings will be added to self.files.

        Return True if files are found.
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)")
----------------------------------------

[2025-08-19T23:31:20.878269] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\distlib\manifest.py:258
Status: FAILED
Original SQL: Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.

        Patterns are not quite the same as implemented by the 'fnmatch'
        module: '*' and '?'  match non-special characters, where
Test SQL: Select TOP 1 strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.

        Patterns are not quite the same as implemented by the 'fnmatch'
        module: '*' and '?'  match non-special characters, where
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.901417] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:208
Status: FAILED
Original SQL: Update just the width, return a copy.

        Args:
            width (int): New width (sets both min_width and max_width)

        Returns:
            ~ConsoleOptions: New console options instance.
Test SQL: SELECT TOP 0 * FROM just
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.909333] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:208
Status: FAILED
Original SQL: Update just the width, return a copy.

        Args:
            width (int): New width (sets both min_width and max_width)

        Returns:
            ~ConsoleOptions: New console options instance.
Test SQL: SELECT TOP 0 * FROM just
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.916349] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:244
Status: FAILED
Original SQL: Update the width and height, and return a copy.

        Args:
            width (int): New width (sets both min_width and max_width).
            height (int): New height.

        Returns:
            ~ConsoleOptions: New console options instance.
Test SQL: SELECT TOP 0 * FROM the
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.924306] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:244
Status: FAILED
Original SQL: Update the width and height, and return a copy.

        Args:
            width (int): New width (sets both min_width and max_width).
            height (int): New height.

        Returns:
            ~ConsoleOptions: New console options instance.
Test SQL: SELECT TOP 0 * FROM the
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.931405] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:1789
Status: FAILED
Original SQL: Update the screen at a given offset.

        Args:
            renderable (RenderableType): A Rich renderable.
            region (Region, optional): Region of screen to update, or None for entire screen. Defaults to None.
            x (int, optional): x offset. Defaults to 0.
            y (int, optional): y offset. Defaults to 0.

        Raises:
            errors.NoAltScreen: If the Console isn't in alt screen mode.
Test SQL: SELECT TOP 0 * FROM the
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.941803] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:1789
Status: FAILED
Original SQL: Update the screen at a given offset.

        Args:
            renderable (RenderableType): A Rich renderable.
            region (Region, optional): Region of screen to update, or None for entire screen. Defaults to None.
            x (int, optional): x offset. Defaults to 0.
            y (int, optional): y offset. Defaults to 0.

        Raises:
            errors.NoAltScreen: If the Console isn't in alt screen mode.
Test SQL: SELECT TOP 0 * FROM the
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.949557] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:1819
Status: FAILED
Original SQL: Update lines of the screen at a given offset.

        Args:
            lines (List[List[Segment]]): Rendered lines (as produced by :meth:`~rich.Console.render_lines`).
            x (int, optional): x offset (column no). Defaults to 0.
            y (int, optional): y offset (column no). Defaults to 0.

        Raises:
            errors.NoAltScreen: If the Console isn't in alt screen mode.
Test SQL: SELECT TOP 0 * FROM lines
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.957478] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:1819
Status: FAILED
Original SQL: Update lines of the screen at a given offset.

        Args:
            lines (List[List[Segment]]): Rendered lines (as produced by :meth:`~rich.Console.render_lines`).
            x (int, optional): x offset (column no). Defaults to 0.
            y (int, optional): y offset (column no). Defaults to 0.

        Raises:
            errors.NoAltScreen: If the Console isn't in alt screen mode.
Test SQL: SELECT TOP 0 * FROM lines
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.971040] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\progress.py:1413
Status: FAILED
Original SQL: Update information associated with a task.

        Args:
            task_id (TaskID): Task id (returned by add_task).
            total (float, optional): Updates task.total if not None.
            completed (float, optional): Updates task.completed if not None.
            advance (float, optional): Add a value to task.completed if not None.
            description (str, optional): Change task description if not None.
            visible (bool, optional): Set visible flag if not None.
            refresh (bool): Force a refresh of progress information. Default is False.
            **fields (Any): Additional data fields required for rendering.
Test SQL: SELECT TOP 0 * FROM information
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:20.976431] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\progress.py:1413
Status: FAILED
Original SQL: Update information associated with a task.

        Args:
            task_id (TaskID): Task id (returned by add_task).
            total (float, optional): Updates task.total if not None.
            completed (float, optional): Updates task.completed if not None.
            advance (float, optional): Add a value to task.completed if not None.
            description (str, optional): Change task description if not None.
            visible (bool, optional): Set visible flag if not None.
            refresh (bool): Force a refresh of progress information. Default is False.
            **fields (Any): Additional data fields required for rendering.
Test SQL: SELECT TOP 0 * FROM information
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:21.049693] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_distutils\filelist.py:203
Status: FAILED
Original SQL: Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns
        are not quite the same as implemented by the 'fnmatch' module: '*'
        and '?'  match non-special characters, where "special" is platform-
        dependent: slash on Unix; colon, slash, and backslash on
        DOS/Windows; and colon on Mac OS.

        If 'anchor' is true (the default), then the pattern match is more
        stringent: "*.py" will match "foo.py" but not "foo/bar.py".  If
        'anchor' is false, both of these will match.

        If 'prefix' is supplied, then only filenames starting with 'prefix'
        (itself a pattern) and ending with 'pattern', with anything in between
        them, will match.  'anchor' is ignored in this case.

        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and
        'pattern' is assumed to be either a string containing a regex or a
        regex object -- no translation is done, the regex is just compiled
        and used as-is.

        Selected strings will be added to self.files.

        Return True if files are found, False otherwise.
Test SQL: Select TOP 1 strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns
        are not quite the same as implemented by the 'fnmatch' module: '*'
        and '?'  match non-special characters, where "special" is platform-
        dependent: slash on Unix; colon, slash, and backslash on
        DOS/Windows; and colon on Mac OS.

        If 'anchor' is true (the default), then the pattern match is more
        stringent: "*.py" will match "foo.py" but not "foo/bar.py".  If
        'anchor' is false, both of these will match.

        If 'prefix' is supplied, then only filenames starting with 'prefix'
        (itself a pattern) and ending with 'pattern', with anything in between
        them, will match.  'anchor' is ignored in this case.

        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and
        'pattern' is assumed to be either a string containing a regex or a
        regex object -- no translation is done, the regex is just compiled
        and used as-is.

        Selected strings will be added to self.files.

        Return True if files are found, False otherwise.
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)")
----------------------------------------

[2025-08-19T23:31:21.064203] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_distutils\filelist.py:203
Status: FAILED
Original SQL: Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns
        are not quite the same as implemented by the 'fnmatch' module: '*'
        and '?'  match non-special characters, where
Test SQL: Select TOP 1 strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns
        are not quite the same as implemented by the 'fnmatch' module: '*'
        and '?'  match non-special characters, where
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:21.076124] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_vendor\importlib_metadata\__init__.py:349
Status: FAILED
Original SQL: Select entry points from self that match the
        given parameters (typically group and/or name).
Test SQL: Select TOP 1 entry points from self that match the
        given parameters (typically group and/or name).
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'match'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-19T23:31:21.087111] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_vendor\importlib_metadata\__init__.py:349
Status: FAILED
Original SQL: Select entry points from self that match the
        given parameters (typically group and/or name).
Test SQL: Select TOP 1 entry points from self that match the
        given parameters (typically group and/or name).
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'match'. (102) (SQLExecDirectW)")
----------------------------------------


Session ended: 2025-08-19T23:31:21.112750
================================================================================
