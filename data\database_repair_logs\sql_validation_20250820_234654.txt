SQL Validation Log - Session 20250820_234654
Started: 2025-08-20T23:46:54.113614
================================================================================

[2025-08-20T23:46:54.416550] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:109
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
               COUNT(e.ID) as EvidenceCount,
               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Test SQL: SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
               COUNT(e.ID) as EvidenceCount,
               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%') AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Column 'REF_Fact.ID' is invalid in the select list because it is not contained in either an aggregate function or the GROUP BY clause. (8120) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.416550] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:109
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
               COUNT(e.ID) as EvidenceCount,
               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Test SQL: SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
               COUNT(e.ID) as EvidenceCount,
               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%') AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Column 'REF_Fact.ID' is invalid in the select list because it is not contained in either an aggregate function or the GROUP BY clause. (8120) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.431370] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:194
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Evidence, Columns: FactID, Evidence, DataSource, Name
----------------------------------------

[2025-08-20T23:46:54.433871] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:194
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Evidence, Columns: FactID, Evidence, DataSource, Name
----------------------------------------

[2025-08-20T23:46:54.433871] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:206
Status: SUCCESS
Original SQL: SELECT ValidityRating FROM REF_Fact WHERE ID = ?
Test SQL: SELECT TOP 1 * FROM (SELECT ValidityRating FROM REF_Fact WHERE ID IS NOT NULL) AS test_query
Result: SUCCESS - Tables: REF_Fact, Columns: ID, ValidityRating
----------------------------------------

[2025-08-20T23:46:54.433871] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:218
Status: SUCCESS
Original SQL: SELECT COUNT(*) as EvidenceCount FROM REF_Evidence WHERE FactID = ?
Test SQL: SELECT TOP 1 * FROM (SELECT COUNT(*) as EvidenceCount FROM REF_Evidence WHERE FactID IS NOT NULL) AS test_query
Result: SUCCESS - Tables: REF_Evidence, Columns: FactID
----------------------------------------

[2025-08-20T23:46:54.433871] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:226
Status: SUCCESS
Original SQL: UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: ID, ValidityRating
----------------------------------------

[2025-08-20T23:46:54.433871] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:260
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   'fact' as KnowledgeType
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   'fact' as KnowledgeType
            FROM REF_Fact f
            WHERE (f.Name LIKE '%' OR f.DataSource LIKE '%')
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating > IS NOT NULL
            ORDER BY f.ValidityRating DESC) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'IS'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.448388] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:260
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   'fact' as KnowledgeType
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   'fact' as KnowledgeType
            FROM REF_Fact f
            WHERE (f.Name LIKE '%' OR f.DataSource LIKE '%')
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating > IS NOT NULL
            ORDER BY f.ValidityRating DESC) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'IS'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.451603] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:276
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
                   'opinion' as KnowledgeType
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT TOP 1 * FROM (SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
                   'opinion' as KnowledgeType
            FROM REF_Opinion o
            WHERE (o.Name LIKE '%' OR o.Opinion LIKE '%' OR o.DataSource LIKE '%')
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating > IS NOT NULL
            ORDER BY o.ValidityRating DESC) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'IS'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.452608] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:276
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
                   'opinion' as KnowledgeType
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT TOP 1 * FROM (SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
                   'opinion' as KnowledgeType
            FROM REF_Opinion o
            WHERE (o.Name LIKE '%' OR o.Opinion LIKE '%' OR o.DataSource LIKE '%')
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating > IS NOT NULL
            ORDER BY o.ValidityRating DESC) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'IS'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.456225] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:292
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
                   f.ValidityRating, 'evidence' as KnowledgeType
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 1 * FROM (SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
                   f.ValidityRating, 'evidence' as KnowledgeType
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE '%' OR e.Evidence LIKE '%' OR e.DataSource LIKE '%')
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.458938] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:292
Status: FAILED
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
                   f.ValidityRating, 'evidence' as KnowledgeType
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 1 * FROM (SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
                   f.ValidityRating, 'evidence' as KnowledgeType
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE '%' OR e.Evidence LIKE '%' OR e.DataSource LIKE '%')
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.463123] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:329
Status: SUCCESS
Original SQL: SELECT 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END as Category,
            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Test SQL: SELECT TOP 1 * FROM (SELECT 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END as Category,
            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END) AS test_query
Result: SUCCESS - Tables: REF_Fact, Columns: DataSource, ValidityRating
----------------------------------------

[2025-08-20T23:46:54.465613] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:329
Status: SUCCESS
Original SQL: SELECT 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END as Category,
            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Test SQL: SELECT TOP 1 * FROM (SELECT 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END as Category,
            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END) AS test_query
Result: SUCCESS - Tables: REF_Fact, Columns: DataSource, ValidityRating
----------------------------------------

[2025-08-20T23:46:54.471996] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:241
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, ValidityRating, DataSource) 
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: DataSource, ValidityRating, Name
----------------------------------------

[2025-08-20T23:46:54.471996] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:241
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, ValidityRating, DataSource) 
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: DataSource, ValidityRating, Name
----------------------------------------

[2025-08-20T23:46:54.471996] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:267
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Evidence, Columns: FactID, Evidence, DataSource, Name
----------------------------------------

[2025-08-20T23:46:54.471996] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:267
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Evidence, Columns: FactID, Evidence, DataSource, Name
----------------------------------------

[2025-08-20T23:46:54.471996] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:374
Status: SUCCESS
Original SQL: SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Test SQL: SELECT TOP 1 * FROM (SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE '%'
            GROUP BY f.ID, f.ValidityRating) AS test_query
Result: SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: ID, ValidityRating, FactID, Name
----------------------------------------

[2025-08-20T23:46:54.471996] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:374
Status: SUCCESS
Original SQL: SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Test SQL: SELECT TOP 1 * FROM (SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE '%'
            GROUP BY f.ID, f.ValidityRating) AS test_query
Result: SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: ID, ValidityRating, FactID, Name
----------------------------------------

[2025-08-20T23:46:54.471996] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:396
Status: SUCCESS
Original SQL: UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: ID, ValidityRating
----------------------------------------

[2025-08-20T23:46:54.490284] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:410
Status: SUCCESS
Original SQL: SELECT ID FROM REF_Entity WHERE Name = ?
Test SQL: SELECT TOP 1 * FROM (SELECT ID FROM REF_Entity WHERE Name IS NOT NULL) AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-20T23:46:54.492999] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:419
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Test SQL: SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.DataSource LIKE '%'
            ORDER BY f.CreateDate DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.495829] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:419
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Test SQL: SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.DataSource LIKE '%'
            ORDER BY f.CreateDate DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.498852] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:430
Status: SUCCESS
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
                FROM REF_Evidence e
                WHERE e.FactID = ?
Test SQL: SELECT TOP 1 * FROM (SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
                FROM REF_Evidence e
                WHERE e.FactID IS NOT NULL) AS test_query
Result: SUCCESS - Tables: REF_Evidence, Columns: Evidence, Name, DataSource, ID, CreateDate, FactID
----------------------------------------

[2025-08-20T23:46:54.498852] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:430
Status: SUCCESS
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
                FROM REF_Evidence e
                WHERE e.FactID = ?
Test SQL: SELECT TOP 1 * FROM (SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
                FROM REF_Evidence e
                WHERE e.FactID IS NOT NULL) AS test_query
Result: SUCCESS - Tables: REF_Evidence, Columns: Evidence, Name, DataSource, ID, CreateDate, FactID
----------------------------------------

[2025-08-20T23:46:54.498852] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:439
Status: FAILED
Original SQL: SELECT a.Name as AttributeName, ev.EntityValue, eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Test SQL: SELECT TOP 0 * FROM X
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.506495] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:439
Status: FAILED
Original SQL: SELECT a.Name as AttributeName, ev.EntityValue, eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Test SQL: SELECT TOP 0 * FROM X
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.509761] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:465
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Test SQL: SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE '%' OR f.DataSource LIKE '%'
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.512185] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:465
Status: FAILED
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Test SQL: SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE '%' OR f.DataSource LIKE '%'
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.515184] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:477
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT TOP 1 * FROM (SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
            FROM REF_Opinion o
            WHERE o.Name LIKE '%' OR o.Opinion LIKE '%'
            ORDER BY o.ValidityRating DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.517511] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:477
Status: FAILED
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT TOP 1 * FROM (SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
            FROM REF_Opinion o
            WHERE o.Name LIKE '%' OR o.Opinion LIKE '%'
            ORDER BY o.ValidityRating DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.520512] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:552
Status: SUCCESS
Original SQL: SELECT ID FROM REF_Category WHERE Name = ?
Test SQL: SELECT TOP 1 * FROM (SELECT ID FROM REF_Category WHERE Name IS NOT NULL) AS test_query
Result: SUCCESS - Tables: REF_Category, Columns: ID, Name
----------------------------------------

[2025-08-20T23:46:54.520512] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:559
Status: SUCCESS
Original SQL: INSERT INTO REF_Category (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT TOP 0 * FROM REF_Category
Result: SUCCESS - Tables: REF_Category, Columns: Name
----------------------------------------

[2025-08-20T23:46:54.526602] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:566
Status: SUCCESS
Original SQL: SELECT ID FROM REF_Entity WHERE Name = ?
Test SQL: SELECT TOP 1 * FROM (SELECT ID FROM REF_Entity WHERE Name IS NOT NULL) AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-20T23:46:54.530000] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:573
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-20T23:46:54.532624] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:580
Status: SUCCESS
Original SQL: SELECT ID FROM REF_Attribute WHERE Name = ?
Test SQL: SELECT TOP 1 * FROM (SELECT ID FROM REF_Attribute WHERE Name IS NOT NULL) AS test_query
Result: SUCCESS - Tables: REF_Attribute, Columns: ID, Name
----------------------------------------

[2025-08-20T23:46:54.532624] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:587
Status: SUCCESS
Original SQL: INSERT INTO REF_Attribute (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT TOP 0 * FROM REF_Attribute
Result: SUCCESS - Tables: REF_Attribute, Columns: Name
----------------------------------------

[2025-08-20T23:46:54.532624] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:594
Status: SUCCESS
Original SQL: SELECT ID FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?
Test SQL: SELECT TOP 1 * FROM (SELECT ID FROM REF_EntityValue WHERE Name IS NOT NULL AND EntityValue IS NOT NULL) AS test_query
Result: SUCCESS - Tables: REF_EntityValue, Columns: ID, EntityValue, Name
----------------------------------------

[2025-08-20T23:46:54.532624] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:602
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)
            OUTPUT INSERTED.ID
            VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, ValueUnits, NumericValue, Name
----------------------------------------

[2025-08-20T23:46:54.532624] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:602
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)
            OUTPUT INSERTED.ID
            VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, ValueUnits, NumericValue, Name
----------------------------------------

[2025-08-20T23:46:54.532624] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:610
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue) OUTPUT INSERTED.ID VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, Name
----------------------------------------

[2025-08-20T23:46:54.548745] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:620
Status: FAILED
Original SQL: SELECT ID FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Test SQL: SELECT TOP 0 * FROM X
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.551219] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:620
Status: FAILED
Original SQL: SELECT ID FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Test SQL: SELECT TOP 0 * FROM X
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.555088] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:630
Status: FAILED
Original SQL: INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)
        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM XRF_EntityAttributeValue
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.558230] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:630
Status: FAILED
Original SQL: INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)
        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM XRF_EntityAttributeValue
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.569397] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:855
Status: SUCCESS
Original SQL: SELECT ID, Name, CreateDate
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Test SQL: SELECT TOP 1 * FROM (SELECT ID, Name, CreateDate
            FROM REF_Entity
            WHERE Name IS NOT NULL OR Name LIKE '%') AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: ID, CreateDate, Name
----------------------------------------

[2025-08-20T23:46:54.572173] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:855
Status: SUCCESS
Original SQL: SELECT ID, Name, CreateDate
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Test SQL: SELECT TOP 1 * FROM (SELECT ID, Name, CreateDate
            FROM REF_Entity
            WHERE Name IS NOT NULL OR Name LIKE '%') AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: ID, CreateDate, Name
----------------------------------------

[2025-08-20T23:46:54.575842] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:872
Status: FAILED
Original SQL: SELECT
                a.Name as AttributeName,
                ev.EntityValue as AttributeValue,
                ev.NumericValue,
                ev.ValueUnits,
                ev.Name as ValueName,
                eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Test SQL: SELECT TOP 0 * FROM X
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.578047] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:872
Status: FAILED
Original SQL: SELECT
                a.Name as AttributeName,
                ev.EntityValue as AttributeValue,
                ev.NumericValue,
                ev.ValueUnits,
                ev.Name as ValueName,
                eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Test SQL: SELECT TOP 0 * FROM X
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.578047] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:914
Status: FAILED
Original SQL: SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 1 * FROM (SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.Name LIKE '%' OR f.DataSource LIKE '%'
            ORDER BY f.ValidityRating DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.584656] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:914
Status: FAILED
Original SQL: SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 1 * FROM (SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.Name LIKE '%' OR f.DataSource LIKE '%'
            ORDER BY f.ValidityRating DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.587232] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:926
Status: FAILED
Original SQL: SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Test SQL: SELECT TOP 1 * FROM (SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate
            FROM REF_Evidence e
            WHERE e.Name LIKE '%' OR e.DataSource LIKE '%'
            ORDER BY e.CreateDate DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.591159] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:926
Status: FAILED
Original SQL: SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Test SQL: SELECT TOP 1 * FROM (SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate
            FROM REF_Evidence e
            WHERE e.Name LIKE '%' OR e.DataSource LIKE '%'
            ORDER BY e.CreateDate DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.594770] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:991
Status: FAILED
Original SQL: SELECT Name, ValidityRating, DataSource, CreateDate
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT TOP 1 * FROM (SELECT Name, ValidityRating, DataSource, CreateDate
            FROM REF_Fact
            WHERE Name LIKE '%' OR DataSource LIKE '%'
            ORDER BY ValidityRating DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.597168] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:991
Status: FAILED
Original SQL: SELECT Name, ValidityRating, DataSource, CreateDate
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT TOP 1 * FROM (SELECT Name, ValidityRating, DataSource, CreateDate
            FROM REF_Fact
            WHERE Name LIKE '%' OR DataSource LIKE '%'
            ORDER BY ValidityRating DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.601001] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1009
Status: FAILED
Original SQL: SELECT Name, ValidityRating, Opinion, DataSource, CreateDate
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT TOP 1 * FROM (SELECT Name, ValidityRating, Opinion, DataSource, CreateDate
            FROM REF_Opinion
            WHERE Name LIKE '%' OR Opinion LIKE '%'
            ORDER BY ValidityRating DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.603992] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1009
Status: FAILED
Original SQL: SELECT Name, ValidityRating, Opinion, DataSource, CreateDate
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT TOP 1 * FROM (SELECT Name, ValidityRating, Opinion, DataSource, CreateDate
            FROM REF_Opinion
            WHERE Name LIKE '%' OR Opinion LIKE '%'
            ORDER BY ValidityRating DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.606991] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1021
Status: FAILED
Original SQL: SELECT DISTINCT e.Name, e.CreateDate
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Test SQL: SELECT TOP 0 * FROM X
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.610652] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1021
Status: FAILED
Original SQL: SELECT DISTINCT e.Name, e.CreateDate
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Test SQL: SELECT TOP 0 * FROM X
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.613696] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:34
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.616700] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:34
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.621228] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:66
Status: FAILED
Original SQL: SELECT name, create_date, modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.624290] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:66
Status: FAILED
Original SQL: SELECT name, create_date, modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.626289] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:93
Status: FAILED
Original SQL: SELECT name FROM sys.procedures WHERE name = '{proc_name}'
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.630957] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:106
Status: FAILED
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.634689] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:106
Status: FAILED
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.637942] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:119
Status: FAILED
Original SQL: SELECT COUNT(*) as row_count FROM {table_name}
Test SQL: SELECT TOP 1 * FROM (SELECT COUNT(*) as row_count FROM {table_name}) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.641863] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:9
Status: FAILED
Original SQL: SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.644969] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:17
Status: SUCCESS
Original SQL: SELECT TOP 5 * FROM REF_Entity WHERE Name LIKE 'Session_%'
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 5 * FROM REF_Entity WHERE Name LIKE 'Session_%') AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-20T23:46:54.646973] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:26
Status: SUCCESS
Original SQL: SELECT TOP 10 * FROM REF_Entity ORDER BY CreateDate DESC
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 10 * FROM REF_Entity ORDER BY CreateDate DESC) AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-20T23:46:54.652660] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:33
Status: FAILED
Original SQL: SELECT TOP 5 * FROM XRF_EntityAttributeValue
Test SQL: SELECT TOP 0 * FROM X
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'X'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.656440] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py:48
Status: FAILED
Original SQL: SELECT name, create_date 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.660439] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py:48
Status: FAILED
Original SQL: SELECT name, create_date 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.663439] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:113
Status: FAILED
Original SQL: SELECT @count = COUNT(*) FROM
Test SQL: SELECT TOP 1 * FROM (SELECT @count = COUNT(*) FROM) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable "@count". (137) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.685289] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py:117
Status: FAILED
Original SQL: SELECT * FROM \1 ORDER BY
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM \1 ORDER BY) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '\\1'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.690631] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:37
Status: FAILED
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.695497] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:37
Status: FAILED
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.699041] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:77
Status: SUCCESS
Original SQL: SELECT TOP 1 * FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 1 * FROM REF_Entity) AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-20T23:46:54.701044] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:87
Status: SUCCESS
Original SQL: SELECT TOP 1 * FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 1 * FROM REF_Entity) AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-20T23:46:54.714136] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:268
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'test') AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: Name, *
----------------------------------------

[2025-08-20T23:46:54.717481] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:269
Status: FAILED
Original SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 1 * FROM (SELECT 1 FROM REF_Entity WHERE Name = 'test') AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.724409] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:31
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'test') AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: Name, *
----------------------------------------

[2025-08-20T23:46:54.725409] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:35
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) 
             VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Name, Value, EntityID
----------------------------------------

[2025-08-20T23:46:54.725409] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:35
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) 
             VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Name, Value, EntityID
----------------------------------------

[2025-08-20T23:46:54.738794] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:39
Status: FAILED
Original SQL: SELECT your option from the menu
Test SQL: SELECT TOP 0 * FROM t
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.743498] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:53
Status: FAILED
Original SQL: SELECT * FROM users WHERE active = 1
Test SQL: SELECT TOP 0 * FROM us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.749577] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:54
Status: FAILED
Original SQL: UPDATE users SET status = ? WHERE id = ?
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.754716] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:55
Status: FAILED
Original SQL: DELETE FROM logs WHERE created_at < ?
Test SQL: SELECT TOP 0 * FROM logs
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'logs'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.795605] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:208
Status: FAILED
Original SQL: SELECT * FROM REF_Entity WHERE Name = \'test\'
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = \'test\') AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.799696] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:209
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Value, Name
----------------------------------------

[2025-08-20T23:46:54.799696] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:209
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Value, Name
----------------------------------------

[2025-08-20T23:46:54.809840] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:210
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-20T23:46:54.813464] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:211
Status: SUCCESS
Original SQL: DELETE FROM REF_Entity WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID
----------------------------------------

[2025-08-20T23:46:54.818451] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:212
Status: FAILED
Original SQL: SELECT COUNT(*) FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID
Test SQL: SELECT TOP 1 * FROM (SELECT COUNT(*) FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID) AS test_query
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'EntityID'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155)")
----------------------------------------

[2025-08-20T23:46:54.822458] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:218
Status: FAILED
Original SQL: SELECT your option FROM the menu
Test SQL: SELECT TOP 0 * FROM t
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.826519] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:228
Status: FAILED
Original SQL: SELECT * FROM
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ')'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.830861] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:230
Status: FAILED
Original SQL: SELECT name FROM users WHERE active = 1 ORDER BY name
Test SQL: SELECT TOP 0 * FROM us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.854587] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:122
Status: FAILED
Original SQL: SELECT\s+(.+?)(?=\s+FROM)
Test SQL: SELECT TOP 1 * FROM (SELECT\s+(.+?)(?=\s+FROM)) AS test_query
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.860159] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:145
Status: FAILED
Original SQL: DELETE FROM table
Test SQL: SELECT TOP 0 * FROM table
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'table'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.864826] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:158
Status: FAILED
Original SQL: SELECT Name FROM Users
Test SQL: SELECT TOP 0 * FROM Us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.870342] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:570
Status: FAILED
Original SQL: SELECT TOP 0 * FROM {table_name}
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 0 * FROM {table_name}) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.875699] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:575
Status: FAILED
Original SQL: SELECT TOP 1 * FROM ({test_sql}) AS test_query
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 1 * FROM ({test_sql}) AS test_query) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.880316] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:582
Status: FAILED
Original SQL: SELECT TOP 0 * FROM {table_name}
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 0 * FROM {table_name}) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.884292] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:589
Status: FAILED
Original SQL: SELECT TOP 0 * FROM {table_name}
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 0 * FROM {table_name}) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.888983] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:596
Status: FAILED
Original SQL: SELECT TOP 0 * FROM {table_name}
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 0 * FROM {table_name}) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.893754] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:603
Status: FAILED
Original SQL: SELECT 1 FROM {table_name} WHERE 1=0
Test SQL: SELECT TOP 1 * FROM (SELECT 1 FROM {table_name} WHERE 1=0) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.902584] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:711
Status: FAILED
Original SQL: SELECT 1 FROM {tables[0]} WHERE 1=0
Test SQL: SELECT TOP 1 * FROM (SELECT 1 FROM {tables[0]} WHERE 1=0) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:54.910627] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:39
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'test') AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: Name, *
----------------------------------------

[2025-08-20T23:46:54.913639] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:42
Status: FAILED
Original SQL: SELECT your option from the menu
Test SQL: SELECT TOP 0 * FROM t
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.913639] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:45
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) 
    VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Name, Value, EntityID
----------------------------------------

[2025-08-20T23:46:54.913639] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:45
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) 
    VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Name, Value, EntityID
----------------------------------------

[2025-08-20T23:46:54.929623] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:54
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-20T23:46:54.935631] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py:36
Status: FAILED
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.940511] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py:36
Status: FAILED
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.947873] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:111
Status: FAILED
Original SQL: SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.952476] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:111
Status: FAILED
Original SQL: SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.957326] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:131
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.961266] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:131
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.968593] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:12
Status: FAILED
Original SQL: SELECT 
            name,
            create_date,
            modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.970593] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:12
Status: FAILED
Original SQL: SELECT 
            name,
            create_date,
            modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.977398] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:83
Status: FAILED
Original SQL: SELECT name FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:54.979398] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:83
Status: FAILED
Original SQL: SELECT name FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.005684] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:45
Status: FAILED
Original SQL: SELECT your option from the menu
Test SQL: SELECT TOP 0 * FROM t
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.009883] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:66
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity) AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: *
----------------------------------------

[2025-08-20T23:46:55.010882] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:67
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'test') AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: Name, *
----------------------------------------

[2025-08-20T23:46:55.010882] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:68
Status: FAILED
Original SQL: SELECT COUNT(*) FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM (SELECT COUNT(*) FROM REF_Entity) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.024597] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:71
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Value) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Value, Name
----------------------------------------

[2025-08-20T23:46:55.030738] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:72
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) VALUES (1, 'test', 'value')
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Name, Value, EntityID
----------------------------------------

[2025-08-20T23:46:55.033414] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:75
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-20T23:46:55.033414] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:76
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Status = 'active' WHERE Name = 'test'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Status, Name
----------------------------------------

[2025-08-20T23:46:55.033414] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:79
Status: SUCCESS
Original SQL: DELETE FROM REF_Entity WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID
----------------------------------------

[2025-08-20T23:46:55.052330] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:80
Status: SUCCESS
Original SQL: DELETE FROM REF_Entity WHERE CreatedDate < '2023-01-01'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreatedDate
----------------------------------------

[2025-08-20T23:46:55.058195] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:90
Status: FAILED
Original SQL: SELECT e.ID, e.Name, f.Value
    FROM REF_Entity e
    JOIN REF_Fact f ON e.ID = f.EntityID
    WHERE e.Status = 'active'
    ORDER BY e.Name
Test SQL: SELECT TOP 1 * FROM (SELECT e.ID, e.Name, f.Value
    FROM REF_Entity e
    JOIN REF_Fact f ON e.ID = f.EntityID
    WHERE e.Status = 'active'
    ORDER BY e.Name) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:55.064225] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:90
Status: FAILED
Original SQL: SELECT e.ID, e.Name, f.Value
    FROM REF_Entity e
    JOIN REF_Fact f ON e.ID = f.EntityID
    WHERE e.Status = 'active'
    ORDER BY e.Name
Test SQL: SELECT TOP 1 * FROM (SELECT e.ID, e.Name, f.Value
    FROM REF_Entity e
    JOIN REF_Fact f ON e.ID = f.EntityID
    WHERE e.Status = 'active'
    ORDER BY e.Name) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:55.069456] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:99
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, Status, CreatedDate)
    VALUES 
        ('Entity1', 'First entity', 'active', GETDATE()),
        ('Entity2', 'Second entity', 'inactive', GETDATE()),
        ('Entity3', 'Third entity', 'active', GETDATE())
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Description, Status, CreatedDate, Name
----------------------------------------

[2025-08-20T23:46:55.070456] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:99
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, Status, CreatedDate)
    VALUES 
        ('Entity1', 'First entity', 'active', GETDATE()),
        ('Entity2', 'Second entity', 'inactive', GETDATE()),
        ('Entity3', 'Third entity', 'active', GETDATE())
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Description, Status, CreatedDate, Name
----------------------------------------

[2025-08-20T23:46:55.070456] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:108
Status: SUCCESS
Original SQL: UPDATE REF_Entity 
    SET Status = 'archived',
        ModifiedDate = GETDATE()
    WHERE CreatedDate < DATEADD(year, -1, GETDATE())
        AND Status = 'inactive'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ModifiedDate, Status, CreatedDate
----------------------------------------

[2025-08-20T23:46:55.087141] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:108
Status: SUCCESS
Original SQL: UPDATE REF_Entity 
    SET Status = 'archived',
        ModifiedDate = GETDATE()
    WHERE CreatedDate < DATEADD(year, -1, GETDATE())
        AND Status = 'inactive'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ModifiedDate, Status, CreatedDate
----------------------------------------

[2025-08-20T23:46:55.093054] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:117
Status: SUCCESS
Original SQL: DELETE FROM REF_Fact 
    WHERE EntityID IN (
        SELECT ID FROM REF_Entity 
        WHERE Status = 'deleted'
    )
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Status, EntityID
----------------------------------------

[2025-08-20T23:46:55.094574] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:117
Status: SUCCESS
Original SQL: DELETE FROM REF_Fact 
    WHERE EntityID IN (
        SELECT ID FROM REF_Entity 
        WHERE Status = 'deleted'
    )
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Status, EntityID
----------------------------------------

[2025-08-20T23:46:55.105789] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:135
Status: FAILED
Original SQL: SELECT 
        e.ID,
        e.Name,
        e.Description,
        
        f.Value,
        f.CreatedDate
        
    FROM REF_Entity e
    
    LEFT JOIN REF_Fact f 
        ON e.ID = f.EntityID
        
    WHERE e.Status = 'active'
        AND f.Value IS NOT NULL
        
    ORDER BY 
        e.Name ASC,
        f.CreatedDate DESC
Test SQL: SELECT TOP 1 * FROM (SELECT 
        e.ID,
        e.Name,
        e.Description,
        
        f.Value,
        f.CreatedDate
        
    FROM REF_Entity e
    
    LEFT JOIN REF_Fact f 
        ON e.ID = f.EntityID
        
    WHERE e.Status = 'active'
        AND f.Value IS NOT NULL
        
    ORDER BY 
        e.Name ASC,
        f.CreatedDate DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:55.110896] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:135
Status: FAILED
Original SQL: SELECT 
        e.ID,
        e.Name,
        e.Description,
        
        f.Value,
        f.CreatedDate
        
    FROM REF_Entity e
    
    LEFT JOIN REF_Fact f 
        ON e.ID = f.EntityID
        
    WHERE e.Status = 'active'
        AND f.Value IS NOT NULL
        
    ORDER BY 
        e.Name ASC,
        f.CreatedDate DESC
Test SQL: SELECT TOP 1 * FROM (SELECT 
        e.ID,
        e.Name,
        e.Description,
        
        f.Value,
        f.CreatedDate
        
    FROM REF_Entity e
    
    LEFT JOIN REF_Fact f 
        ON e.ID = f.EntityID
        
    WHERE e.Status = 'active'
        AND f.Value IS NOT NULL
        
    ORDER BY 
        e.Name ASC,
        f.CreatedDate DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:55.117592] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:158
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity 
    (
        Name,
        Description,
        Status,
        CreatedDate
    )
    VALUES 
    (
        ?,
        ?,
        'active',
        GETDATE()
    )
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Description, Status, CreatedDate, Name
----------------------------------------

[2025-08-20T23:46:55.120445] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:158
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity 
    (
        Name,
        Description,
        Status,
        CreatedDate
    )
    VALUES 
    (
        ?,
        ?,
        'active',
        GETDATE()
    )
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Description, Status, CreatedDate, Name
----------------------------------------

[2025-08-20T23:46:55.120445] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:194
Status: FAILED
Original SQL: SELECT 'This contains SELECT keyword' FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM (SELECT 'This contains SELECT keyword' FROM REF_Entity) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.132814] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:195
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name) VALUES ('UPDATE this later')
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-20T23:46:55.134822] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:198
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity  -- This is a comment with SELECT
    WHERE Name = 'test'  /* Another comment with INSERT */
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity  -- This is a comment with SELECT
    WHERE Name = 'test'  /* Another comment with INSERT */) AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: Name, with, *
----------------------------------------

[2025-08-20T23:46:55.134822] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:198
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity  -- This is a comment with SELECT
    WHERE Name = 'test'  /* Another comment with INSERT */
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity  -- This is a comment with SELECT
    WHERE Name = 'test'  /* Another comment with INSERT */) AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: Name, with, *
----------------------------------------

[2025-08-20T23:46:55.134822] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:205
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'test') AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: Name, *
----------------------------------------

[2025-08-20T23:46:55.152755] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:208
Status: FAILED
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'It\\'s a test'
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'It\\'s a test') AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 's'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Unclosed quotation mark after the character string ') AS test_query'. (105)")
----------------------------------------

[2025-08-20T23:46:55.158139] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:211
Status: FAILED
Original SQL: SELECT 
        e1.ID as Entity1_ID,
        e1.Name as Entity1_Name,
        e2.ID as Entity2_ID,
        e2.Name as Entity2_Name,
        f1.Value as Fact1_Value,
        f2.Value as Fact2_Value
    FROM REF_Entity e1
    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID
    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID
    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID
    WHERE e1.Status = 'active'
        AND f1.Type = 'primary'
        AND (e2.Status IS NULL OR e2.Status = 'active')
    ORDER BY e1.Name, e2.Name
    LIMIT 100
Test SQL: SELECT TOP 1 * FROM (SELECT 
        e1.ID as Entity1_ID,
        e1.Name as Entity1_Name,
        e2.ID as Entity2_ID,
        e2.Name as Entity2_Name,
        f1.Value as Fact1_Value,
        f2.Value as Fact2_Value
    FROM REF_Entity e1
    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID
    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID
    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID
    WHERE e1.Status = 'active'
        AND f1.Type = 'primary'
        AND (e2.Status IS NULL OR e2.Status = 'active')
    ORDER BY e1.Name, e2.Name
    LIMIT 100) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'LIMIT'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.163626] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:211
Status: FAILED
Original SQL: SELECT 
        e1.ID as Entity1_ID,
        e1.Name as Entity1_Name,
        e2.ID as Entity2_ID,
        e2.Name as Entity2_Name,
        f1.Value as Fact1_Value,
        f2.Value as Fact2_Value
    FROM REF_Entity e1
    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID
    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID
    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID
    WHERE e1.Status = 'active'
        AND f1.Type = 'primary'
        AND (e2.Status IS NULL OR e2.Status = 'active')
    ORDER BY e1.Name, e2.Name
    LIMIT 100
Test SQL: SELECT TOP 1 * FROM (SELECT 
        e1.ID as Entity1_ID,
        e1.Name as Entity1_Name,
        e2.ID as Entity2_ID,
        e2.Name as Entity2_Name,
        f1.Value as Fact1_Value,
        f2.Value as Fact2_Value
    FROM REF_Entity e1
    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID
    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID
    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID
    WHERE e1.Status = 'active'
        AND f1.Type = 'primary'
        AND (e2.Status IS NULL OR e2.Status = 'active')
    ORDER BY e1.Name, e2.Name
    LIMIT 100) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'LIMIT'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.165347] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:236
Status: FAILED
Original SQL: SELECT * FROM users WHERE id = ?
Test SQL: SELECT TOP 0 * FROM us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.173537] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:237
Status: FAILED
Original SQL: INSERT INTO users (name, email) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.174541] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:238
Status: FAILED
Original SQL: UPDATE users SET email = ? WHERE id = ?
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.183763] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:239
Status: FAILED
Original SQL: DELETE FROM users WHERE id = ?
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.188751] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:243
Status: FAILED
Original SQL: SELECT COUNT(*) FROM transactions WHERE DATE(created_at) = CURDATE()
Test SQL: SELECT TOP 0 * FROM t
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.194407] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:244
Status: FAILED
Original SQL: SELECT SUM(amount) FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())
Test SQL: SELECT TOP 0 * FROM t
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.198921] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:269
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity) AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: *
----------------------------------------

[2025-08-20T23:46:55.203530] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:285
Status: FAILED
Original SQL: SELECT * FROM
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ')'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.208141] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:286
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-20T23:46:55.213047] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:287
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-20T23:46:55.217966] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:291
Status: FAILED
Original SQL: SELECT INSERT UPDATE DELETE FROM WHERE
Test SQL: SELECT TOP 1 * FROM (SELECT INSERT UPDATE DELETE FROM WHERE) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'INSERT'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.221040] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:305
Status: FAILED
Original SQL: SELECT COUNT(*) FROM REF_Entity WHERE Status = 'test'
Test SQL: SELECT TOP 1 * FROM (SELECT COUNT(*) FROM REF_Entity WHERE Status = 'test') AS test_query
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW); [42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155)")
----------------------------------------

[2025-08-20T23:46:55.229198] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:309
Status: SUCCESS
Original SQL: SELECT 
        'Test completed successfully' as message,
        COUNT(*) as total_entities
    FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM (SELECT 
        'Test completed successfully' as message,
        COUNT(*) as total_entities
    FROM REF_Entity) AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: Test, successfully, completed
----------------------------------------

[2025-08-20T23:46:55.229198] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:309
Status: SUCCESS
Original SQL: SELECT 
        'Test completed successfully' as message,
        COUNT(*) as total_entities
    FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM (SELECT 
        'Test completed successfully' as message,
        COUNT(*) as total_entities
    FROM REF_Entity) AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: Test, successfully, completed
----------------------------------------

[2025-08-20T23:46:55.239680] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:21
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity) AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: *
----------------------------------------

[2025-08-20T23:46:55.244809] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:22
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-20T23:46:55.251809] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:23
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-20T23:46:55.257258] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:32
Status: FAILED
Original SQL: SELECT * FROM REF_Entity 
WHERE Status = 'active'
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity 
WHERE Status = 'active') AS test_query
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.262381] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:32
Status: FAILED
Original SQL: SELECT * FROM REF_Entity 
WHERE Status = 'active'
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity 
WHERE Status = 'active') AS test_query
Result: FAILED - ('42S22', "[42S22] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid column name 'Status'. (207) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.268782] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:62
Status: FAILED
Original SQL: SELECT Name, ID FROM Users
Test SQL: SELECT TOP 0 * FROM Us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.272831] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:71
Status: FAILED
Original SQL: SELECT u.Name, u.ID FROM Users u
Test SQL: SELECT TOP 0 * FROM Us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.280271] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:80
Status: FAILED
Original SQL: SELECT u.Name, u.ID FROM Users AS u
Test SQL: SELECT TOP 0 * FROM Us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.286168] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:89
Status: FAILED
Original SQL: SELECT * FROM Products
Test SQL: SELECT TOP 0 * FROM P
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'P'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.291352] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:98
Status: FAILED
Original SQL: SELECT * FROM Products WHERE Price > 100
Test SQL: SELECT TOP 0 * FROM P
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'P'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.297084] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:107
Status: FAILED
Original SQL: SELECT * FROM Products p WHERE p.Active = 1
Test SQL: SELECT TOP 0 * FROM P
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'P'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.302515] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:116
Status: FAILED
Original SQL: SELECT Name, Email FROM Users WHERE Active = 1
Test SQL: SELECT TOP 0 * FROM Us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.308021] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:125
Status: FAILED
Original SQL: SELECT u.Name, u.Email FROM Users u WHERE u.Active = 1
Test SQL: SELECT TOP 0 * FROM Us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.313638] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:135
Status: FAILED
Original SQL: SELECT u.Name FROM dbo.Users u WHERE u.Status = 'Active'
Test SQL: SELECT TOP 0 * FROM dbo
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.319363] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:149
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u INNER JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 0 * FROM Us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.324671] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:158
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 0 * FROM Us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.330236] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:168
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u LEFT JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 0 * FROM Us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.335487] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:177
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u LEFT OUTER JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 0 * FROM Us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.340712] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:187
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u RIGHT JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 0 * FROM Us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.346724] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:197
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u FULL JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 0 * FROM Us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.352404] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:207
Status: FAILED
Original SQL: SELECT u.Name, p.Title, c.Content FROM Users u JOIN Posts p ON u.ID = p.UserID JOIN Comments c ON p.ID = c.PostID
Test SQL: SELECT TOP 0 * FROM Us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.358161] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:217
Status: FAILED
Original SQL: SELECT u.Name, p.Title, c.Content FROM Users u LEFT JOIN Posts p ON u.ID = p.UserID INNER JOIN Comments c ON p.ID = c.PostID
Test SQL: SELECT TOP 0 * FROM Us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.363416] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:227
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u, Posts p WHERE u.ID = p.UserID
Test SQL: SELECT TOP 0 * FROM Us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.368483] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:236
Status: FAILED
Original SQL: SELECT t1.Name, t2.Title, t3.Content FROM Table1 t1, Table2 t2, Table3 t3 WHERE t1.ID = t2.T1_ID AND t2.ID = t3.T2_ID
Test SQL: SELECT TOP 0 * FROM Tabl
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Tabl'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.375067] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:250
Status: FAILED
Original SQL: SELECT Department, COUNT(*) as EmployeeCount FROM Employees GROUP BY Department
Test SQL: SELECT TOP 1 * FROM (SELECT Department, COUNT(*) as EmployeeCount FROM Employees GROUP BY Department) AS test_query
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.376234] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:259
Status: FAILED
Original SQL: SELECT Department, JobTitle, COUNT(*) as Count FROM Employees GROUP BY Department, JobTitle
Test SQL: SELECT TOP 1 * FROM (SELECT Department, JobTitle, COUNT(*) as Count FROM Employees GROUP BY Department, JobTitle) AS test_query
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.386030] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:269
Status: FAILED
Original SQL: SELECT Department, COUNT(*) as EmployeeCount FROM Employees GROUP BY Department HAVING COUNT(*) > 5
Test SQL: SELECT TOP 1 * FROM (SELECT Department, COUNT(*) as EmployeeCount FROM Employees GROUP BY Department HAVING COUNT(*) > 5) AS test_query
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.391792] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:279
Status: FAILED
Original SQL: SELECT Name, Salary FROM Employees ORDER BY Salary DESC
Test SQL: SELECT TOP 1 * FROM (SELECT Name, Salary FROM Employees ORDER BY Salary DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:55.396794] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:288
Status: FAILED
Original SQL: SELECT Name, Department, Salary FROM Employees ORDER BY Department ASC, Salary DESC
Test SQL: SELECT TOP 1 * FROM (SELECT Name, Department, Salary FROM Employees ORDER BY Department ASC, Salary DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:55.402232] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:298
Status: FAILED
Original SQL: SELECT e.Department, COUNT(e.ID) as EmployeeCount, AVG(e.Salary) as AvgSalary FROM Employees e WHERE e.Active = 1 GROUP BY e.Department HAVING COUNT(e.ID) > 3 ORDER BY AvgSalary DESC
Test SQL: SELECT TOP 1 * FROM (SELECT e.Department, COUNT(e.ID) as EmployeeCount, AVG(e.Salary) as AvgSalary FROM Employees e WHERE e.Active = 1 GROUP BY e.Department HAVING COUNT(e.ID) > 3 ORDER BY AvgSalary DESC) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:55.407789] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:308
Status: FAILED
Original SQL: SELECT d.Name, COUNT(e.ID) as EmployeeCount FROM Departments d LEFT JOIN Employees e ON d.ID = e.DepartmentID GROUP BY d.Name
Test SQL: SELECT TOP 0 * FROM D
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'D'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.414074] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:318
Status: SUCCESS
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, COUNT(e.ID) as EvidenceCount, AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage FROM REF_Fact f LEFT JOIN REF_Evidence e ON f.ID = e.FactID WHERE f.DataSource LIKE '%Self-Healer%' GROUP BY f.ID, f.Name, f.ValidityRating
Test SQL: SELECT TOP 1 * FROM (SELECT f.ID, f.Name, f.ValidityRating, COUNT(e.ID) as EvidenceCount, AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage FROM REF_Fact f LEFT JOIN REF_Evidence e ON f.ID = e.FactID WHERE f.DataSource LIKE '%Self-Healer%' GROUP BY f.ID, f.Name, f.ValidityRating) AS test_query
Result: SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: Name, DataSource, ID, FactID, ValidityRating
----------------------------------------

[2025-08-20T23:46:55.414074] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:328
Status: FAILED
Original SQL: SELECT Name, Salary FROM Employees ORDER BY Salary DESC LIMIT 10
Test SQL: SELECT TOP 1 * FROM (SELECT Name, Salary FROM Employees ORDER BY Salary DESC LIMIT 10) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'LIMIT'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.426996] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:342
Status: FAILED
Original SQL: INSERT INTO Users (Name, Email) VALUES ('John Doe', '<EMAIL>')
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.432629] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:351
Status: FAILED
Original SQL: INSERT INTO Users VALUES ('John Doe', '<EMAIL>', 1)
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.438390] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:361
Status: FAILED
Original SQL: INSERT INTO ActiveUsers (Name, Email) SELECT Name, Email FROM Users WHERE Active = 1
Test SQL: SELECT TOP 0 * FROM ActiveUsers
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'ActiveUsers'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.444054] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:371
Status: FAILED
Original SQL: INSERT INTO dbo.Users (Name, Email, Status) VALUES ('Jane Doe', '<EMAIL>', 'Active')
Test SQL: SELECT TOP 0 * FROM dbo
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.449647] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:385
Status: FAILED
Original SQL: UPDATE Users SET Name = 'Jane Doe' WHERE ID = 1
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.457004] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:394
Status: FAILED
Original SQL: UPDATE Users SET Name = 'Jane Doe', Email = '<EMAIL>', Status = 'Active' WHERE ID = 1
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.462404] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:404
Status: FAILED
Original SQL: UPDATE Users u SET u.Name = 'Jane Doe', u.Status = 'Active' WHERE u.ID = 1
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.469338] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:414
Status: FAILED
Original SQL: UPDATE Users u SET u.Status = 'Inactive' FROM Users u JOIN Departments d ON u.DepartmentID = d.ID WHERE d.Name = 'Closed'
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.473552] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:424
Status: FAILED
Original SQL: UPDATE dbo.Users SET Status = 'Inactive' WHERE LastLogin < '2023-01-01'
Test SQL: SELECT TOP 0 * FROM dbo
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.480353] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:438
Status: FAILED
Original SQL: DELETE FROM Users WHERE ID = 1
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.486414] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:447
Status: FAILED
Original SQL: DELETE FROM Users WHERE Status = 'Inactive' AND LastLogin < '2023-01-01'
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.490437] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:457
Status: FAILED
Original SQL: DELETE FROM Users u WHERE u.Status = 'Inactive'
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.498541] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:467
Status: FAILED
Original SQL: DELETE FROM dbo.Users WHERE Status = 'Deleted'
Test SQL: SELECT TOP 0 * FROM dbo
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.505419] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:481
Status: FAILED
Original SQL: SELECT    u.Name   ,   u.Email   FROM    Users   u   WHERE   u.Active   =   1
Test SQL: SELECT TOP 0 * FROM Us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.510424] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:491
Status: FAILED
Original SQL: select u.Name, u.Email from Users u where u.Active = 1
Test SQL: SELECT TOP 0 * FROM Us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.516952] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:501
Status: FAILED
Original SQL: SELECT rf.ID, rf.Name FROM REF_Fact rf JOIN XRF_Fact_Evidence xfe ON rf.ID = xfe.FactID
Test SQL: SELECT TOP 1 * FROM (SELECT rf.ID, rf.Name FROM REF_Fact rf JOIN XRF_Fact_Evidence xfe ON rf.ID = xfe.FactID) AS test_query
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_Fact_Evidence'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.522678] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:511
Status: FAILED
Original SQL: SELECT t1.Field1, t2.Field2 FROM Table1 t1 JOIN Table2 t2 ON t1.ID = t2.Table1ID
Test SQL: SELECT TOP 0 * FROM Tabl
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Tabl'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.528850] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:521
Status: FAILED
Original SQL: SELECT employee.Name, department.Title FROM Employees employee JOIN Departments department ON employee.DeptID = department.ID
Test SQL: SELECT TOP 1 * FROM (SELECT employee.Name, department.Title FROM Employees employee JOIN Departments department ON employee.DeptID = department.ID) AS test_query
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.535908] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:27
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity) AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: *
----------------------------------------

[2025-08-20T23:46:55.541481] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:28
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'test') AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: Name, *
----------------------------------------

[2025-08-20T23:46:55.545914] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:29
Status: FAILED
Original SQL: SELECT COUNT(*) FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM (SELECT COUNT(*) FROM REF_Entity) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.545914] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:30
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Value) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Value, Name
----------------------------------------

[2025-08-20T23:46:55.560257] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:31
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) VALUES (1, 'test', 'value')
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Name, Value, EntityID
----------------------------------------

[2025-08-20T23:46:55.565016] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:32
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-20T23:46:55.565016] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:33
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Status = 'active' WHERE Name = 'test'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Status, Name
----------------------------------------

[2025-08-20T23:46:55.565016] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:34
Status: SUCCESS
Original SQL: DELETE FROM REF_Entity WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID
----------------------------------------

[2025-08-20T23:46:55.582880] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:35
Status: SUCCESS
Original SQL: DELETE FROM REF_Entity WHERE CreatedDate < '2023-01-01'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreatedDate
----------------------------------------

[2025-08-20T23:46:55.589350] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:40
Status: FAILED
Original SQL: SELECT 'This contains SELECT keyword' FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM (SELECT 'This contains SELECT keyword' FROM REF_Entity) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]No column name was specified for column 1 of 'test_query'. (8155) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.594642] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:41
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name) VALUES ('UPDATE this later')
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-20T23:46:55.594642] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:42
Status: FAILED
Original SQL: SELECT * FROM REF_Entity WHERE Name = \"test\"
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = \"test\") AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'test\\'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.607059] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:43
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'test') AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: Name, *
----------------------------------------

[2025-08-20T23:46:55.614115] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:44
Status: FAILED
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'It\\'s a test'
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name = 'It\\'s a test') AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 's'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Unclosed quotation mark after the character string ') AS test_query'. (105)")
----------------------------------------

[2025-08-20T23:46:55.623802] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:47
Status: FAILED
Original SQL: SELECT * FROM users WHERE id = ?
Test SQL: SELECT TOP 0 * FROM us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.626808] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:48
Status: FAILED
Original SQL: INSERT INTO users (name, email) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.635816] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:49
Status: FAILED
Original SQL: UPDATE users SET email = ? WHERE id = ?
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.641442] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:50
Status: FAILED
Original SQL: DELETE FROM users WHERE id = ?
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.648323] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:51
Status: FAILED
Original SQL: SELECT COUNT(*) FROM transactions WHERE DATE(created_at) = CURDATE()
Test SQL: SELECT TOP 0 * FROM t
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.654957] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:52
Status: FAILED
Original SQL: SELECT SUM(amount) FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())
Test SQL: SELECT TOP 0 * FROM t
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.660679] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:67
Status: FAILED
Original SQL: SELECT your option from the menu
Test SQL: SELECT TOP 0 * FROM t
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 't'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.667263] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:72
Status: FAILED
Original SQL: SELECT * FROM
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ')'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.673138] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:73
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-20T23:46:55.676026] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:74
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-20T23:46:55.687133] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:194
Status: FAILED
Original SQL: SELECT 
    e.ID,
    e.Name

FROM REF_Entity e

WHERE e.Status = 'active'

ORDER BY e.Name
Test SQL: SELECT TOP 1 * FROM (SELECT 
    e.ID,
    e.Name

FROM REF_Entity e

WHERE e.Status = 'active'

ORDER BY e.Name) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:55.693987] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:194
Status: FAILED
Original SQL: SELECT 
    e.ID,
    e.Name

FROM REF_Entity e

WHERE e.Status = 'active'

ORDER BY e.Name
Test SQL: SELECT TOP 1 * FROM (SELECT 
    e.ID,
    e.Name

FROM REF_Entity e

WHERE e.Status = 'active'

ORDER BY e.Name) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]The ORDER BY clause is invalid in views, inline functions, derived tables, subqueries, and common table expressions, unless TOP, OFFSET or FOR XML is also specified. (1033) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:55.699962] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:210
Status: FAILED
Original SQL: SELECT * FROM users WHERE name = 'John'
Test SQL: SELECT TOP 0 * FROM us
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'us'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.705716] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:218
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity) AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: *
----------------------------------------

[2025-08-20T23:46:55.718500] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:219
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE ID = ?
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE ID IS NOT NULL) AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: ID, *
----------------------------------------

[2025-08-20T23:46:55.728596] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_db.py:11
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name LIKE 'Session_%'
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM REF_Entity WHERE Name LIKE 'Session_%') AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: Name, *
----------------------------------------

[2025-08-20T23:46:55.738990] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py:257
Status: SUCCESS
Original SQL: SELECT COUNT(*) as count FROM REF_Fact
Test SQL: SELECT TOP 1 * FROM (SELECT COUNT(*) as count FROM REF_Fact) AS test_query
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-20T23:46:55.755174] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:79
Status: FAILED
Original SQL: SELECT 
                name,
                create_date,
                modify_date,
                type_desc
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.758755] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:79
Status: FAILED
Original SQL: SELECT 
                name,
                create_date,
                modify_date,
                type_desc
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.768923] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:129
Status: SUCCESS
Original SQL: SELECT TOP 5 ID, Name FROM REF_Entity ORDER BY ID
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 5 ID, Name FROM REF_Entity ORDER BY ID) AS test_query
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-20T23:46:55.775519] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:135
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)
                OUTPUT INSERTED.ID, INSERTED.Name
                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ModifyDate, Description, CreateDate, Name
----------------------------------------

[2025-08-20T23:46:55.782484] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:135
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)
                OUTPUT INSERTED.ID, INSERTED.Name
                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ModifyDate, Description, CreateDate, Name
----------------------------------------

[2025-08-20T23:46:55.788021] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:360
Status: FAILED
Original SQL: SELECT name FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.798430] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:360
Status: FAILED
Original SQL: SELECT name FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.807114] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:393
Status: FAILED
Original SQL: SELECT TABLE_NAME, TABLE_TYPE
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.815168] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:393
Status: FAILED
Original SQL: SELECT TABLE_NAME, TABLE_TYPE
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.825954] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_database.py:180
Status: FAILED
Original SQL: SELECT COUNT(*) as total_rows FROM [{schema_name}].[{table_name}]
Test SQL: SELECT TOP 1 * FROM (SELECT COUNT(*) as total_rows FROM [{schema_name}].[{table_name}]) AS test_query
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.839424] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:29
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.850926] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:29
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.857000] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:48
Status: SUCCESS
Original SQL: SELECT 
            COUNT(*) as TotalRows,
            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,
            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows
        FROM REF_EntityValue
Test SQL: SELECT TOP 1 * FROM (SELECT 
            COUNT(*) as TotalRows,
            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,
            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows
        FROM REF_EntityValue) AS test_query
Result: SUCCESS - Tables: REF_EntityValue, Columns: NumericValue, ValueUnits
----------------------------------------

[2025-08-20T23:46:55.868546] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:48
Status: SUCCESS
Original SQL: SELECT 
            COUNT(*) as TotalRows,
            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,
            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows
        FROM REF_EntityValue
Test SQL: SELECT TOP 1 * FROM (SELECT 
            COUNT(*) as TotalRows,
            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,
            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows
        FROM REF_EntityValue) AS test_query
Result: SUCCESS - Tables: REF_EntityValue, Columns: NumericValue, ValueUnits
----------------------------------------

[2025-08-20T23:46:55.876936] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:66
Status: SUCCESS
Original SQL: SELECT TOP 5
            Name,
            EntityValue,
            NumericValue,
            ValueUnits
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 5
            Name,
            EntityValue,
            NumericValue,
            ValueUnits
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC) AS test_query
Result: SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, ValueUnits, NumericValue, Name
----------------------------------------

[2025-08-20T23:46:55.876936] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:66
Status: SUCCESS
Original SQL: SELECT TOP 5
            Name,
            EntityValue,
            NumericValue,
            ValueUnits
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 5
            Name,
            EntityValue,
            NumericValue,
            ValueUnits
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC) AS test_query
Result: SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, ValueUnits, NumericValue, Name
----------------------------------------

[2025-08-20T23:46:55.895101] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:149
Status: SUCCESS
Original SQL: SELECT EntityValue, NumericValue, ValueUnits
            FROM REF_EntityValue 
            WHERE ID = ?
Test SQL: SELECT TOP 1 * FROM (SELECT EntityValue, NumericValue, ValueUnits
            FROM REF_EntityValue 
            WHERE ID IS NOT NULL) AS test_query
Result: SUCCESS - Tables: REF_EntityValue, Columns: ID, NumericValue, ValueUnits, EntityValue
----------------------------------------

[2025-08-20T23:46:55.904517] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:149
Status: SUCCESS
Original SQL: SELECT EntityValue, NumericValue, ValueUnits
            FROM REF_EntityValue 
            WHERE ID = ?
Test SQL: SELECT TOP 1 * FROM (SELECT EntityValue, NumericValue, ValueUnits
            FROM REF_EntityValue 
            WHERE ID IS NOT NULL) AS test_query
Result: SUCCESS - Tables: REF_EntityValue, Columns: ID, NumericValue, ValueUnits, EntityValue
----------------------------------------

[2025-08-20T23:46:55.904517] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py:56
Status: FAILED
Original SQL: SELECT name 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.927806] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py:56
Status: FAILED
Original SQL: SELECT name 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.943709] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_server.py:276
Status: FAILED
Original SQL: SELECT TOP 20 
                FactID, FactType, FactText, ValidityRating, 
                CreatedDate, LastUpdated, Source
            FROM REF_FACT 
            WHERE FactText LIKE ? 
            AND ValidityRating >= ?
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 20 
                FactID, FactType, FactText, ValidityRating, 
                CreatedDate, LastUpdated, Source
            FROM REF_FACT 
            WHERE FactText LIKE '%' 
            AND ValidityRating > IS NOT NULL) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'IS'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.950570] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_server.py:276
Status: FAILED
Original SQL: SELECT TOP 20 
                FactID, FactType, FactText, ValidityRating, 
                CreatedDate, LastUpdated, Source
            FROM REF_FACT 
            WHERE FactText LIKE ? 
            AND ValidityRating >= ?
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 20 
                FactID, FactType, FactText, ValidityRating, 
                CreatedDate, LastUpdated, Source
            FROM REF_FACT 
            WHERE FactText LIKE '%' 
            AND ValidityRating > IS NOT NULL) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'IS'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.958195] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:139
Status: FAILED
Original SQL: SELECT 
                TABLE_SCHEMA,
                TABLE_NAME,
                TABLE_TYPE
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.966098] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:139
Status: FAILED
Original SQL: SELECT 
                TABLE_SCHEMA,
                TABLE_NAME,
                TABLE_TYPE
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.973647] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:161
Status: FAILED
Original SQL: SELECT 
                TABLE_SCHEMA,
                TABLE_NAME
            FROM INFORMATION_SCHEMA.VIEWS
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.979039] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:161
Status: FAILED
Original SQL: SELECT 
                TABLE_SCHEMA,
                TABLE_NAME
            FROM INFORMATION_SCHEMA.VIEWS
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:55.990011] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:181
Status: FAILED
Original SQL: SELECT 
                ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'PROCEDURE'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.003216] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:181
Status: FAILED
Original SQL: SELECT 
                ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'PROCEDURE'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.014846] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:203
Status: FAILED
Original SQL: SELECT 
                ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'FUNCTION'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.024135] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:203
Status: FAILED
Original SQL: SELECT 
                ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'FUNCTION'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.032829] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:255
Status: FAILED
Original SQL: SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                CHARACTER_MAXIMUM_LENGTH,
                NUMERIC_PRECISION,
                NUMERIC_SCALE,
                ORDINAL_POSITION
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?
            ORDER BY ORDINAL_POSITION
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.041590] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:255
Status: FAILED
Original SQL: SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                CHARACTER_MAXIMUM_LENGTH,
                NUMERIC_PRECISION,
                NUMERIC_SCALE,
                ORDINAL_POSITION
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?
            ORDER BY ORDINAL_POSITION
Test SQL: SELECT TOP 0 * FROM INFO
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFO'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.068094] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:287
Status: FAILED
Original SQL: SELECT
                i.name as index_name,
                i.type_desc as index_type,
                i.is_unique,
                i.is_primary_key,
                STRING_AGG(c.name, ', ') WITHIN GROUP (ORDER BY ic.key_ordinal) as columns
            FROM sys.indexes i
            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
            INNER JOIN sys.tables t ON i.object_id = t.object_id
            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
            WHERE t.name = ? AND s.name = ?
            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key
            ORDER BY i.name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.077788] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:287
Status: FAILED
Original SQL: SELECT
                i.name as index_name,
                i.type_desc as index_type,
                i.is_unique,
                i.is_primary_key,
                STRING_AGG(c.name, ', ') WITHIN GROUP (ORDER BY ic.key_ordinal) as columns
            FROM sys.indexes i
            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
            INNER JOIN sys.tables t ON i.object_id = t.object_id
            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
            WHERE t.name = ? AND s.name = ?
            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key
            ORDER BY i.name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.087610] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:318
Status: FAILED
Original SQL: SELECT
                fk.name as foreign_key_name,
                OBJECT_SCHEMA_NAME(fk.parent_object_id) as schema_name,
                OBJECT_NAME(fk.parent_object_id) as table_name,
                COL_NAME(fkc.parent_object_id, fkc.parent_column_id) as column_name,
                OBJECT_SCHEMA_NAME(fk.referenced_object_id) as referenced_schema,
                OBJECT_NAME(fk.referenced_object_id) as referenced_table,
                COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) as referenced_column
            FROM sys.foreign_keys fk
            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?
            ORDER BY fk.name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.098785] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:318
Status: FAILED
Original SQL: SELECT
                fk.name as foreign_key_name,
                OBJECT_SCHEMA_NAME(fk.parent_object_id) as schema_name,
                OBJECT_NAME(fk.parent_object_id) as table_name,
                COL_NAME(fkc.parent_object_id, fkc.parent_column_id) as column_name,
                OBJECT_SCHEMA_NAME(fk.referenced_object_id) as referenced_schema,
                OBJECT_NAME(fk.referenced_object_id) as referenced_table,
                COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) as referenced_column
            FROM sys.foreign_keys fk
            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?
            ORDER BY fk.name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.108868] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:348
Status: FAILED
Original SQL: SELECT COUNT(*) as row_count FROM [{schema_name}].[{table_name}]
Test SQL: SELECT TOP 1 * FROM (SELECT COUNT(*) as row_count FROM [{schema_name}].[{table_name}]) AS test_query
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.116059] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:439
Status: FAILED
Original SQL: SELECT TOP {limit} * FROM [{schema_name}].[{table_name}]
Test SQL: SELECT TOP 1 * FROM (SELECT TOP {limit} * FROM [{schema_name}].[{table_name}]) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-20T23:46:56.149014] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\distlib\manifest.py:258
Status: FAILED
Original SQL: Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.

        Patterns are not quite the same as implemented by the 'fnmatch'
        module: '*' and '?'  match non-special characters, where "special"
        is platform-dependent: slash on Unix; colon, slash, and backslash on
        DOS/Windows; and colon on Mac OS.

        If 'anchor' is true (the default), then the pattern match is more
        stringent: "*.py" will match "foo.py" but not "foo/bar.py".  If
        'anchor' is false, both of these will match.

        If 'prefix' is supplied, then only filenames starting with 'prefix'
        (itself a pattern) and ending with 'pattern', with anything in between
        them, will match.  'anchor' is ignored in this case.

        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and
        'pattern' is assumed to be either a string containing a regex or a
        regex object -- no translation is done, the regex is just compiled
        and used as-is.

        Selected strings will be added to self.files.

        Return True if files are found.
Test SQL: SELECT TOP 1 * FROM (Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.

        Patterns are not quite the same as implemented by the 'fnmatch'
        module: '*' and '?'  match non-special characters, where "special"
        is platform-dependent: slash on Unix; colon, slash, and backslash on
        DOS/Windows; and colon on Mac OS.

        If 'anchor' is true (the default), then the pattern match is more
        stringent: "*.py" will match "foo.py" but not "foo/bar.py".  If
        'anchor' is false, both of these will match.

        If 'prefix' is supplied, then only filenames starting with 'prefix'
        (itself a pattern) and ending with 'pattern', with anything in between
        them, will match.  'anchor' is ignored in this case.

        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and
        'pattern' is assumed to be either a string containing a regex or a
        regex object -- no translation is done, the regex is just compiled
        and used as-is.

        Selected strings will be added to self.files.

        Return True if files are found.) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)")
----------------------------------------

[2025-08-20T23:46:56.158377] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\distlib\manifest.py:258
Status: FAILED
Original SQL: Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.

        Patterns are not quite the same as implemented by the 'fnmatch'
        module: '*' and '?'  match non-special characters, where
Test SQL: SELECT TOP 1 * FROM (Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.

        Patterns are not quite the same as implemented by the 'fnmatch'
        module: '*' and '?'  match non-special characters, where) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.183073] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:208
Status: FAILED
Original SQL: Update just the width, return a copy.

        Args:
            width (int): New width (sets both min_width and max_width)

        Returns:
            ~ConsoleOptions: New console options instance.
Test SQL: SELECT TOP 0 * FROM just
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.192530] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:208
Status: FAILED
Original SQL: Update just the width, return a copy.

        Args:
            width (int): New width (sets both min_width and max_width)

        Returns:
            ~ConsoleOptions: New console options instance.
Test SQL: SELECT TOP 0 * FROM just
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.201282] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:244
Status: FAILED
Original SQL: Update the width and height, and return a copy.

        Args:
            width (int): New width (sets both min_width and max_width).
            height (int): New height.

        Returns:
            ~ConsoleOptions: New console options instance.
Test SQL: SELECT TOP 0 * FROM the
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.210445] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:244
Status: FAILED
Original SQL: Update the width and height, and return a copy.

        Args:
            width (int): New width (sets both min_width and max_width).
            height (int): New height.

        Returns:
            ~ConsoleOptions: New console options instance.
Test SQL: SELECT TOP 0 * FROM the
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.219359] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:1789
Status: FAILED
Original SQL: Update the screen at a given offset.

        Args:
            renderable (RenderableType): A Rich renderable.
            region (Region, optional): Region of screen to update, or None for entire screen. Defaults to None.
            x (int, optional): x offset. Defaults to 0.
            y (int, optional): y offset. Defaults to 0.

        Raises:
            errors.NoAltScreen: If the Console isn't in alt screen mode.
Test SQL: SELECT TOP 0 * FROM the
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.228588] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:1789
Status: FAILED
Original SQL: Update the screen at a given offset.

        Args:
            renderable (RenderableType): A Rich renderable.
            region (Region, optional): Region of screen to update, or None for entire screen. Defaults to None.
            x (int, optional): x offset. Defaults to 0.
            y (int, optional): y offset. Defaults to 0.

        Raises:
            errors.NoAltScreen: If the Console isn't in alt screen mode.
Test SQL: SELECT TOP 0 * FROM the
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.237905] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:1819
Status: FAILED
Original SQL: Update lines of the screen at a given offset.

        Args:
            lines (List[List[Segment]]): Rendered lines (as produced by :meth:`~rich.Console.render_lines`).
            x (int, optional): x offset (column no). Defaults to 0.
            y (int, optional): y offset (column no). Defaults to 0.

        Raises:
            errors.NoAltScreen: If the Console isn't in alt screen mode.
Test SQL: SELECT TOP 0 * FROM lines
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.246526] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:1819
Status: FAILED
Original SQL: Update lines of the screen at a given offset.

        Args:
            lines (List[List[Segment]]): Rendered lines (as produced by :meth:`~rich.Console.render_lines`).
            x (int, optional): x offset (column no). Defaults to 0.
            y (int, optional): y offset (column no). Defaults to 0.

        Raises:
            errors.NoAltScreen: If the Console isn't in alt screen mode.
Test SQL: SELECT TOP 0 * FROM lines
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.261707] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\progress.py:1413
Status: FAILED
Original SQL: Update information associated with a task.

        Args:
            task_id (TaskID): Task id (returned by add_task).
            total (float, optional): Updates task.total if not None.
            completed (float, optional): Updates task.completed if not None.
            advance (float, optional): Add a value to task.completed if not None.
            description (str, optional): Change task description if not None.
            visible (bool, optional): Set visible flag if not None.
            refresh (bool): Force a refresh of progress information. Default is False.
            **fields (Any): Additional data fields required for rendering.
Test SQL: SELECT TOP 0 * FROM information
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.270161] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\progress.py:1413
Status: FAILED
Original SQL: Update information associated with a task.

        Args:
            task_id (TaskID): Task id (returned by add_task).
            total (float, optional): Updates task.total if not None.
            completed (float, optional): Updates task.completed if not None.
            advance (float, optional): Add a value to task.completed if not None.
            description (str, optional): Change task description if not None.
            visible (bool, optional): Set visible flag if not None.
            refresh (bool): Force a refresh of progress information. Default is False.
            **fields (Any): Additional data fields required for rendering.
Test SQL: SELECT TOP 0 * FROM information
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.364991] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_distutils\filelist.py:203
Status: FAILED
Original SQL: Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns
        are not quite the same as implemented by the 'fnmatch' module: '*'
        and '?'  match non-special characters, where "special" is platform-
        dependent: slash on Unix; colon, slash, and backslash on
        DOS/Windows; and colon on Mac OS.

        If 'anchor' is true (the default), then the pattern match is more
        stringent: "*.py" will match "foo.py" but not "foo/bar.py".  If
        'anchor' is false, both of these will match.

        If 'prefix' is supplied, then only filenames starting with 'prefix'
        (itself a pattern) and ending with 'pattern', with anything in between
        them, will match.  'anchor' is ignored in this case.

        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and
        'pattern' is assumed to be either a string containing a regex or a
        regex object -- no translation is done, the regex is just compiled
        and used as-is.

        Selected strings will be added to self.files.

        Return True if files are found, False otherwise.
Test SQL: SELECT TOP 1 * FROM (Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns
        are not quite the same as implemented by the 'fnmatch' module: '*'
        and '?'  match non-special characters, where "special" is platform-
        dependent: slash on Unix; colon, slash, and backslash on
        DOS/Windows; and colon on Mac OS.

        If 'anchor' is true (the default), then the pattern match is more
        stringent: "*.py" will match "foo.py" but not "foo/bar.py".  If
        'anchor' is false, both of these will match.

        If 'prefix' is supplied, then only filenames starting with 'prefix'
        (itself a pattern) and ending with 'pattern', with anything in between
        them, will match.  'anchor' is ignored in this case.

        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and
        'pattern' is assumed to be either a string containing a regex or a
        regex object -- no translation is done, the regex is just compiled
        and used as-is.

        Selected strings will be added to self.files.

        Return True if files are found, False otherwise.) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)")
----------------------------------------

[2025-08-20T23:46:56.375682] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_distutils\filelist.py:203
Status: FAILED
Original SQL: Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns
        are not quite the same as implemented by the 'fnmatch' module: '*'
        and '?'  match non-special characters, where
Test SQL: SELECT TOP 1 * FROM (Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns
        are not quite the same as implemented by the 'fnmatch' module: '*'
        and '?'  match non-special characters, where) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.387483] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_vendor\importlib_metadata\__init__.py:349
Status: FAILED
Original SQL: Select entry points from self that match the
        given parameters (typically group and/or name).
Test SQL: SELECT TOP 0 * FROM s
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 's'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-20T23:46:56.396576] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_vendor\importlib_metadata\__init__.py:349
Status: FAILED
Original SQL: Select entry points from self that match the
        given parameters (typically group and/or name).
Test SQL: SELECT TOP 0 * FROM s
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 's'. (208) (SQLExecDirectW)")
----------------------------------------


Session ended: 2025-08-20T23:46:56.418510
================================================================================
