SQL Validation Log - Session 20250823_110612
Started: 2025-08-23T11:06:12.867807
================================================================================

[2025-08-23T11:06:13.128023] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:109
Status: SUCCESS
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
               COUNT(e.ID) as EvidenceCount,
               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ID, FactID, ValidityRating, Name, DataSource
----------------------------------------

[2025-08-23T11:06:13.130775] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:109
Status: SUCCESS
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
               COUNT(e.ID) as EvidenceCount,
               AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage
        FROM REF_Fact f
        LEFT JOIN REF_Evidence e ON f.ID = e.FactID
        WHERE f.DataSource LIKE '%Self-Healer%'
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ID, FactID, ValidityRating, Name, DataSource
----------------------------------------

[2025-08-23T11:06:13.132774] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:194
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Evidence, Columns: FactID, DataSource, Name, Evidence
----------------------------------------

[2025-08-23T11:06:13.134773] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:194
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Evidence, Columns: FactID, DataSource, Name, Evidence
----------------------------------------

[2025-08-23T11:06:13.137298] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:206
Status: SUCCESS
Original SQL: SELECT ValidityRating FROM REF_Fact WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: ID, ValidityRating
----------------------------------------

[2025-08-23T11:06:13.139303] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:218
Status: SUCCESS
Original SQL: SELECT COUNT(*) as EvidenceCount FROM REF_Evidence WHERE FactID = ?
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Evidence, Columns: FactID
----------------------------------------

[2025-08-23T11:06:13.141303] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:226
Status: SUCCESS
Original SQL: UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: ID, ValidityRating
----------------------------------------

[2025-08-23T11:06:13.143302] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:260
Status: SUCCESS
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   'fact' as KnowledgeType
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: CreateDate, ID, ValidityRating, fact, Name, DataSource
----------------------------------------

[2025-08-23T11:06:13.144302] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:260
Status: SUCCESS
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   'fact' as KnowledgeType
            FROM REF_Fact f
            WHERE (f.Name LIKE ? OR f.DataSource LIKE ?)
            AND f.DataSource LIKE '%Self-Healer%'
            AND f.ValidityRating >= ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: CreateDate, ID, ValidityRating, fact, Name, DataSource
----------------------------------------

[2025-08-23T11:06:13.147363] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:276
Status: SUCCESS
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
                   'opinion' as KnowledgeType
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT TOP 0 * FROM REF_Opinion
Result: SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ID, opinion, ValidityRating, Name, Opinion, DataSource
----------------------------------------

[2025-08-23T11:06:13.149275] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:276
Status: SUCCESS
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate,
                   'opinion' as KnowledgeType
            FROM REF_Opinion o
            WHERE (o.Name LIKE ? OR o.Opinion LIKE ? OR o.DataSource LIKE ?)
            AND o.DataSource LIKE '%Self-Healer%'
            AND o.ValidityRating >= ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT TOP 0 * FROM REF_Opinion
Result: SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ID, opinion, ValidityRating, Name, Opinion, DataSource
----------------------------------------

[2025-08-23T11:06:13.151274] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:292
Status: SUCCESS
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
                   f.ValidityRating, 'evidence' as KnowledgeType
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ID, FactID, Evidence, ValidityRating, evidence, Name, DataSource
----------------------------------------

[2025-08-23T11:06:13.153778] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:292
Status: SUCCESS
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate,
                   f.ValidityRating, 'evidence' as KnowledgeType
            FROM REF_Evidence e
            JOIN REF_Fact f ON e.FactID = f.ID
            WHERE (e.Name LIKE ? OR e.Evidence LIKE ? OR e.DataSource LIKE ?)
            AND e.DataSource LIKE '%Self-Healer%'
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ID, FactID, Evidence, ValidityRating, evidence, Name, DataSource
----------------------------------------

[2025-08-23T11:06:13.154781] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:329
Status: SUCCESS
Original SQL: SELECT 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END as Category,
            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: DataSource, ValidityRating
----------------------------------------

[2025-08-23T11:06:13.154781] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\api\knowledge_endpoints.py:329
Status: SUCCESS
Original SQL: SELECT 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END as Category,
            AVG(CAST(f.ValidityRating as FLOAT)) as AvgEffectiveness
        FROM REF_Fact f
        WHERE f.DataSource LIKE '%Self-Healer%'
        GROUP BY 
            CASE 
                WHEN f.Name LIKE '%JSON%' THEN 'JSON_Parsing'
                WHEN f.Name LIKE '%Database%' THEN 'Database'
                WHEN f.Name LIKE '%Network%' THEN 'Network'
                WHEN f.Name LIKE '%Configuration%' THEN 'Configuration'
                WHEN f.Name LIKE '%Workflow%' THEN 'Workflow'
                ELSE 'Other'
            END
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: DataSource, ValidityRating
----------------------------------------

[2025-08-23T11:06:13.154781] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:241
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, ValidityRating, DataSource) 
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: DataSource, Name, ValidityRating
----------------------------------------

[2025-08-23T11:06:13.154781] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:241
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, ValidityRating, DataSource) 
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: DataSource, Name, ValidityRating
----------------------------------------

[2025-08-23T11:06:13.154781] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:267
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Evidence, Columns: FactID, DataSource, Name, Evidence
----------------------------------------

[2025-08-23T11:06:13.172218] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:267
Status: SUCCESS
Original SQL: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Evidence, Columns: FactID, DataSource, Name, Evidence
----------------------------------------

[2025-08-23T11:06:13.173945] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:374
Status: SUCCESS
Original SQL: SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: ID, FactID, Name, ValidityRating
----------------------------------------

[2025-08-23T11:06:13.175945] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:374
Status: SUCCESS
Original SQL: SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: ID, FactID, Name, ValidityRating
----------------------------------------

[2025-08-23T11:06:13.176946] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:396
Status: SUCCESS
Original SQL: UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: ID, ValidityRating
----------------------------------------

[2025-08-23T11:06:13.179673] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:410
Status: SUCCESS
Original SQL: SELECT ID FROM REF_Entity WHERE Name = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-23T11:06:13.182551] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:419
Status: SUCCESS
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: CreateDate, ID, ValidityRating, Name, DataSource
----------------------------------------

[2025-08-23T11:06:13.186157] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:419
Status: SUCCESS
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: CreateDate, ID, ValidityRating, Name, DataSource
----------------------------------------

[2025-08-23T11:06:13.188906] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:430
Status: SUCCESS
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
                FROM REF_Evidence e
                WHERE e.FactID = ?
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Evidence, Columns: CreateDate, ID, FactID, Evidence, Name, DataSource
----------------------------------------

[2025-08-23T11:06:13.190840] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:430
Status: SUCCESS
Original SQL: SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
                FROM REF_Evidence e
                WHERE e.FactID = ?
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Evidence, Columns: CreateDate, ID, FactID, Evidence, Name, DataSource
----------------------------------------

[2025-08-23T11:06:13.194810] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:439
Status: FAILED
Original SQL: SELECT a.Name as AttributeName, ev.EntityValue, eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Test SQL: SELECT TOP 0 * FROM XRF_EntityAttributeValue
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.197501] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:439
Status: FAILED
Original SQL: SELECT a.Name as AttributeName, ev.EntityValue, eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
Test SQL: SELECT TOP 0 * FROM XRF_EntityAttributeValue
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.200138] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:465
Status: SUCCESS
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ID, FactID, ValidityRating, Name, DataSource
----------------------------------------

[2025-08-23T11:06:13.203340] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:465
Status: SUCCESS
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: CreateDate, ID, FactID, ValidityRating, Name, DataSource
----------------------------------------

[2025-08-23T11:06:13.205892] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:477
Status: SUCCESS
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT TOP 0 * FROM REF_Opinion
Result: SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ID, ValidityRating, Name, Opinion, DataSource
----------------------------------------

[2025-08-23T11:06:13.207422] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:477
Status: SUCCESS
Original SQL: SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
Test SQL: SELECT TOP 0 * FROM REF_Opinion
Result: SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ID, ValidityRating, Name, Opinion, DataSource
----------------------------------------

[2025-08-23T11:06:13.208463] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:552
Status: SUCCESS
Original SQL: SELECT ID FROM REF_Category WHERE Name = ?
Test SQL: SELECT TOP 0 * FROM REF_Category
Result: SUCCESS - Tables: REF_Category, Columns: ID, Name
----------------------------------------

[2025-08-23T11:06:13.208463] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:559
Status: SUCCESS
Original SQL: INSERT INTO REF_Category (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT TOP 0 * FROM REF_Category
Result: SUCCESS - Tables: REF_Category, Columns: Name
----------------------------------------

[2025-08-23T11:06:13.215869] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:566
Status: SUCCESS
Original SQL: SELECT ID FROM REF_Entity WHERE Name = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-23T11:06:13.218962] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:573
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-23T11:06:13.222404] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:580
Status: SUCCESS
Original SQL: SELECT ID FROM REF_Attribute WHERE Name = ?
Test SQL: SELECT TOP 0 * FROM REF_Attribute
Result: SUCCESS - Tables: REF_Attribute, Columns: ID, Name
----------------------------------------

[2025-08-23T11:06:13.224404] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:587
Status: SUCCESS
Original SQL: INSERT INTO REF_Attribute (Name) OUTPUT INSERTED.ID VALUES (?)
Test SQL: SELECT TOP 0 * FROM REF_Attribute
Result: SUCCESS - Tables: REF_Attribute, Columns: Name
----------------------------------------

[2025-08-23T11:06:13.224404] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:594
Status: SUCCESS
Original SQL: SELECT ID FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?
Test SQL: SELECT TOP 0 * FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: ID, Name, EntityValue
----------------------------------------

[2025-08-23T11:06:13.224404] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:602
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)
            OUTPUT INSERTED.ID
            VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, NumericValue, Name, ValueUnits
----------------------------------------

[2025-08-23T11:06:13.224404] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:602
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)
            OUTPUT INSERTED.ID
            VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, NumericValue, Name, ValueUnits
----------------------------------------

[2025-08-23T11:06:13.224404] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:610
Status: SUCCESS
Original SQL: INSERT INTO REF_EntityValue (Name, EntityValue) OUTPUT INSERTED.ID VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, Name
----------------------------------------

[2025-08-23T11:06:13.224404] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:620
Status: FAILED
Original SQL: SELECT ID FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Test SQL: SELECT TOP 0 * FROM XRF_EntityAttributeValue
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.250916] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:620
Status: FAILED
Original SQL: SELECT ID FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
Test SQL: SELECT TOP 0 * FROM XRF_EntityAttributeValue
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.254933] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:630
Status: FAILED
Original SQL: INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)
        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM XRF_EntityAttributeValue
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.258133] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py:630
Status: FAILED
Original SQL: INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)
        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)
Test SQL: SELECT TOP 0 * FROM XRF_EntityAttributeValue
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.264985] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:855
Status: SUCCESS
Original SQL: SELECT ID, Name, CreateDate
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, CreateDate, Name
----------------------------------------

[2025-08-23T11:06:13.266989] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:855
Status: SUCCESS
Original SQL: SELECT ID, Name, CreateDate
            FROM REF_Entity
            WHERE Name = ? OR Name LIKE ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, CreateDate, Name
----------------------------------------

[2025-08-23T11:06:13.270011] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:872
Status: FAILED
Original SQL: SELECT
                a.Name as AttributeName,
                ev.EntityValue as AttributeValue,
                ev.NumericValue,
                ev.ValueUnits,
                ev.Name as ValueName,
                eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Test SQL: SELECT TOP 0 * FROM XRF_EntityAttributeValue
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.273369] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:872
Status: FAILED
Original SQL: SELECT
                a.Name as AttributeName,
                ev.EntityValue as AttributeValue,
                ev.NumericValue,
                ev.ValueUnits,
                ev.Name as ValueName,
                eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attribute a ON eav.AttributeID = a.ID
            JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            ORDER BY a.Name
Test SQL: SELECT TOP 0 * FROM XRF_EntityAttributeValue
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.276370] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:914
Status: SUCCESS
Original SQL: SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: CreateDate, DataSource, Name, ValidityRating
----------------------------------------

[2025-08-23T11:06:13.279610] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:914
Status: SUCCESS
Original SQL: SELECT f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            ORDER BY f.ValidityRating DESC
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: CreateDate, DataSource, Name, ValidityRating
----------------------------------------

[2025-08-23T11:06:13.284892] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:926
Status: SUCCESS
Original SQL: SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Evidence, Columns: CreateDate, DataSource, Name, Evidence
----------------------------------------

[2025-08-23T11:06:13.286892] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:926
Status: SUCCESS
Original SQL: SELECT e.Name, e.Evidence, e.DataSource, e.CreateDate
            FROM REF_Evidence e
            WHERE e.Name LIKE ? OR e.DataSource LIKE ?
            ORDER BY e.CreateDate DESC
Test SQL: SELECT TOP 0 * FROM REF_Evidence
Result: SUCCESS - Tables: REF_Evidence, Columns: CreateDate, DataSource, Name, Evidence
----------------------------------------

[2025-08-23T11:06:13.286892] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:991
Status: SUCCESS
Original SQL: SELECT Name, ValidityRating, DataSource, CreateDate
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: CreateDate, DataSource, Name, ValidityRating
----------------------------------------

[2025-08-23T11:06:13.286892] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:991
Status: SUCCESS
Original SQL: SELECT Name, ValidityRating, DataSource, CreateDate
            FROM REF_Fact
            WHERE Name LIKE ? OR DataSource LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: CreateDate, DataSource, Name, ValidityRating
----------------------------------------

[2025-08-23T11:06:13.304254] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1009
Status: SUCCESS
Original SQL: SELECT Name, ValidityRating, Opinion, DataSource, CreateDate
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT TOP 0 * FROM REF_Opinion
Result: SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ValidityRating, Name, Opinion, DataSource
----------------------------------------

[2025-08-23T11:06:13.307258] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1009
Status: SUCCESS
Original SQL: SELECT Name, ValidityRating, Opinion, DataSource, CreateDate
            FROM REF_Opinion
            WHERE Name LIKE ? OR Opinion LIKE ?
            ORDER BY ValidityRating DESC
Test SQL: SELECT TOP 0 * FROM REF_Opinion
Result: SUCCESS - Tables: REF_Opinion, Columns: CreateDate, ValidityRating, Name, Opinion, DataSource
----------------------------------------

[2025-08-23T11:06:13.310005] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1021
Status: SUCCESS
Original SQL: SELECT DISTINCT e.Name, e.CreateDate
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: EntityID, CreateDate, EntityValue, ID, EntityValueID, Name, AttributeID
----------------------------------------

[2025-08-23T11:06:13.312884] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py:1021
Status: SUCCESS
Original SQL: SELECT DISTINCT e.Name, e.CreateDate
            FROM REF_Entity e
            WHERE e.Name LIKE 'Session_%'
            AND EXISTS (
                SELECT 1 FROM XRF_EntityAttributeValue eav
                JOIN REF_Attribute a ON eav.AttributeID = a.ID
                JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID
                WHERE eav.EntityID = e.ID
                AND (ev.EntityValue LIKE ? OR a.Name LIKE ?)
            )
            ORDER BY e.CreateDate DESC
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: EntityID, CreateDate, EntityValue, ID, EntityValueID, Name, AttributeID
----------------------------------------

[2025-08-23T11:06:13.316547] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:34
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.320455] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:34
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = REF_EntityValue
        ORDER BY ORDINAL_POSITION
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.324606] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:66
Status: FAILED
Original SQL: SELECT name, create_date, modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.327605] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:66
Status: FAILED
Original SQL: SELECT name, create_date, modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%' OR name LIKE 'S_SYS_%' OR name LIKE 'z_S_SYS_%'
        ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.332967] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:93
Status: FAILED
Original SQL: SELECT name FROM sys.procedures WHERE name = '{proc_name}'
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.335344] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:106
Status: FAILED
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.338344] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:106
Status: FAILED
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME LIKE 'REF_%'
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.342384] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py:119
Status: FAILED
Original SQL: SELECT COUNT(*) as row_count FROM {table_name}
Test SQL: SELECT TOP 1 * FROM (SELECT COUNT(*) as row_count FROM {table_name}) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-23T11:06:13.347387] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:9
Status: FAILED
Original SQL: SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.350980] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:17
Status: SUCCESS
Original SQL: SELECT TOP 5 * FROM REF_Entity WHERE Name LIKE 'Session_%'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-23T11:06:13.353141] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:26
Status: SUCCESS
Original SQL: SELECT TOP 10 * FROM REF_Entity ORDER BY CreateDate DESC
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-23T11:06:13.357147] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py:33
Status: FAILED
Original SQL: SELECT TOP 5 * FROM XRF_EntityAttributeValue
Test SQL: SELECT TOP 0 * FROM XRF_EntityAttributeValue
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'XRF_EntityAttributeValue'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.361896] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py:48
Status: FAILED
Original SQL: SELECT name, create_date 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.366246] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py:48
Status: FAILED
Original SQL: SELECT name, create_date 
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.370353] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql:113
Status: FAILED
Original SQL: SELECT @count = COUNT(*) FROM
Test SQL: SELECT TOP 1 * FROM (SELECT @count = COUNT(*) FROM) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Must declare the scalar variable "@count". (137) (SQLExecDirectW)')
----------------------------------------

[2025-08-23T11:06:13.407412] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py:117
Status: FAILED
Original SQL: SELECT * FROM \1 ORDER BY
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM \1 ORDER BY) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near '\\1'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.413666] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:37
Status: FAILED
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.418294] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:37
Status: FAILED
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE' 
        AND TABLE_NAME LIKE 'REF_%' 
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.421784] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:77
Status: SUCCESS
Original SQL: SELECT TOP 1 * FROM REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-23T11:06:13.424870] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py:87
Status: SUCCESS
Original SQL: SELECT TOP 1 * FROM REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-23T11:06:13.433670] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:268
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-23T11:06:13.437418] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\database_repair_logger.py:269
Status: SUCCESS
Original SQL: SELECT 1 FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-23T11:06:13.444297] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:31
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-23T11:06:13.447283] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:35
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) 
             VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: EntityID, Name, Value
----------------------------------------

[2025-08-23T11:06:13.450573] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:35
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) 
             VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: EntityID, Name, Value
----------------------------------------

[2025-08-23T11:06:13.454077] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:39
Status: FAILED
Original SQL: SELECT your option from the menu
Test SQL: SELECT TOP 0 * FROM the
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.457860] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:53
Status: FAILED
Original SQL: SELECT * FROM users WHERE active = 1
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.459861] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:54
Status: FAILED
Original SQL: UPDATE users SET status = ? WHERE id = ?
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.464908] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\demonstrate_sql_refinement.py:55
Status: FAILED
Original SQL: DELETE FROM logs WHERE created_at < ?
Test SQL: SELECT TOP 0 * FROM logs
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'logs'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.504592] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:208
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = \'test\'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-23T11:06:13.505592] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:209
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Value, Name
----------------------------------------

[2025-08-23T11:06:13.505592] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:209
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (Name, Value) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: Value, Name
----------------------------------------

[2025-08-23T11:06:13.505592] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:210
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-23T11:06:13.505592] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:211
Status: SUCCESS
Original SQL: DELETE FROM REF_Entity WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID
----------------------------------------

[2025-08-23T11:06:13.524748] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:212
Status: SUCCESS
Original SQL: SELECT COUNT(*) FROM REF_Entity JOIN REF_Fact ON REF_Entity.ID = REF_Fact.EntityID
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Fact, REF_Entity, Columns: ID, EntityID
----------------------------------------

[2025-08-23T11:06:13.529351] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:218
Status: FAILED
Original SQL: SELECT your option FROM the menu
Test SQL: SELECT TOP 0 * FROM the
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.534152] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:228
Status: FAILED
Original SQL: SELECT * FROM
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ')'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.537468] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\refined_sql_detector.py:230
Status: FAILED
Original SQL: SELECT name FROM users WHERE active = 1 ORDER BY name
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.562724] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:122
Status: FAILED
Original SQL: SELECT\s+(.+?)(?=\s+FROM)
Test SQL: SELECT TOP 1 * FROM (SELECT\s+(.+?)(?=\s+FROM)) AS test_query
Result: FAILED - ('07002', '[07002] [Microsoft][ODBC Driver 18 for SQL Server]COUNT field incorrect or syntax error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-23T11:06:13.566746] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:145
Status: FAILED
Original SQL: DELETE FROM table
Test SQL: SELECT TOP 0 * FROM table
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'table'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.571028] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:158
Status: FAILED
Original SQL: SELECT Name FROM Users
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.573970] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:566
Status: FAILED
Original SQL: SELECT TOP 0 * FROM {table_name}
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 0 * FROM {table_name}) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-23T11:06:13.579684] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:580
Status: FAILED
Original SQL: SELECT TOP 1 * FROM ({test_sql}) AS test_query
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 1 * FROM ({test_sql}) AS test_query) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-23T11:06:13.583645] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:587
Status: FAILED
Original SQL: SELECT TOP 0 * FROM {table_name}
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 0 * FROM {table_name}) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-23T11:06:13.586648] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:594
Status: FAILED
Original SQL: SELECT TOP 0 * FROM {table_name}
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 0 * FROM {table_name}) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-23T11:06:13.591233] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:601
Status: FAILED
Original SQL: SELECT TOP 0 * FROM {table_name}
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 0 * FROM {table_name}) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-23T11:06:13.595691] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:608
Status: FAILED
Original SQL: SELECT TOP 0 * FROM {table_name}
Test SQL: SELECT TOP 1 * FROM (SELECT TOP 0 * FROM {table_name}) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-23T11:06:13.600182] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\sql_validator.py:716
Status: FAILED
Original SQL: SELECT 1 FROM {tables[0]} WHERE 1=0
Test SQL: SELECT TOP 1 * FROM (SELECT 1 FROM {tables[0]} WHERE 1=0) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-23T11:06:13.606303] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:39
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-23T11:06:13.609061] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:42
Status: FAILED
Original SQL: SELECT your option from the menu
Test SQL: SELECT TOP 0 * FROM the
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.614354] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:45
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) 
    VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: EntityID, Name, Value
----------------------------------------

[2025-08-23T11:06:13.617693] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:45
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) 
    VALUES (?, ?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: EntityID, Name, Value
----------------------------------------

[2025-08-23T11:06:13.623800] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_refined_detection.py:54
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-23T11:06:13.628937] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py:36
Status: FAILED
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.632947] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\test_schema_validator.py:36
Status: FAILED
Original SQL: SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.641250] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:111
Status: FAILED
Original SQL: SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.645675] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:111
Status: FAILED
Original SQL: SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.650492] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:131
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.656255] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_database_schema_references.py:131
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = '{table_name}'
                ORDER BY ORDINAL_POSITION
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.662195] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:12
Status: FAILED
Original SQL: SELECT 
            name,
            create_date,
            modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.666583] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:12
Status: FAILED
Original SQL: SELECT 
            name,
            create_date,
            modify_date
        FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.668607] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:83
Status: FAILED
Original SQL: SELECT name FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.675122] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\validate_stored_procedures.py:83
Status: FAILED
Original SQL: SELECT name FROM sys.procedures 
        WHERE name LIKE '%SelfHealer%'
        ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.693404] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:45
Status: FAILED
Original SQL: SELECT your option from the menu
Test SQL: SELECT TOP 0 * FROM the
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.702742] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:66
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *
----------------------------------------

[2025-08-23T11:06:13.704741] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:67
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-23T11:06:13.704741] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:68
Status: SUCCESS
Original SQL: SELECT COUNT(*) FROM REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-23T11:06:13.716954] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:71
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Value) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Value, Name
----------------------------------------

[2025-08-23T11:06:13.721893] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:72
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) VALUES (1, 'test', 'value')
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: EntityID, Name, Value
----------------------------------------

[2025-08-23T11:06:13.725009] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:75
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-23T11:06:13.725009] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:76
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Status = 'active' WHERE Name = 'test'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Name, Status
----------------------------------------

[2025-08-23T11:06:13.733714] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:79
Status: SUCCESS
Original SQL: DELETE FROM REF_Entity WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID
----------------------------------------

[2025-08-23T11:06:13.739385] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:80
Status: SUCCESS
Original SQL: DELETE FROM REF_Entity WHERE CreatedDate < '2023-01-01'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreatedDate
----------------------------------------

[2025-08-23T11:06:13.742897] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:90
Status: SUCCESS
Original SQL: SELECT e.ID, e.Name, f.Value
    FROM REF_Entity e
    JOIN REF_Fact f ON e.ID = f.EntityID
    WHERE e.Status = 'active'
    ORDER BY e.Name
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Fact, REF_Entity, Columns: EntityID, ID, Value, Name, Status
----------------------------------------

[2025-08-23T11:06:13.748487] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:90
Status: SUCCESS
Original SQL: SELECT e.ID, e.Name, f.Value
    FROM REF_Entity e
    JOIN REF_Fact f ON e.ID = f.EntityID
    WHERE e.Status = 'active'
    ORDER BY e.Name
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Fact, REF_Entity, Columns: EntityID, ID, Value, Name, Status
----------------------------------------

[2025-08-23T11:06:13.752402] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:99
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, Status, CreatedDate)
    VALUES 
        ('Entity1', 'First entity', 'active', GETDATE()),
        ('Entity2', 'Second entity', 'inactive', GETDATE()),
        ('Entity3', 'Third entity', 'active', GETDATE())
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreatedDate, Name, Status, Description
----------------------------------------

[2025-08-23T11:06:13.754405] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:99
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, Status, CreatedDate)
    VALUES 
        ('Entity1', 'First entity', 'active', GETDATE()),
        ('Entity2', 'Second entity', 'inactive', GETDATE()),
        ('Entity3', 'Third entity', 'active', GETDATE())
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreatedDate, Name, Status, Description
----------------------------------------

[2025-08-23T11:06:13.754405] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:108
Status: SUCCESS
Original SQL: UPDATE REF_Entity 
    SET Status = 'archived',
        ModifiedDate = GETDATE()
    WHERE CreatedDate < DATEADD(year, -1, GETDATE())
        AND Status = 'inactive'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreatedDate, Status, ModifiedDate
----------------------------------------

[2025-08-23T11:06:13.766223] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:108
Status: SUCCESS
Original SQL: UPDATE REF_Entity 
    SET Status = 'archived',
        ModifiedDate = GETDATE()
    WHERE CreatedDate < DATEADD(year, -1, GETDATE())
        AND Status = 'inactive'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreatedDate, Status, ModifiedDate
----------------------------------------

[2025-08-23T11:06:13.769768] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:117
Status: SUCCESS
Original SQL: DELETE FROM REF_Fact 
    WHERE EntityID IN (
        SELECT ID FROM REF_Entity 
        WHERE Status = 'deleted'
    )
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: EntityID, Status
----------------------------------------

[2025-08-23T11:06:13.769768] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:117
Status: SUCCESS
Original SQL: DELETE FROM REF_Fact 
    WHERE EntityID IN (
        SELECT ID FROM REF_Entity 
        WHERE Status = 'deleted'
    )
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: EntityID, Status
----------------------------------------

[2025-08-23T11:06:13.779293] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:135
Status: SUCCESS
Original SQL: SELECT 
        e.ID,
        e.Name,
        e.Description,
        
        f.Value,
        f.CreatedDate
        
    FROM REF_Entity e
    
    LEFT JOIN REF_Fact f 
        ON e.ID = f.EntityID
        
    WHERE e.Status = 'active'
        AND f.Value IS NOT NULL
        
    ORDER BY 
        e.Name ASC,
        f.CreatedDate DESC
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Fact, REF_Entity, Columns: EntityID, ID, CreatedDate, Description, Value, Name, Status
----------------------------------------

[2025-08-23T11:06:13.779293] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:135
Status: SUCCESS
Original SQL: SELECT 
        e.ID,
        e.Name,
        e.Description,
        
        f.Value,
        f.CreatedDate
        
    FROM REF_Entity e
    
    LEFT JOIN REF_Fact f 
        ON e.ID = f.EntityID
        
    WHERE e.Status = 'active'
        AND f.Value IS NOT NULL
        
    ORDER BY 
        e.Name ASC,
        f.CreatedDate DESC
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Fact, REF_Entity, Columns: EntityID, ID, CreatedDate, Description, Value, Name, Status
----------------------------------------

[2025-08-23T11:06:13.779293] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:158
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity 
    (
        Name,
        Description,
        Status,
        CreatedDate
    )
    VALUES 
    (
        ?,
        ?,
        'active',
        GETDATE()
    )
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreatedDate, Name, Status, Description
----------------------------------------

[2025-08-23T11:06:13.794318] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:158
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity 
    (
        Name,
        Description,
        Status,
        CreatedDate
    )
    VALUES 
    (
        ?,
        ?,
        'active',
        GETDATE()
    )
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreatedDate, Name, Status, Description
----------------------------------------

[2025-08-23T11:06:13.794903] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:194
Status: SUCCESS
Original SQL: SELECT 'This contains SELECT keyword' FROM REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: This, keyword, contains
----------------------------------------

[2025-08-23T11:06:13.794903] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:195
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name) VALUES ('UPDATE this later')
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-23T11:06:13.807903] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:198
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity  -- This is a comment with SELECT
    WHERE Name = 'test'  /* Another comment with INSERT */
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name, with
----------------------------------------

[2025-08-23T11:06:13.813746] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:198
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity  -- This is a comment with SELECT
    WHERE Name = 'test'  /* Another comment with INSERT */
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name, with
----------------------------------------

[2025-08-23T11:06:13.817787] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:205
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-23T11:06:13.822979] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:208
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'It\\'s a test'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-23T11:06:13.827050] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:211
Status: SUCCESS
Original SQL: SELECT 
        e1.ID as Entity1_ID,
        e1.Name as Entity1_Name,
        e2.ID as Entity2_ID,
        e2.Name as Entity2_Name,
        f1.Value as Fact1_Value,
        f2.Value as Fact2_Value
    FROM REF_Entity e1
    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID
    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID
    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID
    WHERE e1.Status = 'active'
        AND f1.Type = 'primary'
        AND (e2.Status IS NULL OR e2.Status = 'active')
    ORDER BY e1.Name, e2.Name
    LIMIT 100
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Fact, REF_Entity, Columns: EntityID, ID, Value, Name, Type, Status, ParentID
----------------------------------------

[2025-08-23T11:06:13.832391] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:211
Status: SUCCESS
Original SQL: SELECT 
        e1.ID as Entity1_ID,
        e1.Name as Entity1_Name,
        e2.ID as Entity2_ID,
        e2.Name as Entity2_Name,
        f1.Value as Fact1_Value,
        f2.Value as Fact2_Value
    FROM REF_Entity e1
    INNER JOIN REF_Fact f1 ON e1.ID = f1.EntityID
    LEFT JOIN REF_Entity e2 ON e1.ParentID = e2.ID
    LEFT JOIN REF_Fact f2 ON e2.ID = f2.EntityID
    WHERE e1.Status = 'active'
        AND f1.Type = 'primary'
        AND (e2.Status IS NULL OR e2.Status = 'active')
    ORDER BY e1.Name, e2.Name
    LIMIT 100
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Fact, REF_Entity, Columns: EntityID, ID, Value, Name, Type, Status, ParentID
----------------------------------------

[2025-08-23T11:06:13.837390] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:236
Status: FAILED
Original SQL: SELECT * FROM users WHERE id = ?
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.842397] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:237
Status: FAILED
Original SQL: INSERT INTO users (name, email) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.847732] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:238
Status: FAILED
Original SQL: UPDATE users SET email = ? WHERE id = ?
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.852339] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:239
Status: FAILED
Original SQL: DELETE FROM users WHERE id = ?
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.857475] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:243
Status: FAILED
Original SQL: SELECT COUNT(*) FROM transactions WHERE DATE(created_at) = CURDATE()
Test SQL: SELECT TOP 0 * FROM transactions
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'transactions'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.862475] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:244
Status: FAILED
Original SQL: SELECT SUM(amount) FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())
Test SQL: SELECT TOP 0 * FROM transactions
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'transactions'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.866462] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:269
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *
----------------------------------------

[2025-08-23T11:06:13.872386] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:285
Status: FAILED
Original SQL: SELECT * FROM
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ')'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.879031] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:286
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-23T11:06:13.882862] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:287
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-23T11:06:13.888312] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:291
Status: FAILED
Original SQL: SELECT INSERT UPDATE DELETE FROM WHERE
Test SQL: SELECT TOP 0 * FROM WHERE
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'WHERE'. (156) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.893660] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:305
Status: SUCCESS
Original SQL: SELECT COUNT(*) FROM REF_Entity WHERE Status = 'test'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Status
----------------------------------------

[2025-08-23T11:06:13.895660] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:309
Status: SUCCESS
Original SQL: SELECT 
        'Test completed successfully' as message,
        COUNT(*) as total_entities
    FROM REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: successfully, completed, Test
----------------------------------------

[2025-08-23T11:06:13.903763] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\comprehensive_sql_test_file.py:309
Status: SUCCESS
Original SQL: SELECT 
        'Test completed successfully' as message,
        COUNT(*) as total_entities
    FROM REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: successfully, completed, Test
----------------------------------------

[2025-08-23T11:06:13.912066] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:21
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *
----------------------------------------

[2025-08-23T11:06:13.912066] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:22
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-23T11:06:13.924103] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:23
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-23T11:06:13.929505] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:32
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity 
WHERE Status = 'active'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *, Status
----------------------------------------

[2025-08-23T11:06:13.929505] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\simple_test.py:32
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity 
WHERE Status = 'active'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *, Status
----------------------------------------

[2025-08-23T11:06:13.941148] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:62
Status: FAILED
Original SQL: SELECT Name, ID FROM Users
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.944069] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:71
Status: FAILED
Original SQL: SELECT u.Name, u.ID FROM Users u
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.951445] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:80
Status: FAILED
Original SQL: SELECT u.Name, u.ID FROM Users AS u
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.957638] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:89
Status: FAILED
Original SQL: SELECT * FROM Products
Test SQL: SELECT TOP 0 * FROM Products
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Products'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.962949] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:98
Status: FAILED
Original SQL: SELECT * FROM Products WHERE Price > 100
Test SQL: SELECT TOP 0 * FROM Products
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Products'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.969066] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:107
Status: FAILED
Original SQL: SELECT * FROM Products p WHERE p.Active = 1
Test SQL: SELECT TOP 0 * FROM Products
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Products'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.975084] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:116
Status: FAILED
Original SQL: SELECT Name, Email FROM Users WHERE Active = 1
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.980231] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:125
Status: FAILED
Original SQL: SELECT u.Name, u.Email FROM Users u WHERE u.Active = 1
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.985956] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:135
Status: FAILED
Original SQL: SELECT u.Name FROM dbo.Users u WHERE u.Status = 'Active'
Test SQL: SELECT TOP 0 * FROM dbo
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.991454] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:149
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u INNER JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:13.999546] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:158
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.004919] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:168
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u LEFT JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.010824] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:177
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u LEFT OUTER JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.015867] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:187
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u RIGHT JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.020871] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:197
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u FULL JOIN Posts p ON u.ID = p.UserID
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.027717] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:207
Status: FAILED
Original SQL: SELECT u.Name, p.Title, c.Content FROM Users u JOIN Posts p ON u.ID = p.UserID JOIN Comments c ON p.ID = c.PostID
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.032936] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:217
Status: FAILED
Original SQL: SELECT u.Name, p.Title, c.Content FROM Users u LEFT JOIN Posts p ON u.ID = p.UserID INNER JOIN Comments c ON p.ID = c.PostID
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.039541] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:227
Status: FAILED
Original SQL: SELECT u.Name, p.Title FROM Users u, Posts p WHERE u.ID = p.UserID
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.045909] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:236
Status: FAILED
Original SQL: SELECT t1.Name, t2.Title, t3.Content FROM Table1 t1, Table2 t2, Table3 t3 WHERE t1.ID = t2.T1_ID AND t2.ID = t3.T2_ID
Test SQL: SELECT TOP 0 * FROM Table1
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Table1'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.050970] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:250
Status: FAILED
Original SQL: SELECT Department, COUNT(*) as EmployeeCount FROM Employees GROUP BY Department
Test SQL: SELECT TOP 0 * FROM Employees
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.057382] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:259
Status: FAILED
Original SQL: SELECT Department, JobTitle, COUNT(*) as Count FROM Employees GROUP BY Department, JobTitle
Test SQL: SELECT TOP 0 * FROM Employees
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.063391] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:269
Status: FAILED
Original SQL: SELECT Department, COUNT(*) as EmployeeCount FROM Employees GROUP BY Department HAVING COUNT(*) > 5
Test SQL: SELECT TOP 0 * FROM Employees
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.068285] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:279
Status: FAILED
Original SQL: SELECT Name, Salary FROM Employees ORDER BY Salary DESC
Test SQL: SELECT TOP 0 * FROM Employees
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.075012] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:288
Status: FAILED
Original SQL: SELECT Name, Department, Salary FROM Employees ORDER BY Department ASC, Salary DESC
Test SQL: SELECT TOP 0 * FROM Employees
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.080695] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:298
Status: FAILED
Original SQL: SELECT e.Department, COUNT(e.ID) as EmployeeCount, AVG(e.Salary) as AvgSalary FROM Employees e WHERE e.Active = 1 GROUP BY e.Department HAVING COUNT(e.ID) > 3 ORDER BY AvgSalary DESC
Test SQL: SELECT TOP 0 * FROM Employees
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.080695] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:308
Status: FAILED
Original SQL: SELECT d.Name, COUNT(e.ID) as EmployeeCount FROM Departments d LEFT JOIN Employees e ON d.ID = e.DepartmentID GROUP BY d.Name
Test SQL: SELECT TOP 0 * FROM Departments
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Departments'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.091122] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:318
Status: SUCCESS
Original SQL: SELECT f.ID, f.Name, f.ValidityRating, COUNT(e.ID) as EvidenceCount, AVG(CAST(f.ValidityRating as FLOAT)) OVER() as OverallAverage FROM REF_Fact f LEFT JOIN REF_Evidence e ON f.ID = e.FactID WHERE f.DataSource LIKE '%Self-Healer%' GROUP BY f.ID, f.Name, f.ValidityRating
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, REF_Evidence, Columns: ID, FactID, ValidityRating, Name, DataSource
----------------------------------------

[2025-08-23T11:06:14.097692] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:328
Status: FAILED
Original SQL: SELECT Name, Salary FROM Employees ORDER BY Salary DESC LIMIT 10
Test SQL: SELECT TOP 0 * FROM Employees
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.103635] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:342
Status: FAILED
Original SQL: INSERT INTO Users (Name, Email) VALUES ('John Doe', '<EMAIL>')
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.109224] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:351
Status: FAILED
Original SQL: INSERT INTO Users VALUES ('John Doe', '<EMAIL>', 1)
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.115364] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:361
Status: FAILED
Original SQL: INSERT INTO ActiveUsers (Name, Email) SELECT Name, Email FROM Users WHERE Active = 1
Test SQL: SELECT TOP 0 * FROM ActiveUsers
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'ActiveUsers'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.121066] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:371
Status: FAILED
Original SQL: INSERT INTO dbo.Users (Name, Email, Status) VALUES ('Jane Doe', '<EMAIL>', 'Active')
Test SQL: SELECT TOP 0 * FROM dbo
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.127767] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:385
Status: FAILED
Original SQL: UPDATE Users SET Name = 'Jane Doe' WHERE ID = 1
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.133648] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:394
Status: FAILED
Original SQL: UPDATE Users SET Name = 'Jane Doe', Email = '<EMAIL>', Status = 'Active' WHERE ID = 1
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.136830] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:404
Status: FAILED
Original SQL: UPDATE Users u SET u.Name = 'Jane Doe', u.Status = 'Active' WHERE u.ID = 1
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.145993] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:414
Status: FAILED
Original SQL: UPDATE Users u SET u.Status = 'Inactive' FROM Users u JOIN Departments d ON u.DepartmentID = d.ID WHERE d.Name = 'Closed'
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.151559] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:424
Status: FAILED
Original SQL: UPDATE dbo.Users SET Status = 'Inactive' WHERE LastLogin < '2023-01-01'
Test SQL: SELECT TOP 0 * FROM dbo
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.158358] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:438
Status: FAILED
Original SQL: DELETE FROM Users WHERE ID = 1
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.164387] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:447
Status: FAILED
Original SQL: DELETE FROM Users WHERE Status = 'Inactive' AND LastLogin < '2023-01-01'
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.170300] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:457
Status: FAILED
Original SQL: DELETE FROM Users u WHERE u.Status = 'Inactive'
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.175620] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:467
Status: FAILED
Original SQL: DELETE FROM dbo.Users WHERE Status = 'Deleted'
Test SQL: SELECT TOP 0 * FROM dbo
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'dbo'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.182150] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:481
Status: FAILED
Original SQL: SELECT    u.Name   ,   u.Email   FROM    Users   u   WHERE   u.Active   =   1
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.190069] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:491
Status: FAILED
Original SQL: select u.Name, u.Email from Users u where u.Active = 1
Test SQL: SELECT TOP 0 * FROM Users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.195389] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:501
Status: SUCCESS
Original SQL: SELECT rf.ID, rf.Name FROM REF_Fact rf JOIN XRF_Fact_Evidence xfe ON rf.ID = xfe.FactID
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, XRF_Fact_Evidence, Columns: ID, FactID, Name
----------------------------------------

[2025-08-23T11:06:14.205349] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:511
Status: FAILED
Original SQL: SELECT t1.Field1, t2.Field2 FROM Table1 t1 JOIN Table2 t2 ON t1.ID = t2.Table1ID
Test SQL: SELECT TOP 0 * FROM Table1
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Table1'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.213683] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\sql_parser_test_suite.py:521
Status: FAILED
Original SQL: SELECT employee.Name, department.Title FROM Employees employee JOIN Departments department ON employee.DeptID = department.ID
Test SQL: SELECT TOP 0 * FROM Employees
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'Employees'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.220068] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:27
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *
----------------------------------------

[2025-08-23T11:06:14.225084] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:28
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-23T11:06:14.229221] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:29
Status: SUCCESS
Original SQL: SELECT COUNT(*) FROM REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-23T11:06:14.237031] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:30
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Value) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Value, Name
----------------------------------------

[2025-08-23T11:06:14.242064] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:31
Status: SUCCESS
Original SQL: INSERT INTO REF_Fact (EntityID, Name, Value) VALUES (1, 'test', 'value')
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: EntityID, Name, Value
----------------------------------------

[2025-08-23T11:06:14.248232] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:32
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Name = ? WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-23T11:06:14.255106] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:33
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET Status = 'active' WHERE Name = 'test'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Name, Status
----------------------------------------

[2025-08-23T11:06:14.256244] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:34
Status: SUCCESS
Original SQL: DELETE FROM REF_Entity WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID
----------------------------------------

[2025-08-23T11:06:14.267338] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:35
Status: SUCCESS
Original SQL: DELETE FROM REF_Entity WHERE CreatedDate < '2023-01-01'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreatedDate
----------------------------------------

[2025-08-23T11:06:14.272892] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:40
Status: SUCCESS
Original SQL: SELECT 'This contains SELECT keyword' FROM REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: This, keyword, contains
----------------------------------------

[2025-08-23T11:06:14.279528] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:41
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name) VALUES ('UPDATE this later')
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: Name
----------------------------------------

[2025-08-23T11:06:14.284507] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:42
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = \"test\"
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-23T11:06:14.284507] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:43
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'test'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-23T11:06:14.297142] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:44
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name = 'It\\'s a test'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-23T11:06:14.297142] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:47
Status: FAILED
Original SQL: SELECT * FROM users WHERE id = ?
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.313441] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:48
Status: FAILED
Original SQL: INSERT INTO users (name, email) VALUES (?, ?)
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.321548] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:49
Status: FAILED
Original SQL: UPDATE users SET email = ? WHERE id = ?
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.326946] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:50
Status: FAILED
Original SQL: DELETE FROM users WHERE id = ?
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.326946] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:51
Status: FAILED
Original SQL: SELECT COUNT(*) FROM transactions WHERE DATE(created_at) = CURDATE()
Test SQL: SELECT TOP 0 * FROM transactions
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'transactions'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.338842] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:52
Status: FAILED
Original SQL: SELECT SUM(amount) FROM transactions WHERE MONTH(created_at) = MONTH(CURDATE())
Test SQL: SELECT TOP 0 * FROM transactions
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'transactions'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.346035] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:67
Status: FAILED
Original SQL: SELECT your option from the menu
Test SQL: SELECT TOP 0 * FROM the
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.351953] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:72
Status: FAILED
Original SQL: SELECT * FROM
Test SQL: SELECT TOP 1 * FROM (SELECT * FROM) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near ')'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.357678] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:73
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-23T11:06:14.365024] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:74
Status: SUCCESS
Original SQL: UPDATE REF_Entity SET
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: 
----------------------------------------

[2025-08-23T11:06:14.370677] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:194
Status: SUCCESS
Original SQL: SELECT 
    e.ID,
    e.Name

FROM REF_Entity e

WHERE e.Status = 'active'

ORDER BY e.Name
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name, Status
----------------------------------------

[2025-08-23T11:06:14.376064] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:194
Status: SUCCESS
Original SQL: SELECT 
    e.ID,
    e.Name

FROM REF_Entity e

WHERE e.Status = 'active'

ORDER BY e.Name
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name, Status
----------------------------------------

[2025-08-23T11:06:14.376064] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:210
Status: FAILED
Original SQL: SELECT * FROM users WHERE name = 'John'
Test SQL: SELECT TOP 0 * FROM users
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'users'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.389381] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:218
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *
----------------------------------------

[2025-08-23T11:06:14.392553] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\SQL_Scripts\test_sql_validator.py:219
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, *
----------------------------------------

[2025-08-23T11:06:14.403525] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_dashboard_db.py:11
Status: SUCCESS
Original SQL: SELECT * FROM REF_Entity WHERE Name LIKE 'Session_%'
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: *, Name
----------------------------------------

[2025-08-23T11:06:14.412925] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_healing_pipeline.py:257
Status: SUCCESS
Original SQL: SELECT COUNT(*) as count FROM REF_Fact
Test SQL: SELECT TOP 0 * FROM REF_Fact
Result: SUCCESS - Tables: REF_Fact, Columns: 
----------------------------------------

[2025-08-23T11:06:14.426446] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:79
Status: FAILED
Original SQL: SELECT 
                name,
                create_date,
                modify_date,
                type_desc
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.437542] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:79
Status: FAILED
Original SQL: SELECT 
                name,
                create_date,
                modify_date,
                type_desc
            FROM sys.objects 
            WHERE type = 'P' 
            AND name IN ({})
            ORDER BY name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.443886] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:129
Status: SUCCESS
Original SQL: SELECT TOP 5 ID, Name FROM REF_Entity ORDER BY ID
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: ID, Name
----------------------------------------

[2025-08-23T11:06:14.448548] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:135
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)
                OUTPUT INSERTED.ID, INSERTED.Name
                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreateDate, Name, ModifyDate, Description
----------------------------------------

[2025-08-23T11:06:14.457086] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:135
Status: SUCCESS
Original SQL: INSERT INTO REF_Entity (Name, Description, CreateDate, ModifyDate)
                OUTPUT INSERTED.ID, INSERTED.Name
                VALUES ('Test Entity for Procedure Validation', 'Test entity created for stored procedure testing', GETDATE(), GETDATE())
Test SQL: SELECT TOP 0 * FROM REF_Entity
Result: SUCCESS - Tables: REF_Entity, Columns: CreateDate, Name, ModifyDate, Description
----------------------------------------

[2025-08-23T11:06:14.460432] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:360
Status: FAILED
Original SQL: SELECT name FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.472491] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:360
Status: FAILED
Original SQL: SELECT name FROM sys.objects
            WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema'
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.479090] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:393
Status: FAILED
Original SQL: SELECT TABLE_NAME, TABLE_TYPE
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.485343] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_knowledgebase_procedures.py:393
Status: FAILED
Original SQL: SELECT TABLE_NAME, TABLE_TYPE
                FROM INFORMATION_SCHEMA.TABLES
                WHERE TABLE_TYPE = 'BASE TABLE'
                AND TABLE_NAME LIKE 'REF_%'
                ORDER BY TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.492758] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_mcp_database.py:180
Status: FAILED
Original SQL: SELECT COUNT(*) as total_rows FROM [{schema_name}].[{table_name}]
Test SQL: SELECT TOP 1 * FROM (SELECT COUNT(*) as total_rows FROM [{schema_name}].[{table_name}]) AS test_query
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.505320] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:29
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.505320] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:29
Status: FAILED
Original SQL: SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'REF_EntityValue'
        AND COLUMN_NAME IN ('NumericValue', 'ValueUnits')
        ORDER BY COLUMN_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.519906] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:48
Status: SUCCESS
Original SQL: SELECT 
            COUNT(*) as TotalRows,
            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,
            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows
        FROM REF_EntityValue
Test SQL: SELECT TOP 0 * FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: NumericValue, ValueUnits
----------------------------------------

[2025-08-23T11:06:14.524927] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:48
Status: SUCCESS
Original SQL: SELECT 
            COUNT(*) as TotalRows,
            COUNT(CASE WHEN NumericValue IS NOT NULL THEN 1 END) as NumericRows,
            COUNT(CASE WHEN ValueUnits IS NOT NULL THEN 1 END) as UnitsRows
        FROM REF_EntityValue
Test SQL: SELECT TOP 0 * FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: NumericValue, ValueUnits
----------------------------------------

[2025-08-23T11:06:14.533785] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:66
Status: SUCCESS
Original SQL: SELECT TOP 5
            Name,
            EntityValue,
            NumericValue,
            ValueUnits
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Test SQL: SELECT TOP 0 * FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, NumericValue, Name, ValueUnits
----------------------------------------

[2025-08-23T11:06:14.537473] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:66
Status: SUCCESS
Original SQL: SELECT TOP 5
            Name,
            EntityValue,
            NumericValue,
            ValueUnits
        FROM REF_EntityValue 
        WHERE NumericValue IS NOT NULL
        ORDER BY CreateDate DESC
Test SQL: SELECT TOP 0 * FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, NumericValue, Name, ValueUnits
----------------------------------------

[2025-08-23T11:06:14.537473] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:149
Status: SUCCESS
Original SQL: SELECT EntityValue, NumericValue, ValueUnits
            FROM REF_EntityValue 
            WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, NumericValue, ValueUnits, ID
----------------------------------------

[2025-08-23T11:06:14.537473] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py:149
Status: SUCCESS
Original SQL: SELECT EntityValue, NumericValue, ValueUnits
            FROM REF_EntityValue 
            WHERE ID = ?
Test SQL: SELECT TOP 0 * FROM REF_EntityValue
Result: SUCCESS - Tables: REF_EntityValue, Columns: EntityValue, NumericValue, ValueUnits, ID
----------------------------------------

[2025-08-23T11:06:14.563622] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py:56
Status: FAILED
Original SQL: SELECT name 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.568935] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_stored_procedures.py:56
Status: FAILED
Original SQL: SELECT name 
        FROM sys.objects 
        WHERE type = 'P' 
        AND name IN ({})
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.583892] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_server.py:276
Status: SUCCESS
Original SQL: SELECT TOP 20 
                FactID, FactType, FactText, ValidityRating, 
                CreatedDate, LastUpdated, Source
            FROM REF_FACT 
            WHERE FactText LIKE ? 
            AND ValidityRating >= ?
Test SQL: SELECT TOP 0 * FROM REF_FACT
Result: SUCCESS - Tables: REF_FACT, Columns: FactID, CreatedDate, Source, ValidityRating, FactText, LastUpdated, FactType
----------------------------------------

[2025-08-23T11:06:14.589252] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_server.py:276
Status: SUCCESS
Original SQL: SELECT TOP 20 
                FactID, FactType, FactText, ValidityRating, 
                CreatedDate, LastUpdated, Source
            FROM REF_FACT 
            WHERE FactText LIKE ? 
            AND ValidityRating >= ?
Test SQL: SELECT TOP 0 * FROM REF_FACT
Result: SUCCESS - Tables: REF_FACT, Columns: FactID, CreatedDate, Source, ValidityRating, FactText, LastUpdated, FactType
----------------------------------------

[2025-08-23T11:06:14.596393] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:139
Status: FAILED
Original SQL: SELECT 
                TABLE_SCHEMA,
                TABLE_NAME,
                TABLE_TYPE
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.596393] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:139
Status: FAILED
Original SQL: SELECT 
                TABLE_SCHEMA,
                TABLE_NAME,
                TABLE_TYPE
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.611275] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:161
Status: FAILED
Original SQL: SELECT 
                TABLE_SCHEMA,
                TABLE_NAME
            FROM INFORMATION_SCHEMA.VIEWS
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.618592] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:161
Status: FAILED
Original SQL: SELECT 
                TABLE_SCHEMA,
                TABLE_NAME
            FROM INFORMATION_SCHEMA.VIEWS
            ORDER BY TABLE_SCHEMA, TABLE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.628366] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:181
Status: FAILED
Original SQL: SELECT 
                ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'PROCEDURE'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.635042] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:181
Status: FAILED
Original SQL: SELECT 
                ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'PROCEDURE'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.643294] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:203
Status: FAILED
Original SQL: SELECT 
                ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'FUNCTION'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.650278] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:203
Status: FAILED
Original SQL: SELECT 
                ROUTINE_SCHEMA,
                ROUTINE_NAME,
                ROUTINE_TYPE
            FROM INFORMATION_SCHEMA.ROUTINES
            WHERE ROUTINE_TYPE = 'FUNCTION'
            ORDER BY ROUTINE_SCHEMA, ROUTINE_NAME
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.656412] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:255
Status: FAILED
Original SQL: SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                CHARACTER_MAXIMUM_LENGTH,
                NUMERIC_PRECISION,
                NUMERIC_SCALE,
                ORDINAL_POSITION
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?
            ORDER BY ORDINAL_POSITION
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.664393] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:255
Status: FAILED
Original SQL: SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                CHARACTER_MAXIMUM_LENGTH,
                NUMERIC_PRECISION,
                NUMERIC_SCALE,
                ORDINAL_POSITION
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?
            ORDER BY ORDINAL_POSITION
Test SQL: SELECT TOP 0 * FROM INFORMATION_SCHEMA
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'INFORMATION_SCHEMA'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.670105] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:287
Status: FAILED
Original SQL: SELECT
                i.name as index_name,
                i.type_desc as index_type,
                i.is_unique,
                i.is_primary_key,
                STRING_AGG(c.name, ', ') WITHIN GROUP (ORDER BY ic.key_ordinal) as columns
            FROM sys.indexes i
            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
            INNER JOIN sys.tables t ON i.object_id = t.object_id
            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
            WHERE t.name = ? AND s.name = ?
            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key
            ORDER BY i.name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.676770] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:287
Status: FAILED
Original SQL: SELECT
                i.name as index_name,
                i.type_desc as index_type,
                i.is_unique,
                i.is_primary_key,
                STRING_AGG(c.name, ', ') WITHIN GROUP (ORDER BY ic.key_ordinal) as columns
            FROM sys.indexes i
            INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
            INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
            INNER JOIN sys.tables t ON i.object_id = t.object_id
            INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
            WHERE t.name = ? AND s.name = ?
            GROUP BY i.name, i.type_desc, i.is_unique, i.is_primary_key
            ORDER BY i.name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.684908] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:318
Status: FAILED
Original SQL: SELECT
                fk.name as foreign_key_name,
                OBJECT_SCHEMA_NAME(fk.parent_object_id) as schema_name,
                OBJECT_NAME(fk.parent_object_id) as table_name,
                COL_NAME(fkc.parent_object_id, fkc.parent_column_id) as column_name,
                OBJECT_SCHEMA_NAME(fk.referenced_object_id) as referenced_schema,
                OBJECT_NAME(fk.referenced_object_id) as referenced_table,
                COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) as referenced_column
            FROM sys.foreign_keys fk
            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?
            ORDER BY fk.name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.692815] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:318
Status: FAILED
Original SQL: SELECT
                fk.name as foreign_key_name,
                OBJECT_SCHEMA_NAME(fk.parent_object_id) as schema_name,
                OBJECT_NAME(fk.parent_object_id) as table_name,
                COL_NAME(fkc.parent_object_id, fkc.parent_column_id) as column_name,
                OBJECT_SCHEMA_NAME(fk.referenced_object_id) as referenced_schema,
                OBJECT_NAME(fk.referenced_object_id) as referenced_table,
                COL_NAME(fkc.referenced_object_id, fkc.referenced_column_id) as referenced_column
            FROM sys.foreign_keys fk
            INNER JOIN sys.foreign_key_columns fkc ON fk.object_id = fkc.constraint_object_id
            WHERE OBJECT_NAME(fk.parent_object_id) = ? AND OBJECT_SCHEMA_NAME(fk.parent_object_id) = ?
            ORDER BY fk.name
Test SQL: SELECT TOP 0 * FROM sys
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'sys'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.698977] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:348
Status: FAILED
Original SQL: SELECT COUNT(*) as row_count FROM [{schema_name}].[{table_name}]
Test SQL: SELECT TOP 1 * FROM (SELECT COUNT(*) as row_count FROM [{schema_name}].[{table_name}]) AS test_query
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name '{schema_name}.{table_name}'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.707559] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\mcp_database_tool.py:439
Status: FAILED
Original SQL: SELECT TOP {limit} * FROM [{schema_name}].[{table_name}]
Test SQL: SELECT TOP 1 * FROM (SELECT TOP {limit} * FROM [{schema_name}].[{table_name}]) AS test_query
Result: FAILED - ('42000', '[42000] [Microsoft][ODBC Driver 18 for SQL Server]Syntax error, permission violation, or other nonspecific error (0) (SQLExecDirectW)')
----------------------------------------

[2025-08-23T11:06:14.739190] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\distlib\manifest.py:258
Status: FAILED
Original SQL: Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.

        Patterns are not quite the same as implemented by the 'fnmatch'
        module: '*' and '?'  match non-special characters, where "special"
        is platform-dependent: slash on Unix; colon, slash, and backslash on
        DOS/Windows; and colon on Mac OS.

        If 'anchor' is true (the default), then the pattern match is more
        stringent: "*.py" will match "foo.py" but not "foo/bar.py".  If
        'anchor' is false, both of these will match.

        If 'prefix' is supplied, then only filenames starting with 'prefix'
        (itself a pattern) and ending with 'pattern', with anything in between
        them, will match.  'anchor' is ignored in this case.

        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and
        'pattern' is assumed to be either a string containing a regex or a
        regex object -- no translation is done, the regex is just compiled
        and used as-is.

        Selected strings will be added to self.files.

        Return True if files are found.
Test SQL: SELECT TOP 1 * FROM (Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.

        Patterns are not quite the same as implemented by the 'fnmatch'
        module: '*' and '?'  match non-special characters, where "special"
        is platform-dependent: slash on Unix; colon, slash, and backslash on
        DOS/Windows; and colon on Mac OS.

        If 'anchor' is true (the default), then the pattern match is more
        stringent: "*.py" will match "foo.py" but not "foo/bar.py".  If
        'anchor' is false, both of these will match.

        If 'prefix' is supplied, then only filenames starting with 'prefix'
        (itself a pattern) and ending with 'pattern', with anything in between
        them, will match.  'anchor' is ignored in this case.

        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and
        'pattern' is assumed to be either a string containing a regex or a
        regex object -- no translation is done, the regex is just compiled
        and used as-is.

        Selected strings will be added to self.files.

        Return True if files are found.) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)")
----------------------------------------

[2025-08-23T11:06:14.747347] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\distlib\manifest.py:258
Status: FAILED
Original SQL: Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.

        Patterns are not quite the same as implemented by the 'fnmatch'
        module: '*' and '?'  match non-special characters, where
Test SQL: SELECT TOP 1 * FROM (Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.

        Patterns are not quite the same as implemented by the 'fnmatch'
        module: '*' and '?'  match non-special characters, where) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.771789] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:208
Status: FAILED
Original SQL: Update just the width, return a copy.

        Args:
            width (int): New width (sets both min_width and max_width)

        Returns:
            ~ConsoleOptions: New console options instance.
Test SQL: SELECT TOP 0 * FROM just
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.782390] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:208
Status: FAILED
Original SQL: Update just the width, return a copy.

        Args:
            width (int): New width (sets both min_width and max_width)

        Returns:
            ~ConsoleOptions: New console options instance.
Test SQL: SELECT TOP 0 * FROM just
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'just'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.787670] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:244
Status: FAILED
Original SQL: Update the width and height, and return a copy.

        Args:
            width (int): New width (sets both min_width and max_width).
            height (int): New height.

        Returns:
            ~ConsoleOptions: New console options instance.
Test SQL: SELECT TOP 0 * FROM the
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.796900] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:244
Status: FAILED
Original SQL: Update the width and height, and return a copy.

        Args:
            width (int): New width (sets both min_width and max_width).
            height (int): New height.

        Returns:
            ~ConsoleOptions: New console options instance.
Test SQL: SELECT TOP 0 * FROM the
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.812637] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:1789
Status: FAILED
Original SQL: Update the screen at a given offset.

        Args:
            renderable (RenderableType): A Rich renderable.
            region (Region, optional): Region of screen to update, or None for entire screen. Defaults to None.
            x (int, optional): x offset. Defaults to 0.
            y (int, optional): y offset. Defaults to 0.

        Raises:
            errors.NoAltScreen: If the Console isn't in alt screen mode.
Test SQL: SELECT TOP 0 * FROM the
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.818473] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:1789
Status: FAILED
Original SQL: Update the screen at a given offset.

        Args:
            renderable (RenderableType): A Rich renderable.
            region (Region, optional): Region of screen to update, or None for entire screen. Defaults to None.
            x (int, optional): x offset. Defaults to 0.
            y (int, optional): y offset. Defaults to 0.

        Raises:
            errors.NoAltScreen: If the Console isn't in alt screen mode.
Test SQL: SELECT TOP 0 * FROM the
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'the'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.825366] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:1819
Status: FAILED
Original SQL: Update lines of the screen at a given offset.

        Args:
            lines (List[List[Segment]]): Rendered lines (as produced by :meth:`~rich.Console.render_lines`).
            x (int, optional): x offset (column no). Defaults to 0.
            y (int, optional): y offset (column no). Defaults to 0.

        Raises:
            errors.NoAltScreen: If the Console isn't in alt screen mode.
Test SQL: SELECT TOP 0 * FROM lines
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.825366] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\console.py:1819
Status: FAILED
Original SQL: Update lines of the screen at a given offset.

        Args:
            lines (List[List[Segment]]): Rendered lines (as produced by :meth:`~rich.Console.render_lines`).
            x (int, optional): x offset (column no). Defaults to 0.
            y (int, optional): y offset (column no). Defaults to 0.

        Raises:
            errors.NoAltScreen: If the Console isn't in alt screen mode.
Test SQL: SELECT TOP 0 * FROM lines
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'lines'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.849947] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\progress.py:1413
Status: FAILED
Original SQL: Update information associated with a task.

        Args:
            task_id (TaskID): Task id (returned by add_task).
            total (float, optional): Updates task.total if not None.
            completed (float, optional): Updates task.completed if not None.
            advance (float, optional): Add a value to task.completed if not None.
            description (str, optional): Change task description if not None.
            visible (bool, optional): Set visible flag if not None.
            refresh (bool): Force a refresh of progress information. Default is False.
            **fields (Any): Additional data fields required for rendering.
Test SQL: SELECT TOP 0 * FROM information
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.857412] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_vendor\rich\progress.py:1413
Status: FAILED
Original SQL: Update information associated with a task.

        Args:
            task_id (TaskID): Task id (returned by add_task).
            total (float, optional): Updates task.total if not None.
            completed (float, optional): Updates task.completed if not None.
            advance (float, optional): Add a value to task.completed if not None.
            description (str, optional): Change task description if not None.
            visible (bool, optional): Set visible flag if not None.
            refresh (bool): Force a refresh of progress information. Default is False.
            **fields (Any): Additional data fields required for rendering.
Test SQL: SELECT TOP 0 * FROM information
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'information'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.938884] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_distutils\filelist.py:203
Status: FAILED
Original SQL: Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns
        are not quite the same as implemented by the 'fnmatch' module: '*'
        and '?'  match non-special characters, where "special" is platform-
        dependent: slash on Unix; colon, slash, and backslash on
        DOS/Windows; and colon on Mac OS.

        If 'anchor' is true (the default), then the pattern match is more
        stringent: "*.py" will match "foo.py" but not "foo/bar.py".  If
        'anchor' is false, both of these will match.

        If 'prefix' is supplied, then only filenames starting with 'prefix'
        (itself a pattern) and ending with 'pattern', with anything in between
        them, will match.  'anchor' is ignored in this case.

        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and
        'pattern' is assumed to be either a string containing a regex or a
        regex object -- no translation is done, the regex is just compiled
        and used as-is.

        Selected strings will be added to self.files.

        Return True if files are found, False otherwise.
Test SQL: SELECT TOP 1 * FROM (Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns
        are not quite the same as implemented by the 'fnmatch' module: '*'
        and '?'  match non-special characters, where "special" is platform-
        dependent: slash on Unix; colon, slash, and backslash on
        DOS/Windows; and colon on Mac OS.

        If 'anchor' is true (the default), then the pattern match is more
        stringent: "*.py" will match "foo.py" but not "foo/bar.py".  If
        'anchor' is false, both of these will match.

        If 'prefix' is supplied, then only filenames starting with 'prefix'
        (itself a pattern) and ending with 'pattern', with anything in between
        them, will match.  'anchor' is ignored in this case.

        If 'is_regex' is true, 'anchor' and 'prefix' are ignored, and
        'pattern' is assumed to be either a string containing a regex or a
        regex object -- no translation is done, the regex is just compiled
        and used as-is.

        Selected strings will be added to self.files.

        Return True if files are found, False otherwise.) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'false'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'supplied'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near the keyword 'with'. If this statement is a common table expression, an xmlnamespaces clause or a change tracking context clause, the previous statement must be terminated with a semicolon. (319); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'true'. (102); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]A RETURN statement with a return value cannot be used in this context. (178); [42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]An expression of non-boolean type specified in a context where a condition is expected, near 'are'. (4145)")
----------------------------------------

[2025-08-23T11:06:14.943124] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_distutils\filelist.py:203
Status: FAILED
Original SQL: Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns
        are not quite the same as implemented by the 'fnmatch' module: '*'
        and '?'  match non-special characters, where
Test SQL: SELECT TOP 1 * FROM (Select strings (presumably filenames) from 'self.files' that
        match 'pattern', a Unix-style wildcard (glob) pattern.  Patterns
        are not quite the same as implemented by the 'fnmatch' module: '*'
        and '?'  match non-special characters, where) AS test_query
Result: FAILED - ('42000', "[42000] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Incorrect syntax near 'filenames'. (102) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.954919] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_vendor\importlib_metadata\__init__.py:349
Status: FAILED
Original SQL: Select entry points from self that match the
        given parameters (typically group and/or name).
Test SQL: SELECT TOP 0 * FROM self
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'self'. (208) (SQLExecDirectW)")
----------------------------------------

[2025-08-23T11:06:14.963857] C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\setuptools\_vendor\importlib_metadata\__init__.py:349
Status: FAILED
Original SQL: Select entry points from self that match the
        given parameters (typically group and/or name).
Test SQL: SELECT TOP 0 * FROM self
Result: FAILED - ('42S02', "[42S02] [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]Invalid object name 'self'. (208) (SQLExecDirectW)")
----------------------------------------


Session ended: 2025-08-23T11:06:14.987703
================================================================================
