Table Name Fix Report
Generated: 2025-08-23 11:50:36
Source CSV: data/database_repair_logs/repair_summary_20250823_110612.csv
Table Name Change: XRF_EntityAttributeValue → XRF_Entity_AttributeValue
================================================================================

SUMMARY:
  Fixes Applied: 4
  Errors: 0

FIXES APPLIED:
----------------------------------------
File: C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py
Line: 621
Old:  SELECT ID FROM XRF_EntityAttributeValue
New:  SELECT ID FROM XRF_Entity_AttributeValue

File: C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py
Line: 631
Old:  INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)
New:  INSERT INTO XRF_Entity_AttributeValue (Name, EntityID, AttributeID, EntityValueID)

File: C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py
Line: 871
Old:  # Get session attributes using XRF_EntityAttributeValue with numeric support
New:  # Get session attributes using XRF_Entity_AttributeValue with numeric support

File: C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py
Line: 32
Old:  print("\n=== Check XRF_EntityAttributeValue ===")
New:  print("\n=== Check XRF_Entity_AttributeValue ===")

