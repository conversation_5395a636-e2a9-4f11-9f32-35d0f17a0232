
<!DOCTYPE html>
<html>
<head>
    <title>Database Reference Repair Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f8ff; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background-color: #e8f4f8; padding: 15px; border-radius: 5px; text-align: center; }
        .metric.success { background-color: #e8f8e8; }
        .metric.warning { background-color: #fff8e8; }
        .metric.error { background-color: #ffe8e8; }
        .repair { background-color: #f9f9f9; padding: 10px; margin: 5px 0; border-left: 4px solid #4CAF50; }
        .repair.failed { border-left-color: #f44336; }
        .file-path { font-weight: bold; color: #0066cc; }
        .line-number { color: #666; font-size: 0.9em; }
        .code { font-family: monospace; background-color: #f0f0f0; padding: 5px; margin: 5px 0; }
        .before { color: #d32f2f; }
        .after { color: #388e3c; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Database Reference Repair Report</h1>
        <p>Generated: 2025-08-17 23:48:43</p>
        <p>Mode: DRY RUN</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>454</h3>
            <p>Files Processed</p>
        </div>
        <div class="metric success">
            <h3>19</h3>
            <p>Files Modified</p>
        </div>
        <div class="metric success">
            <h3>97</h3>
            <p>Successful Repairs</p>
        </div>
        <div class="metric success">
            <h3>0</h3>
            <p>Failed Repairs</p>
        </div>
    </div>
    
    <h2>Repair Categories</h2>
    <ul>
<li><strong>syntax</strong>: 2 repairs</li><li><strong>table_name</strong>: 95 repairs</li>
    </ul>
    
    <h2>✅ Successful Repairs</h2>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\pip\_internal\build_env.py</div>
        <div class="line-number">Line 53</div>
        <div><strong>Rule:</strong> Fix table alias spacing</div>
        <div class="code before">Before: # This would happen if someone is using pip from inside a zip file. In that</div>
        <div class="code after">After:  # This would happen if someone is using pip FROM inside a zip file. In that</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\n8n_builder\venv\Lib\site-packages\_pytest\_py\path.py</div>
        <div class="line-number">Line 1160</div>
        <div><strong>Rule:</strong> Fix table alias spacing</div>
        <div class="code before">Before: """Return stdout text from executing a system child process,</div>
        <div class="code after">After:  """Return stdout text FROM executing a system child process,</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py</div>
        <div class="line-number">Line 410</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: entity_query = "SELECT ID FROM REF_Entities WHERE Name = ?"</div>
        <div class="code after">After:  entity_query = "SELECT ID FROM REF_Entity WHERE Name = ?"</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py</div>
        <div class="line-number">Line 442</div>
        <div><strong>Rule:</strong> Fix table name: REF_Attributes → REF_Attribute</div>
        <div class="code before">Before: JOIN REF_Attributes a ON eav.AttributeID = a.ID</div>
        <div class="code after">After:  JOIN REF_Attribute a ON eav.AttributeID = a.ID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py</div>
        <div class="line-number">Line 443</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: JOIN REF_EntityValues ev ON eav.EntityValueID = ev.ID</div>
        <div class="code after">After:  JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py</div>
        <div class="line-number">Line 566</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: query = "SELECT ID FROM REF_Entities WHERE Name = ?"</div>
        <div class="code after">After:  query = "SELECT ID FROM REF_Entity WHERE Name = ?"</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py</div>
        <div class="line-number">Line 573</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: insert_query = "INSERT INTO REF_Entities (Name) OUTPUT INSERTED.ID VALUES (?)"</div>
        <div class="code after">After:  insert_query = "INSERT INTO REF_Entity (Name) OUTPUT INSERTED.ID VALUES (?)"</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py</div>
        <div class="line-number">Line 580</div>
        <div><strong>Rule:</strong> Fix table name: REF_Attributes → REF_Attribute</div>
        <div class="code before">Before: query = "SELECT ID FROM REF_Attributes WHERE Name = ?"</div>
        <div class="code after">After:  query = "SELECT ID FROM REF_Attribute WHERE Name = ?"</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py</div>
        <div class="line-number">Line 587</div>
        <div><strong>Rule:</strong> Fix table name: REF_Attributes → REF_Attribute</div>
        <div class="code before">Before: insert_query = "INSERT INTO REF_Attributes (Name) OUTPUT INSERTED.ID VALUES (?)"</div>
        <div class="code after">After:  insert_query = "INSERT INTO REF_Attribute (Name) OUTPUT INSERTED.ID VALUES (?)"</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py</div>
        <div class="line-number">Line 594</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: query = "SELECT ID FROM REF_EntityValues WHERE Name = ? AND EntityValue = ?"</div>
        <div class="code after">After:  query = "SELECT ID FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?"</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py</div>
        <div class="line-number">Line 603</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: INSERT INTO REF_EntityValues (Name, EntityValue, NumericValue, ValueUnits)</div>
        <div class="code after">After:  INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\core\knowledge_integration.py</div>
        <div class="line-number">Line 610</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: insert_query = "INSERT INTO REF_EntityValues (Name, EntityValue) OUTPUT INSERTED.ID VALUES (?, ?)"</div>
        <div class="code after">After:  insert_query = "INSERT INTO REF_EntityValue (Name, EntityValue) OUTPUT INSERTED.ID VALUES (?, ?)"</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py</div>
        <div class="line-number">Line 857</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: FROM REF_Entities</div>
        <div class="code after">After:  FROM REF_Entity</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py</div>
        <div class="line-number">Line 881</div>
        <div><strong>Rule:</strong> Fix table name: REF_Attributes → REF_Attribute</div>
        <div class="code before">Before: JOIN REF_Attributes a ON eav.AttributeID = a.ID</div>
        <div class="code after">After:  JOIN REF_Attribute a ON eav.AttributeID = a.ID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py</div>
        <div class="line-number">Line 882</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: JOIN REF_EntityValues ev ON eav.EntityValueID = ev.ID</div>
        <div class="code after">After:  JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py</div>
        <div class="line-number">Line 1023</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: FROM REF_Entities e</div>
        <div class="code after">After:  FROM REF_Entity e</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py</div>
        <div class="line-number">Line 1027</div>
        <div><strong>Rule:</strong> Fix table name: REF_Attributes → REF_Attribute</div>
        <div class="code before">Before: JOIN REF_Attributes a ON eav.AttributeID = a.ID</div>
        <div class="code after">After:  JOIN REF_Attribute a ON eav.AttributeID = a.ID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\dashboard\dashboard.py</div>
        <div class="line-number">Line 1028</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: JOIN REF_EntityValues ev ON eav.EntityValueID = ev.ID</div>
        <div class="code after">After:  JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py</div>
        <div class="line-number">Line 31</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: # 2. Check REF_EntityValues table structure</div>
        <div class="code after">After:  # 2. Check REF_EntityValue table structure</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py</div>
        <div class="line-number">Line 32</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: print('\n2. Checking REF_EntityValues table:')</div>
        <div class="code after">After:  print('\n2. Checking REF_EntityValue table:')</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py</div>
        <div class="line-number">Line 37</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: WHERE TABLE_NAME = 'REF_EntityValues'</div>
        <div class="code after">After:  WHERE TABLE_NAME = REF_EntityValue</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_db_state.py</div>
        <div class="line-number">Line 59</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: print('  ❌ REF_EntityValues table not found!')</div>
        <div class="code after">After:  print('  ❌ REF_EntityValue table not found!')</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py</div>
        <div class="line-number">Line 25</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: print("\n=== All REF_Entities ===")</div>
        <div class="code after">After:  print("\n=== All REF_Entity ===")</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\check_schema.py</div>
        <div class="line-number">Line 26</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: all_entities = await db_tool.execute_query("SELECT TOP 10 * FROM REF_Entities ORDER BY CreateDate DESC")</div>
        <div class="code after">After:  all_entities = await db_tool.execute_query("SELECT TOP 10 * FROM REF_Entity ORDER BY CreateDate DESC")</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\database\create_analytics_procedure.py</div>
        <div class="line-number">Line 27</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: FROM REF_Entities e</div>
        <div class="code after">After:  FROM REF_Entity e</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql</div>
        <div class="line-number">Line 29</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: EXEC z_S_SYS_Admin_KnowledgeBaseSchema @TableName = 'REF_Entities'</div>
        <div class="code after">After:  EXEC z_S_SYS_Admin_KnowledgeBaseSchema @TableName = REF_Entity</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql</div>
        <div class="line-number">Line 42</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'REF_Entities'</div>
        <div class="code after">After:  SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = REF_Entity</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_schema_procedure.sql</div>
        <div class="line-number">Line 174</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: PRINT '  EXEC z_S_SYS_Admin_KnowledgeBaseSchema @TableName = ''REF_Entities''  -- Get specific table'</div>
        <div class="code after">After:  PRINT '  EXEC z_S_SYS_Admin_KnowledgeBaseSchema @TableName = 'REF_Entity'  -- Get specific table'</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql</div>
        <div class="line-number">Line 63</div>
        <div><strong>Rule:</strong> Standardize XRF table name: XRF_Entity_AttributeValue → XRF_EntityAttributeValue</div>
        <div class="code before">Before: FROM XRF_Entity_AttributeValue eav</div>
        <div class="code after">After:  FROM XRF_EntityAttributeValue eav</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql</div>
        <div class="line-number">Line 66</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: FROM REF_Entities e</div>
        <div class="code after">After:  FROM REF_Entity e</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql</div>
        <div class="line-number">Line 68</div>
        <div><strong>Rule:</strong> Fix table name: REF_Attributes → REF_Attribute</div>
        <div class="code before">Before: LEFT JOIN REF_Attributes attr_duration ON eav_duration.AttributeID = attr_duration.ID</div>
        <div class="code after">After:  LEFT JOIN REF_Attribute attr_duration ON eav_duration.AttributeID = attr_duration.ID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql</div>
        <div class="line-number">Line 70</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: LEFT JOIN REF_EntityValues duration_val ON eav_duration.ValueID = duration_val.ID</div>
        <div class="code after">After:  LEFT JOIN REF_EntityValue duration_val ON eav_duration.ValueID = duration_val.ID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql</div>
        <div class="line-number">Line 73</div>
        <div><strong>Rule:</strong> Fix table name: REF_Attributes → REF_Attribute</div>
        <div class="code before">Before: LEFT JOIN REF_Attributes attr_success ON eav_success.AttributeID = attr_success.ID</div>
        <div class="code after">After:  LEFT JOIN REF_Attribute attr_success ON eav_success.AttributeID = attr_success.ID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql</div>
        <div class="line-number">Line 75</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: LEFT JOIN REF_EntityValues success_val ON eav_success.ValueID = success_val.ID</div>
        <div class="code after">After:  LEFT JOIN REF_EntityValue success_val ON eav_success.ValueID = success_val.ID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql</div>
        <div class="line-number">Line 136</div>
        <div><strong>Rule:</strong> Standardize XRF table name: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue</div>
        <div class="code before">Before: LEFT JOIN XRF_Entity_Attribute_Value eav ON e.ID = eav.EntityID</div>
        <div class="code after">After:  LEFT JOIN XRF_EntityAttributeValue eav ON e.ID = eav.EntityID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql</div>
        <div class="line-number">Line 205</div>
        <div><strong>Rule:</strong> Standardize XRF table name: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue</div>
        <div class="code before">Before: LEFT JOIN XRF_Entity_Attribute_Value eav_success ON e.ID = eav_success.EntityID</div>
        <div class="code after">After:  LEFT JOIN XRF_EntityAttributeValue eav_success ON e.ID = eav_success.EntityID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql</div>
        <div class="line-number">Line 210</div>
        <div><strong>Rule:</strong> Standardize XRF table name: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue</div>
        <div class="code before">Before: LEFT JOIN XRF_Entity_Attribute_Value eav_duration ON e.ID = eav_duration.EntityID</div>
        <div class="code after">After:  LEFT JOIN XRF_EntityAttributeValue eav_duration ON e.ID = eav_duration.EntityID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql</div>
        <div class="line-number">Line 277</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: FROM REF_Entities e</div>
        <div class="code after">After:  FROM REF_Entity e</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql</div>
        <div class="line-number">Line 279</div>
        <div><strong>Rule:</strong> Fix table name: REF_Attributes → REF_Attribute</div>
        <div class="code before">Before: LEFT JOIN REF_Attributes attr_duration ON eav_duration.AttributeID = attr_duration.ID</div>
        <div class="code after">After:  LEFT JOIN REF_Attribute attr_duration ON eav_duration.AttributeID = attr_duration.ID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql</div>
        <div class="line-number">Line 281</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: LEFT JOIN REF_EntityValues duration_val ON eav_duration.ValueID = duration_val.ID</div>
        <div class="code after">After:  LEFT JOIN REF_EntityValue duration_val ON eav_duration.ValueID = duration_val.ID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\create_session_procedures.sql</div>
        <div class="line-number">Line 283</div>
        <div><strong>Rule:</strong> Standardize XRF table name: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue</div>
        <div class="code before">Before: LEFT JOIN XRF_Entity_Attribute_Value eav_success ON e.ID = eav_success.EntityID</div>
        <div class="code after">After:  LEFT JOIN XRF_EntityAttributeValue eav_success ON e.ID = eav_success.EntityID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql</div>
        <div class="line-number">Line 16</div>
        <div><strong>Rule:</strong> Standardize XRF table name: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue</div>
        <div class="code before">Before: - Convention requires underscores: XRF_Entity_Attribute_Value, XRF_Category_Fact</div>
        <div class="code after">After:  - Convention requires underscores: XRF_EntityAttributeValue, XRF_Category_Fact</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql</div>
        <div class="line-number">Line 72</div>
        <div><strong>Rule:</strong> Standardize XRF table name: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue</div>
        <div class="code before">Before: -- XRF_EntityAttributeValue -> XRF_Entity_Attribute_Value</div>
        <div class="code after">After:  -- XRF_EntityAttributeValue -> XRF_EntityAttributeValue</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql</div>
        <div class="line-number">Line 75</div>
        <div><strong>Rule:</strong> Standardize XRF table name: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue</div>
        <div class="code before">Before: EXEC sp_rename 'XRF_EntityAttributeValue', 'XRF_Entity_Attribute_Value'</div>
        <div class="code after">After:  EXEC sp_rename 'XRF_EntityAttributeValue', 'XRF_EntityAttributeValue'</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\fix_table_naming_convention.sql</div>
        <div class="line-number">Line 76</div>
        <div><strong>Rule:</strong> Standardize XRF table name: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue</div>
        <div class="code before">Before: PRINT '  ✅ XRF_EntityAttributeValue -> XRF_Entity_Attribute_Value'</div>
        <div class="code after">After:  PRINT '  ✅ XRF_EntityAttributeValue -> XRF_EntityAttributeValue'</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\get_knowledgebase_schema.py</div>
        <div class="line-number">Line 14</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: python get_knowledgebase_schema.py REF_Entities       # Get specific table</div>
        <div class="code after">After:  python get_knowledgebase_schema.py REF_Entity       # Get specific table</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\install_and_test.py</div>
        <div class="line-number">Line 104</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: print("   Test 2: Getting REF_Entities table...")</div>
        <div class="code after">After:  print("   Test 2: Getting REF_Entity table...")</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\install_and_test.py</div>
        <div class="line-number">Line 107</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: ['REF_Entities']</div>
        <div class="code after">After:  [REF_Entity]</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\install_and_test.py</div>
        <div class="line-number">Line 113</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: print(f"   ✅ REF_Entities has {column_count} columns")</div>
        <div class="code after">After:  print(f"   ✅ REF_Entity has {column_count} columns")</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\install_and_test.py</div>
        <div class="line-number">Line 115</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: print(f"   ❌ Failed to get REF_Entities: {result.get('error')}")</div>
        <div class="code after">After:  print(f"   ❌ Failed to get REF_Entity: {result.get('error')}")</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\install_and_test.py</div>
        <div class="line-number">Line 212</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: print("   python get_knowledgebase_schema.py REF_Entities       # Get specific table")</div>
        <div class="code after">After:  print("   python get_knowledgebase_schema.py REF_Entity       # Get specific table")</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql</div>
        <div class="line-number">Line 5</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: PURPOSE:   Enhance REF_EntityValues table with numeric value support</div>
        <div class="code after">After:  PURPOSE:   Enhance REF_EntityValue table with numeric value support</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql</div>
        <div class="line-number">Line 13</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: - REF_EntityValues currently stores all values as text (EntityValue NVARCHAR)</div>
        <div class="code after">After:  - REF_EntityValue currently stores all values as text (EntityValue NVARCHAR)</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql</div>
        <div class="line-number">Line 31</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: PRINT 'Adding NumericValue and ValueUnits fields to REF_EntityValues...'</div>
        <div class="code after">After:  PRINT 'Adding NumericValue and ValueUnits fields to REF_EntityValue...'</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql</div>
        <div class="line-number">Line 35</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: WHERE TABLE_NAME = 'REF_EntityValues' AND COLUMN_NAME = 'NumericValue')</div>
        <div class="code after">After:  WHERE TABLE_NAME = REF_EntityValue AND COLUMN_NAME = 'NumericValue')</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql</div>
        <div class="line-number">Line 37</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: ALTER TABLE REF_EntityValues</div>
        <div class="code after">After:  ALTER TABLE REF_EntityValue</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql</div>
        <div class="line-number">Line 47</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: WHERE TABLE_NAME = 'REF_EntityValues' AND COLUMN_NAME = 'ValueUnits')</div>
        <div class="code after">After:  WHERE TABLE_NAME = REF_EntityValue AND COLUMN_NAME = 'ValueUnits')</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql</div>
        <div class="line-number">Line 49</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: ALTER TABLE REF_EntityValues</div>
        <div class="code after">After:  ALTER TABLE REF_EntityValue</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql</div>
        <div class="line-number">Line 65</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: UPDATE REF_EntityValues</div>
        <div class="code after">After:  UPDATE REF_EntityValue</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql</div>
        <div class="line-number">Line 82</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: UPDATE REF_EntityValues</div>
        <div class="code after">After:  UPDATE REF_EntityValue</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql</div>
        <div class="line-number">Line 102</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: UPDATE REF_EntityValues</div>
        <div class="code after">After:  UPDATE REF_EntityValue</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql</div>
        <div class="line-number">Line 120</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: UPDATE REF_EntityValues</div>
        <div class="code after">After:  UPDATE REF_EntityValue</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql</div>
        <div class="line-number">Line 149</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: FROM REF_EntityValues</div>
        <div class="code after">After:  FROM REF_EntityValue</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql</div>
        <div class="line-number">Line 62</div>
        <div><strong>Rule:</strong> Standardize XRF table name: XRF_Entity_AttributeValue → XRF_EntityAttributeValue</div>
        <div class="code before">Before: LEFT JOIN XRF_Entity_AttributeValue eav_success ON e.ID = eav_success.EntityID</div>
        <div class="code after">After:  LEFT JOIN XRF_EntityAttributeValue eav_success ON e.ID = eav_success.EntityID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql</div>
        <div class="line-number">Line 67</div>
        <div><strong>Rule:</strong> Standardize XRF table name: XRF_Entity_AttributeValue → XRF_EntityAttributeValue</div>
        <div class="code before">Before: LEFT JOIN XRF_Entity_AttributeValue eav_duration ON e.ID = eav_duration.EntityID</div>
        <div class="code after">After:  LEFT JOIN XRF_EntityAttributeValue eav_duration ON e.ID = eav_duration.EntityID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql</div>
        <div class="line-number">Line 126</div>
        <div><strong>Rule:</strong> Standardize XRF table name: XRF_Entity_AttributeValue → XRF_EntityAttributeValue</div>
        <div class="code before">Before: LEFT JOIN XRF_Entity_AttributeValue eav_duration ON e.ID = eav_duration.EntityID</div>
        <div class="code after">After:  LEFT JOIN XRF_EntityAttributeValue eav_duration ON e.ID = eav_duration.EntityID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql</div>
        <div class="line-number">Line 131</div>
        <div><strong>Rule:</strong> Standardize XRF table name: XRF_Entity_AttributeValue → XRF_EntityAttributeValue</div>
        <div class="code before">Before: LEFT JOIN XRF_Entity_AttributeValue eav_success ON e.ID = eav_success.EntityID</div>
        <div class="code after">After:  LEFT JOIN XRF_EntityAttributeValue eav_success ON e.ID = eav_success.EntityID</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py</div>
        <div class="line-number">Line 58</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: 'REF_Entities': 'REF_Entity',</div>
        <div class="code after">After:  REF_Entity: 'REF_Entity',</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py</div>
        <div class="line-number">Line 59</div>
        <div><strong>Rule:</strong> Fix table name: REF_Facts → REF_Fact</div>
        <div class="code before">Before: 'REF_Facts': 'REF_Fact',</div>
        <div class="code after">After:  REF_Fact: 'REF_Fact',</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py</div>
        <div class="line-number">Line 60</div>
        <div><strong>Rule:</strong> Fix table name: REF_Opinions → REF_Opinion</div>
        <div class="code before">Before: 'REF_Opinions': 'REF_Opinion',</div>
        <div class="code after">After:  REF_Opinion: 'REF_Opinion',</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py</div>
        <div class="line-number">Line 61</div>
        <div><strong>Rule:</strong> Fix table name: REF_Sources → REF_Source</div>
        <div class="code before">Before: 'REF_Sources': 'REF_Source',</div>
        <div class="code after">After:  REF_Source: 'REF_Source',</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py</div>
        <div class="line-number">Line 62</div>
        <div><strong>Rule:</strong> Fix table name: REF_Categories → REF_Category</div>
        <div class="code before">Before: 'REF_Categories': 'REF_Category',</div>
        <div class="code after">After:  REF_Category: 'REF_Category',</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py</div>
        <div class="line-number">Line 63</div>
        <div><strong>Rule:</strong> Fix table name: REF_Attributes → REF_Attribute</div>
        <div class="code before">Before: 'REF_Attributes': 'REF_Attribute',  # Assuming singular is correct</div>
        <div class="code after">After:  REF_Attribute: 'REF_Attribute',  # Assuming singular is correct</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py</div>
        <div class="line-number">Line 64</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: 'REF_EntityValues': 'REF_EntityValue',</div>
        <div class="code after">After:  REF_EntityValue: 'REF_EntityValue',</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py</div>
        <div class="line-number">Line 65</div>
        <div><strong>Rule:</strong> Fix table name: REF_Evidence → REF_Evidence</div>
        <div class="code before">Before: 'REF_Evidence': 'REF_Evidence',  # This might be correct as-is</div>
        <div class="code after">After:  REF_Evidence: REF_Evidence,  # This might be correct as-is</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py</div>
        <div class="line-number">Line 88</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: confidence = "high" if incorrect in ['REF_Entities'] else "medium"</div>
        <div class="code after">After:  confidence = "high" if incorrect in [REF_Entity] else "medium"</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py</div>
        <div class="line-number">Line 99</div>
        <div><strong>Rule:</strong> Standardize XRF table name: XRF_Entity_Attribute_Value → XRF_EntityAttributeValue</div>
        <div class="code before">Before: 'XRF_Entity_Attribute_Value': 'XRF_EntityAttributeValue',</div>
        <div class="code after">After:  'XRF_EntityAttributeValue': 'XRF_EntityAttributeValue',</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\auto_fix_database_references.py</div>
        <div class="line-number">Line 100</div>
        <div><strong>Rule:</strong> Standardize XRF table name: XRF_Entity_AttributeValue → XRF_EntityAttributeValue</div>
        <div class="code before">Before: 'XRF_Entity_AttributeValue': 'XRF_EntityAttributeValue',  # Remove underscores</div>
        <div class="code after">After:  'XRF_EntityAttributeValue': 'XRF_EntityAttributeValue',  # Remove underscores</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py</div>
        <div class="line-number">Line 3</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: Check KnowledgeBase database table names to confirm REF_Entity vs REF_Entities</div>
        <div class="code after">After:  Check KnowledgeBase database table names to confirm REF_Entity vs REF_Entity</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py</div>
        <div class="line-number">Line 69</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: elif table == 'REF_Entities':</div>
        <div class="code after">After:  elif table == REF_Entity:</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py</div>
        <div class="line-number">Line 70</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: print(f"   ⚠️  OLD TABLE NAME: REF_Entities (plural)")</div>
        <div class="code after">After:  print(f"   ⚠️  OLD TABLE NAME: REF_Entity (plural)")</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py</div>
        <div class="line-number">Line 77</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: test_query = "SELECT TOP 1 * FROM REF_Entities"</div>
        <div class="code after">After:  test_query = "SELECT TOP 1 * FROM REF_Entity"</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py</div>
        <div class="line-number">Line 79</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: print("   ❌ REF_Entities table exists (unexpected)")</div>
        <div class="code after">After:  print("   ❌ REF_Entity table exists (unexpected)")</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py</div>
        <div class="line-number">Line 81</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: if "Invalid object name 'REF_Entities'" in str(e):</div>
        <div class="code after">After:  if "Invalid object name REF_Entity" in str(e):</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py</div>
        <div class="line-number">Line 82</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: print("   ✅ REF_Entities does NOT exist (as expected)")</div>
        <div class="code after">After:  print("   ✅ REF_Entity does NOT exist (as expected)")</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\check_database_tables.py</div>
        <div class="line-number">Line 109</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: print(f"   Scripts referencing 'REF_Entities' need to be updated")</div>
        <div class="code after">After:  print(f"   Scripts referencing REF_Entity need to be updated")</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py</div>
        <div class="line-number">Line 23</div>
        <div><strong>Rule:</strong> Fix table name: REF_Entities → REF_Entity</div>
        <div class="code before">Before: 'REF_Entities': 'REF_Entity',  # The one we just fixed</div>
        <div class="code after">After:  REF_Entity: 'REF_Entity',  # The one we just fixed</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py</div>
        <div class="line-number">Line 24</div>
        <div><strong>Rule:</strong> Fix table name: REF_Facts → REF_Fact</div>
        <div class="code before">Before: 'REF_Facts': 'REF_Fact',</div>
        <div class="code after">After:  REF_Fact: 'REF_Fact',</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py</div>
        <div class="line-number">Line 25</div>
        <div><strong>Rule:</strong> Fix table name: REF_Opinions → REF_Opinion</div>
        <div class="code before">Before: 'REF_Opinions': 'REF_Opinion',</div>
        <div class="code after">After:  REF_Opinion: 'REF_Opinion',</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py</div>
        <div class="line-number">Line 26</div>
        <div><strong>Rule:</strong> Fix table name: REF_Sources → REF_Source</div>
        <div class="code before">Before: 'REF_Sources': 'REF_Source',</div>
        <div class="code after">After:  REF_Source: 'REF_Source',</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\Scripts\simple_table_validator.py</div>
        <div class="line-number">Line 27</div>
        <div><strong>Rule:</strong> Fix table name: REF_Categories → REF_Category</div>
        <div class="code before">Before: 'REF_Categories': 'REF_Category'</div>
        <div class="code after">After:  REF_Category: 'REF_Category'</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py</div>
        <div class="line-number">Line 3</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: Test script for REF_EntityValues numeric enhancement.</div>
        <div class="code after">After:  Test script for REF_EntityValue numeric enhancement.</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py</div>
        <div class="line-number">Line 20</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: print("🧪 Testing REF_EntityValues Numeric Enhancement")</div>
        <div class="code after">After:  print("🧪 Testing REF_EntityValue Numeric Enhancement")</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py</div>
        <div class="line-number">Line 32</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: WHERE TABLE_NAME = 'REF_EntityValues'</div>
        <div class="code after">After:  WHERE TABLE_NAME = REF_EntityValue</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py</div>
        <div class="line-number">Line 53</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: FROM REF_EntityValues</div>
        <div class="code after">After:  FROM REF_EntityValue</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py</div>
        <div class="line-number">Line 72</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: FROM REF_EntityValues</div>
        <div class="code after">After:  FROM REF_EntityValue</div>
    </div>

    <div class="repair">
        <div class="file-path">C:\Users\<USER>\source\Cursor_Workspaces\N8N_Builder\tests\test_numeric_values_enhancement.py</div>
        <div class="line-number">Line 151</div>
        <div><strong>Rule:</strong> Fix table name: REF_EntityValues → REF_EntityValue</div>
        <div class="code before">Before: FROM REF_EntityValues</div>
        <div class="code after">After:  FROM REF_EntityValue</div>
    </div>

</body>
</html>
