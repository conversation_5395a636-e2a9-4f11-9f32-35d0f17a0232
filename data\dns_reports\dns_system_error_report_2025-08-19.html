<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DNS Security Monitor - System Error</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .error-header { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 30px; }
        .error-header h1 { margin: 0; font-size: 2.5em; }
        .error-header p { margin: 10px 0 0 0; opacity: 0.9; }
        .error-details { background: #fff5f5; border: 2px solid #dc3545; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .error-details h3 { color: #dc3545; margin-top: 0; }
        .troubleshooting { background: #f8f9fa; border-left: 5px solid #007bff; padding: 20px; margin: 20px 0; }
        .troubleshooting h3 { color: #007bff; margin-top: 0; }
        .command { background: #f1f3f4; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
        .status-info { background: #e8f5e8; border: 1px solid #4caf50; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .footer { margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px; text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-header">
            <h1>🚨 DNS Security Monitor - System Error</h1>
            <p>Automation Failure Detected: 8/19/2025, 11:00:09 PM</p>
        </div>
        
        <div class="error-details">
            <h3>❌ Critical System Error</h3>
            <p><strong>Error Type:</strong> DNS_CACHE_AUTOMATION_FAILURE</p>
            <p><strong>Error Message:</strong> DNS cache file is too old (3031 minutes, max 65)</p>
            <p><strong>Impact:</strong> DNS security monitoring is not functioning. No real DNS analysis can be performed.</p>
        </div>
        
        <div class="troubleshooting">
            <h3>🔧 Required Actions</h3>
            <p>The DNS cache automation system has failed. Please follow these steps to restore functionality:</p>
            
            <h4>Step 1: Check Task Scheduler Status</h4>
            <div class="command">.\Scripts\setup_dns_automation.ps1 -Status</div>
            
            <h4>Step 2: Manual DNS Collection (Immediate Fix)</h4>
            <div class="command">.\Scripts\get_dns_cache.ps1</div>
            
            <h4>Step 3: Restart Automation (If Needed)</h4>
            <div class="command">.\Scripts\setup_dns_monitoring.ps1</div>
            
            <h4>Step 4: Verify File Creation</h4>
            <p>After running the manual collection, verify that this file exists and is recent:</p>
            <div class="command">data\dns_reports\setup\dns_cache_output.txt</div>
        </div>
        
        
        <div class="status-info">
            <h3>📊 File Status Details</h3>
            <p><strong>File Exists:</strong> Yes</p>
            <p><strong>File Age:</strong> 3031 minutes</p>
            <p><strong>Maximum Allowed Age:</strong> 65 minutes</p>
            <p><strong>Expected Update Frequency:</strong> Every 55 minutes via Windows Task Scheduler</p>
        </div>
        
        
        <div class="footer">
            <p><strong>DNS Security Monitor - System Error Report</strong></p>
            <p>Generated: 8/19/2025, 11:00:09 PM</p>
            <p>This report will be replaced with normal security analysis once the automation is restored.</p>
        </div>
    </div>
</body>
</html>