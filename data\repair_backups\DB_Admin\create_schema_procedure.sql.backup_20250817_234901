USE KnowledgeBase
GO

-- Drop procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'z_S_SYS_Admin_KnowledgeBaseSchema')
    DROP PROCEDURE z_S_SYS_Admin_KnowledgeBaseSchema
GO

/*
================================================
AUTHOR:    MARK ABRAMS & Augment Agent (AI Assistant)
DATE:      2025-06-27
PURPOSE:   Dynamic schema information retrieval for Self-Healer integration
NOTES:     Administrative procedure protected from auto-generation utility.
           Returns structured schema data for real-time documentation.
           Optimized for both overview and detailed table analysis.
================================================
PARAMETERS:
   @TableName NVARCHAR(128) - Specific table name (NULL returns all tables)

RETURNS:   Multiple result sets with table info, columns, relationships, and row counts
================================================
USAGE:

   -- Get all tables overview
   EXEC z_S_SYS_Admin_KnowledgeBaseSchema

   -- Get specific table details
   EXEC z_S_SYS_Admin_KnowledgeBaseSchema @TableName = 'REF_Entities'

================================================
SUPPORT QUERIES:

   -- List all REF tables
   SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES
   WHERE TABLE_NAME LIKE 'REF_%' ORDER BY TABLE_NAME

   -- Check procedure exists
   SELECT name FROM sys.objects WHERE type = 'P' AND name LIKE '%Schema%'

   -- Verify table exists before querying
   SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'REF_Entities'

================================================
*/

CREATE PROCEDURE z_S_SYS_Admin_KnowledgeBaseSchema
    @TableName NVARCHAR(128) = NULL  -- Optional parameter for specific table
AS
BEGIN
    SET NOCOUNT ON;
    
    -- If specific table requested
    IF @TableName IS NOT NULL
    BEGIN
        -- Table Information
        SELECT 
            'TABLE_INFO' as QueryType,
            t.TABLE_NAME,
            t.TABLE_TYPE,
            t.TABLE_SCHEMA,
            CASE 
                WHEN t.TABLE_NAME LIKE 'REF_%' THEN 'Reference Table'
                ELSE 'Data Table'
            END as TableCategory
        FROM INFORMATION_SCHEMA.TABLES t
        WHERE t.TABLE_NAME = @TableName
        AND t.TABLE_TYPE = 'BASE TABLE'
        
        -- Column Information for specific table
        SELECT 
            'COLUMN_INFO' as QueryType,
            c.TABLE_NAME,
            c.COLUMN_NAME,
            c.DATA_TYPE,
            c.CHARACTER_MAXIMUM_LENGTH,
            c.IS_NULLABLE,
            c.COLUMN_DEFAULT,
            c.ORDINAL_POSITION,
            CASE 
                WHEN pk.COLUMN_NAME IS NOT NULL THEN 'PRIMARY KEY'
                WHEN fk.COLUMN_NAME IS NOT NULL THEN 'FOREIGN KEY'
                ELSE 'REGULAR'
            END as ColumnType,
            fk.REFERENCED_TABLE_NAME,
            fk.REFERENCED_COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS c
        LEFT JOIN (
            SELECT ku.TABLE_NAME, ku.COLUMN_NAME
            FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
            INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku
                ON tc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
            WHERE tc.CONSTRAINT_TYPE = 'PRIMARY KEY'
        ) pk ON c.TABLE_NAME = pk.TABLE_NAME AND c.COLUMN_NAME = pk.COLUMN_NAME
        LEFT JOIN (
            SELECT 
                ku.TABLE_NAME,
                ku.COLUMN_NAME,
                ku2.TABLE_NAME as REFERENCED_TABLE_NAME,
                ku2.COLUMN_NAME as REFERENCED_COLUMN_NAME
            FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
            INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku
                ON rc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
            INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku2
                ON rc.UNIQUE_CONSTRAINT_NAME = ku2.CONSTRAINT_NAME
        ) fk ON c.TABLE_NAME = fk.TABLE_NAME AND c.COLUMN_NAME = fk.COLUMN_NAME
        WHERE c.TABLE_NAME = @TableName
        ORDER BY c.ORDINAL_POSITION
        
        -- Row count for specific table
        DECLARE @sql NVARCHAR(MAX)
        DECLARE @rowCount INT
        SET @sql = N'SELECT @count = COUNT(*) FROM ' + QUOTENAME(@TableName)
        EXEC sp_executesql @sql, N'@count INT OUTPUT', @count = @rowCount OUTPUT
        
        SELECT 
            'ROW_COUNT' as QueryType,
            @TableName as TABLE_NAME,
            @rowCount as ROW_COUNT
    END
    ELSE
    BEGIN
        -- All Tables Overview
        SELECT 
            'ALL_TABLES' as QueryType,
            t.TABLE_NAME,
            t.TABLE_TYPE,
            t.TABLE_SCHEMA,
            CASE 
                WHEN t.TABLE_NAME LIKE 'REF_%' THEN 'Reference Table'
                ELSE 'Data Table'
            END as TableCategory,
            (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS c WHERE c.TABLE_NAME = t.TABLE_NAME) as ColumnCount
        FROM INFORMATION_SCHEMA.TABLES t
        WHERE t.TABLE_TYPE = 'BASE TABLE'
        AND t.TABLE_NAME LIKE 'REF_%'  -- Focus on REF tables for KnowledgeBase
        ORDER BY t.TABLE_NAME
        
        -- All Relationships
        SELECT 
            'RELATIONSHIPS' as QueryType,
            ku.TABLE_NAME as FromTable,
            ku.COLUMN_NAME as FromColumn,
            ku2.TABLE_NAME as ToTable,
            ku2.COLUMN_NAME as ToColumn,
            rc.CONSTRAINT_NAME as RelationshipName
        FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
        INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku
            ON rc.CONSTRAINT_NAME = ku.CONSTRAINT_NAME
        INNER JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE ku2
            ON rc.UNIQUE_CONSTRAINT_NAME = ku2.CONSTRAINT_NAME
        WHERE ku.TABLE_NAME LIKE 'REF_%' OR ku2.TABLE_NAME LIKE 'REF_%'
        ORDER BY ku.TABLE_NAME, ku2.TABLE_NAME
        
        -- Row counts for all tables
        SELECT 
            'ALL_ROW_COUNTS' as QueryType,
            t.TABLE_NAME,
            (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS c WHERE c.TABLE_NAME = t.TABLE_NAME) as ColumnCount
        FROM INFORMATION_SCHEMA.TABLES t
        WHERE t.TABLE_TYPE = 'BASE TABLE'
        AND t.TABLE_NAME LIKE 'REF_%'
        ORDER BY t.TABLE_NAME
    END
END
GO

-- Grant execute permissions (adjust as needed for your security model)
-- GRANT EXECUTE ON sp_GetKnowledgeBaseSchema TO [YourUserOrRole]

PRINT 'Stored procedure z_S_SYS_Admin_KnowledgeBaseSchema created successfully!'
PRINT 'Usage:'
PRINT '  EXEC z_S_SYS_Admin_KnowledgeBaseSchema                    -- Get all tables'
PRINT '  EXEC z_S_SYS_Admin_KnowledgeBaseSchema @TableName = ''REF_Entities''  -- Get specific table'
