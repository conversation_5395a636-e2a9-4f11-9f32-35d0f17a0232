USE KnowledgeBase
GO

/*
================================================
AUTHOR:    MARK ABRAMS & Augment Agent (AI Assistant)
DATE:      2025-06-28
PURPOSE:   Self-Healer session retrieval stored procedures for dashboard integration
NOTES:     Replaces in-memory session data with KnowledgeBase queries.
           Optimized for dashboard real-time display and analytics.
           Follows established SQL naming conventions.
================================================
*/

-- =====================================================
-- 1. GET RECENT HEALING SESSIONS
-- =====================================================

-- Drop procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_SelfHealer_RecentSessions_P')
    DROP PROCEDURE S_SYS_SelfHealer_RecentSessions_P
GO

/*
================================================
AUTHOR:    MARK ABRAMS & Augment Agent (AI Assistant)
DATE:      2025-06-28
PURPOSE:   Get recent healing sessions for dashboard display
NOTES:     Returns last N sessions with key attributes for dashboard table
           Includes session ID, status, duration, success rate, timestamps
================================================
PARAMETERS:
   @Limit INT - Maximum number of sessions to return (default 10)

RETURNS:   Recent sessions with ID, SessionID, Status, Duration, Success, CreateDate
================================================
USAGE:

   EXEC S_SYS_SelfHealer_RecentSessions_P @Limit = 10
   EXEC S_SYS_SelfHealer_RecentSessions_P @Limit = 50

================================================
*/

CREATE PROCEDURE S_SYS_SelfHealer_RecentSessions_P
    @Limit INT = 10
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT TOP (@Limit)
        e.ID as EntityID,
        REPLACE(e.Name, 'Session_', '') as SessionID,
        e.CreateDate,
        COALESCE(duration_val.Value, 'Unknown') as Duration,
        COALESCE(success_val.Value, 'Unknown') as SuccessRate,
        CASE 
            WHEN success_val.Value LIKE '%90-100%' THEN 'Resolved'
            WHEN success_val.Value LIKE '%0-29%' THEN 'Failed'
            ELSE 'In Progress'
        END as Status,
        CASE 
            WHEN success_val.Value LIKE '%90-100%' THEN 1
            ELSE 0
        END as Success
    FROM REF_Entities e
    LEFT JOIN XRF_EntityAttributeValue eav_duration ON e.ID = eav_duration.EntityID
    LEFT JOIN REF_Attributes attr_duration ON eav_duration.AttributeID = attr_duration.ID 
        AND attr_duration.Name = 'Session_Duration'
    LEFT JOIN REF_EntityValues duration_val ON eav_duration.ValueID = duration_val.ID
    
    LEFT JOIN XRF_EntityAttributeValue eav_success ON e.ID = eav_success.EntityID
    LEFT JOIN REF_Attributes attr_success ON eav_success.AttributeID = attr_success.ID 
        AND attr_success.Name = 'Solution_Success_Rate'
    LEFT JOIN REF_EntityValues success_val ON eav_success.ValueID = success_val.ID
    
    WHERE e.Name LIKE 'Session_%'
    ORDER BY e.CreateDate DESC
END
GO

-- =====================================================
-- 2. GET SESSION DETAILS BY SESSION ID
-- =====================================================

-- Drop procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_SelfHealer_SessionDetails_P')
    DROP PROCEDURE S_SYS_SelfHealer_SessionDetails_P
GO

/*
================================================
AUTHOR:    MARK ABRAMS & Augment Agent (AI Assistant)
DATE:      2025-06-28
PURPOSE:   Get detailed information for a specific healing session
NOTES:     Returns complete session data including attributes, facts, and evidence
           Used for session detail views and debugging
================================================
PARAMETERS:
   @SessionID NVARCHAR(255) - Session ID to retrieve details for

RETURNS:   Complete session information with attributes and related data
================================================
USAGE:

   EXEC S_SYS_SelfHealer_SessionDetails_P @SessionID = 'heal_1735398123456'

================================================
*/

CREATE PROCEDURE S_SYS_SelfHealer_SessionDetails_P
    @SessionID NVARCHAR(255)
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Get session entity
    DECLARE @EntityID INT
    SELECT @EntityID = ID FROM REF_Entity WHERE Name = 'Session_' + @SessionID
    
    IF @EntityID IS NULL
    BEGIN
        SELECT 'Session not found' as Error
        RETURN
    END
    
    -- Return session details with all attributes
    SELECT 
        e.ID as EntityID,
        REPLACE(e.Name, 'Session_', '') as SessionID,
        e.CreateDate,
        attr.Name as AttributeName,
        val.Value as AttributeValue,
        val.Name as ValueName
    FROM REF_Entity e
    LEFT JOIN XRF_Entity_Attribute_Value eav ON e.ID = eav.EntityID
    LEFT JOIN REF_Attribute attr ON eav.AttributeID = attr.ID
    LEFT JOIN REF_EntityValue val ON eav.EntityValueID = val.ID
    WHERE e.ID = @EntityID
    ORDER BY attr.Name
END
GO

-- =====================================================
-- 3. GET SESSION ANALYTICS
-- =====================================================

-- Drop procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_SelfHealer_SessionAnalytics_P')
    DROP PROCEDURE S_SYS_SelfHealer_SessionAnalytics_P
GO

/*
================================================
AUTHOR:    MARK ABRAMS & Augment Agent (AI Assistant)
DATE:      2025-06-28
PURPOSE:   Get session analytics for dashboard metrics
NOTES:     Calculates success rates, average healing times, and session counts
           Replaces in-memory calculations with database-driven analytics
================================================
PARAMETERS:
   @DaysBack INT - Number of days to look back for analytics (default 30)

RETURNS:   Analytics data for dashboard display
================================================
USAGE:

   EXEC S_SYS_SelfHealer_SessionAnalytics_P @DaysBack = 30
   EXEC S_SYS_SelfHealer_SessionAnalytics_P @DaysBack = 7

================================================
*/

CREATE PROCEDURE S_SYS_SelfHealer_SessionAnalytics_P
    @DaysBack INT = 30
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartDate DATETIME = DATEADD(day, -@DaysBack, GETDATE())
    
    SELECT 
        COUNT(*) as TotalSessions,
        SUM(CASE WHEN success_val.Value LIKE '%90-100%' THEN 1 ELSE 0 END) as SuccessfulSessions,
        SUM(CASE WHEN success_val.Value LIKE '%0-29%' THEN 1 ELSE 0 END) as FailedSessions,
        CASE 
            WHEN COUNT(*) > 0 THEN 
                CAST(SUM(CASE WHEN success_val.Value LIKE '%90-100%' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as DECIMAL(5,2))
            ELSE 0 
        END as SuccessRate,
        AVG(
            CASE
                WHEN duration_val.Value LIKE '%seconds' THEN
                    CASE
                        WHEN ISNUMERIC(REPLACE(REPLACE(duration_val.Value, ' seconds', ''), 's', '')) = 1 THEN
                            CAST(REPLACE(REPLACE(duration_val.Value, ' seconds', ''), 's', '') as FLOAT)
                        ELSE NULL
                    END
                ELSE NULL
            END
        ) as AverageHealingTime,
        COUNT(CASE WHEN e.CreateDate >= DATEADD(day, -1, GETDATE()) THEN 1 END) as SessionsLast24Hours,
        COUNT(CASE WHEN e.CreateDate >= DATEADD(day, -7, GETDATE()) THEN 1 END) as SessionsLastWeek
    FROM REF_Entity e
    LEFT JOIN XRF_Entity_Attribute_Value eav_success ON e.ID = eav_success.EntityID
    LEFT JOIN REF_Attribute attr_success ON eav_success.AttributeID = attr_success.ID
        AND attr_success.Name = 'Solution_Success_Rate'
    LEFT JOIN REF_EntityValue success_val ON eav_success.EntityValueID = success_val.ID

    LEFT JOIN XRF_Entity_Attribute_Value eav_duration ON e.ID = eav_duration.EntityID
    LEFT JOIN REF_Attribute attr_duration ON eav_duration.AttributeID = attr_duration.ID
        AND attr_duration.Name = 'Session_Duration'
    LEFT JOIN REF_EntityValue duration_val ON eav_duration.EntityValueID = duration_val.ID
    
    WHERE e.Name LIKE 'Session_%'
    AND e.CreateDate >= @StartDate
END
GO

-- =====================================================
-- 4. GET SESSIONS BY STATUS FILTER
-- =====================================================

-- Drop procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'S_SYS_SelfHealer_SessionsByStatus_Prms')
    DROP PROCEDURE S_SYS_SelfHealer_SessionsByStatus_Prms
GO

/*
================================================
AUTHOR:    MARK ABRAMS & Augment Agent (AI Assistant)
DATE:      2025-06-28
PURPOSE:   Get sessions filtered by status and date range
NOTES:     Supports dashboard filtering and search functionality
           Allows filtering by success status and time periods
================================================
PARAMETERS:
   @Status NVARCHAR(50) - Status filter: 'Resolved', 'Failed', 'All' (default 'All')
   @DaysBack INT - Number of days to look back (default 30)
   @Limit INT - Maximum number of results (default 50)

RETURNS:   Filtered sessions matching criteria
================================================
USAGE:

   EXEC S_SYS_SelfHealer_SessionsByStatus_Prms @Status = 'Resolved', @DaysBack = 7, @Limit = 20
   EXEC S_SYS_SelfHealer_SessionsByStatus_Prms @Status = 'Failed', @DaysBack = 30, @Limit = 10

================================================
*/

CREATE PROCEDURE S_SYS_SelfHealer_SessionsByStatus_Prms
    @Status NVARCHAR(50) = 'All',
    @DaysBack INT = 30,
    @Limit INT = 50
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartDate DATETIME = DATEADD(day, -@DaysBack, GETDATE())
    
    SELECT TOP (@Limit)
        e.ID as EntityID,
        REPLACE(e.Name, 'Session_', '') as SessionID,
        e.CreateDate,
        COALESCE(duration_val.Value, 'Unknown') as Duration,
        COALESCE(success_val.Value, 'Unknown') as SuccessRate,
        CASE 
            WHEN success_val.Value LIKE '%90-100%' THEN 'Resolved'
            WHEN success_val.Value LIKE '%0-29%' THEN 'Failed'
            ELSE 'In Progress'
        END as Status,
        CASE 
            WHEN success_val.Value LIKE '%90-100%' THEN 1
            ELSE 0
        END as Success
    FROM REF_Entities e
    LEFT JOIN XRF_EntityAttributeValue eav_duration ON e.ID = eav_duration.EntityID
    LEFT JOIN REF_Attributes attr_duration ON eav_duration.AttributeID = attr_duration.ID 
        AND attr_duration.Name = 'Session_Duration'
    LEFT JOIN REF_EntityValues duration_val ON eav_duration.ValueID = duration_val.ID
    
    LEFT JOIN XRF_Entity_Attribute_Value eav_success ON e.ID = eav_success.EntityID
    LEFT JOIN REF_Attribute attr_success ON eav_success.AttributeID = attr_success.ID
        AND attr_success.Name = 'Solution_Success_Rate'
    LEFT JOIN REF_EntityValue success_val ON eav_success.EntityValueID = success_val.ID
    
    WHERE e.Name LIKE 'Session_%'
    AND e.CreateDate >= @StartDate
    AND (
        @Status = 'All' OR
        (@Status = 'Resolved' AND success_val.Value LIKE '%90-100%') OR
        (@Status = 'Failed' AND success_val.Value LIKE '%0-29%') OR
        (@Status = 'In Progress' AND success_val.Value NOT LIKE '%90-100%' AND success_val.Value NOT LIKE '%0-29%')
    )
    ORDER BY e.CreateDate DESC
END
GO

PRINT 'Self-Healer session retrieval stored procedures created successfully!'
PRINT 'Created procedures:'
PRINT '  - S_SYS_SelfHealer_RecentSessions_P'
PRINT '  - S_SYS_SelfHealer_SessionDetails_P'
PRINT '  - S_SYS_SelfHealer_SessionAnalytics_P'
PRINT '  - S_SYS_SelfHealer_SessionsByStatus_Prms'
