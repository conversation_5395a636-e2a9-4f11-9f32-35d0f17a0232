#!/usr/bin/env python3
"""
Dynamic KnowledgeBase Schema Retrieval Tool
============================================

This script uses the z_S_SYS_Admin_KnowledgeBaseSchema stored procedure to dynamically
retrieve schema information from the KnowledgeBase database.

Location: Self_Healer/Documentation/DB_Admin/get_knowledgebase_schema.py
Stored Procedure: z_S_SYS_Admin_KnowledgeBaseSchema (see create_schema_procedure.sql)

Usage:
    python get_knowledgebase_schema.py                    # Get all tables
    python get_knowledgebase_schema.py REF_Entities       # Get specific table
    python get_knowledgebase_schema.py --format json      # Output as JSON
    python get_knowledgebase_schema.py --format markdown  # Output as Markdown

Created: 2025-06-27
Purpose: Dynamic schema information for Self-Healer KnowledgeBase integration
"""

import asyncio
import sys
import json
import os
from datetime import datetime
from typing import Optional, Dict, Any, List

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
sys.path.insert(0, project_root)

from n8n_builder.mcp_database_tool import MCPDatabaseTool


class KnowledgeBaseSchemaRetriever:
    """Dynamic schema retrieval for KnowledgeBase."""
    
    def __init__(self):
        self.db_tool = MCPDatabaseTool('knowledgebase')
        self.schema_data = {}
    
    async def get_schema(self, table_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Get schema information using the stored procedure.
        
        Args:
            table_name: Optional specific table name. If None, gets all tables.
            
        Returns:
            Dictionary containing schema information
        """
        try:
            if table_name:
                query = "EXEC z_S_SYS_Admin_KnowledgeBaseSchema @TableName = ?"
                params = [table_name]
                print(f"🔍 Getting schema for table: {table_name}")
            else:
                query = "EXEC z_S_SYS_Admin_KnowledgeBaseSchema"
                params = []
                print("🔍 Getting schema for all tables")
            
            # Execute the stored procedure
            result = await self.db_tool.execute_query(query, params)
            
            if result.get('status') == 'success':
                return self._process_schema_results(result, table_name)
            else:
                print(f"❌ Error executing stored procedure: {result.get('error', 'Unknown error')}")
                return {}
                
        except Exception as e:
            print(f"❌ Exception getting schema: {e}")
            return {}
    
    def _process_schema_results(self, result: Dict[str, Any], table_name: Optional[str]) -> Dict[str, Any]:
        """Process the results from the stored procedure."""
        
        schema_info = {
            'database': 'KnowledgeBase',
            'retrieved_at': datetime.now().isoformat(),
            'table_requested': table_name,
            'tables': {},
            'relationships': [],
            'summary': {}
        }
        
        rows = result.get('rows', [])
        
        # Group results by QueryType
        grouped_results = {}
        for row in rows:
            query_type = row.get('QueryType', 'UNKNOWN')
            if query_type not in grouped_results:
                grouped_results[query_type] = []
            grouped_results[query_type].append(row)
        
        # Process each type of result
        for query_type, data in grouped_results.items():
            if query_type == 'TABLE_INFO':
                self._process_table_info(schema_info, data)
            elif query_type == 'COLUMN_INFO':
                self._process_column_info(schema_info, data)
            elif query_type == 'ROW_COUNT':
                self._process_row_count(schema_info, data)
            elif query_type == 'ALL_TABLES':
                self._process_all_tables(schema_info, data)
            elif query_type == 'RELATIONSHIPS':
                self._process_relationships(schema_info, data)
            elif query_type == 'ALL_ROW_COUNTS':
                self._process_all_row_counts(schema_info, data)
        
        return schema_info
    
    def _process_table_info(self, schema_info: Dict, data: List[Dict]):
        """Process table information."""
        for row in data:
            table_name = row['TABLE_NAME']
            schema_info['tables'][table_name] = {
                'name': table_name,
                'type': row['TABLE_TYPE'],
                'schema': row['TABLE_SCHEMA'],
                'category': row['TableCategory'],
                'columns': {},
                'row_count': 0
            }
    
    def _process_column_info(self, schema_info: Dict, data: List[Dict]):
        """Process column information."""
        for row in data:
            table_name = row['TABLE_NAME']
            if table_name not in schema_info['tables']:
                schema_info['tables'][table_name] = {'columns': {}}
            
            column_name = row['COLUMN_NAME']
            schema_info['tables'][table_name]['columns'][column_name] = {
                'name': column_name,
                'data_type': row['DATA_TYPE'],
                'max_length': row['CHARACTER_MAXIMUM_LENGTH'],
                'nullable': row['IS_NULLABLE'] == 'YES',
                'default': row['COLUMN_DEFAULT'],
                'position': row['ORDINAL_POSITION'],
                'column_type': row['ColumnType'],
                'referenced_table': row.get('REFERENCED_TABLE_NAME'),
                'referenced_column': row.get('REFERENCED_COLUMN_NAME')
            }
    
    def _process_row_count(self, schema_info: Dict, data: List[Dict]):
        """Process row count information."""
        for row in data:
            table_name = row['TABLE_NAME']
            if table_name in schema_info['tables']:
                schema_info['tables'][table_name]['row_count'] = row['ROW_COUNT']
    
    def _process_all_tables(self, schema_info: Dict, data: List[Dict]):
        """Process all tables overview."""
        for row in data:
            table_name = row['TABLE_NAME']
            schema_info['tables'][table_name] = {
                'name': table_name,
                'type': row['TABLE_TYPE'],
                'schema': row['TABLE_SCHEMA'],
                'category': row['TableCategory'],
                'column_count': row['ColumnCount'],
                'columns': {},
                'row_count': 0
            }
    
    def _process_relationships(self, schema_info: Dict, data: List[Dict]):
        """Process relationship information."""
        for row in data:
            schema_info['relationships'].append({
                'from_table': row['FromTable'],
                'from_column': row['FromColumn'],
                'to_table': row['ToTable'],
                'to_column': row['ToColumn'],
                'constraint_name': row['RelationshipName']
            })
    
    def _process_all_row_counts(self, schema_info: Dict, data: List[Dict]):
        """Process all row counts."""
        for row in data:
            table_name = row['TABLE_NAME']
            if table_name in schema_info['tables']:
                schema_info['tables'][table_name]['column_count'] = row['ColumnCount']
    
    def format_as_markdown(self, schema_info: Dict[str, Any]) -> str:
        """Format schema information as Markdown."""
        md = []
        md.append("# KnowledgeBase Schema Information")
        md.append(f"**Retrieved:** {schema_info['retrieved_at']}")
        md.append(f"**Database:** {schema_info['database']}")
        
        if schema_info['table_requested']:
            md.append(f"**Table:** {schema_info['table_requested']}")
        
        md.append("\n## Tables\n")
        
        for table_name, table_info in schema_info['tables'].items():
            md.append(f"### {table_name}")
            md.append(f"- **Type:** {table_info.get('type', 'N/A')}")
            md.append(f"- **Category:** {table_info.get('category', 'N/A')}")
            md.append(f"- **Row Count:** {table_info.get('row_count', 'N/A')}")
            
            if table_info.get('columns'):
                md.append("\n#### Columns")
                md.append("| Column | Type | Nullable | Default | Key Type |")
                md.append("|--------|------|----------|---------|----------|")
                
                for col_name, col_info in table_info['columns'].items():
                    nullable = "Yes" if col_info['nullable'] else "No"
                    default = col_info['default'] or "None"
                    key_type = col_info['column_type']
                    md.append(f"| {col_name} | {col_info['data_type']} | {nullable} | {default} | {key_type} |")
            
            md.append("")
        
        if schema_info['relationships']:
            md.append("## Relationships\n")
            md.append("| From Table | From Column | To Table | To Column |")
            md.append("|------------|-------------|----------|-----------|")
            
            for rel in schema_info['relationships']:
                md.append(f"| {rel['from_table']} | {rel['from_column']} | {rel['to_table']} | {rel['to_column']} |")
        
        return "\n".join(md)


async def main():
    """Main function to handle command line usage."""
    
    # Parse command line arguments
    table_name = None
    output_format = 'text'
    
    if len(sys.argv) > 1:
        for arg in sys.argv[1:]:
            if arg == '--format':
                continue
            elif arg in ['json', 'markdown', 'text']:
                output_format = arg
            elif not arg.startswith('--'):
                table_name = arg
    
    # Get schema information
    retriever = KnowledgeBaseSchemaRetriever()
    schema_info = await retriever.get_schema(table_name)
    
    if not schema_info:
        print("❌ Failed to retrieve schema information")
        return
    
    # Output in requested format
    if output_format == 'json':
        print(json.dumps(schema_info, indent=2, default=str))
    elif output_format == 'markdown':
        print(retriever.format_as_markdown(schema_info))
    else:
        # Text format (default)
        print("📊 KnowledgeBase Schema Information")
        print(f"Retrieved: {schema_info['retrieved_at']}")
        print(f"Database: {schema_info['database']}")
        
        if schema_info['table_requested']:
            print(f"Table: {schema_info['table_requested']}")
        
        print(f"\n📋 Tables Found: {len(schema_info['tables'])}")
        
        for table_name, table_info in schema_info['tables'].items():
            print(f"\n🔹 {table_name}")
            print(f"   Type: {table_info.get('type', 'N/A')}")
            print(f"   Category: {table_info.get('category', 'N/A')}")
            print(f"   Columns: {len(table_info.get('columns', {}))}")
            print(f"   Row Count: {table_info.get('row_count', 'N/A')}")
            
            if table_info.get('columns'):
                print("   Column Details:")
                for col_name, col_info in table_info['columns'].items():
                    key_info = f" ({col_info['column_type']})" if col_info['column_type'] != 'REGULAR' else ""
                    print(f"     - {col_name}: {col_info['data_type']}{key_info}")
        
        if schema_info['relationships']:
            print(f"\n🔗 Relationships: {len(schema_info['relationships'])}")
            for rel in schema_info['relationships']:
                print(f"   {rel['from_table']}.{rel['from_column']} → {rel['to_table']}.{rel['to_column']}")


if __name__ == "__main__":
    asyncio.run(main())
