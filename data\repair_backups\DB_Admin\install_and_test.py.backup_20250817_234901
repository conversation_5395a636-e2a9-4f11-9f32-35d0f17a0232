#!/usr/bin/env python3
"""
KnowledgeBase DB Admin Installation and Test Script
===================================================

This script installs the stored procedure and tests the schema retrieval system.

Location: Self_Healer/Documentation/DB_Admin/install_and_test.py
Purpose: One-click setup and testing of KnowledgeBase schema tools

Usage:
    python install_and_test.py                    # Install procedure and test
    python install_and_test.py --test-only        # Test existing installation
    python install_and_test.py --install-only     # Install procedure only

Created: 2025-06-27
"""

import asyncio
import sys
import os
from datetime import datetime

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
sys.path.insert(0, project_root)

from n8n_builder.mcp_database_tool import MCPDatabaseTool


class KnowledgeBaseInstaller:
    """Install and test KnowledgeBase schema tools."""
    
    def __init__(self):
        self.db_tool = MCPDatabaseTool('knowledgebase')
        self.script_dir = os.path.dirname(__file__)
    
    async def install_stored_procedure(self):
        """Install the sp_GetKnowledgeBaseSchema stored procedure."""
        
        print("🔧 Installing KnowledgeBase schema stored procedure...")
        
        sql_file = os.path.join(self.script_dir, 'create_schema_procedure.sql')
        
        if not os.path.exists(sql_file):
            print(f"❌ SQL file not found: {sql_file}")
            return False
        
        try:
            # Read the SQL file
            with open(sql_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # Split by GO statements and execute each batch
            batches = [batch.strip() for batch in sql_content.split('GO') if batch.strip()]
            
            for i, batch in enumerate(batches):
                # Skip USE statements and comments
                if (batch.startswith('USE ') or 
                    batch.startswith('--') or 
                    batch.startswith('PRINT ') or
                    'GRANT EXECUTE' in batch):
                    continue
                
                try:
                    print(f"   Executing batch {i+1}...")
                    result = await self.db_tool.execute_query(batch)
                    
                    if result.get('status') == 'success':
                        print(f"   ✅ Batch {i+1} completed successfully")
                    else:
                        print(f"   ⚠️ Batch {i+1} result: {result}")
                        
                except Exception as e:
                    print(f"   ❌ Error in batch {i+1}: {e}")
                    return False
            
            print("✅ Stored procedure installation completed!")
            return True
            
        except Exception as e:
            print(f"❌ Failed to install stored procedure: {e}")
            return False
    
    async def test_schema_retrieval(self):
        """Test the schema retrieval functionality."""
        
        print("\n🧪 Testing schema retrieval...")
        
        try:
            # Test 1: Get all tables
            print("   Test 1: Getting all tables...")
            result = await self.db_tool.execute_query("EXEC z_S_SYS_Admin_KnowledgeBaseSchema")
            
            if result.get('status') == 'success':
                rows = result.get('rows', [])
                table_count = len([r for r in rows if r.get('QueryType') == 'ALL_TABLES'])
                print(f"   ✅ Found {table_count} tables")
            else:
                print(f"   ❌ Failed to get all tables: {result.get('error')}")
                return False
            
            # Test 2: Get specific table
            print("   Test 2: Getting REF_Entities table...")
            result = await self.db_tool.execute_query(
                "EXEC z_S_SYS_Admin_KnowledgeBaseSchema @TableName = ?",
                ['REF_Entities']
            )
            
            if result.get('status') == 'success':
                rows = result.get('rows', [])
                column_count = len([r for r in rows if r.get('QueryType') == 'COLUMN_INFO'])
                print(f"   ✅ REF_Entities has {column_count} columns")
            else:
                print(f"   ❌ Failed to get REF_Entities: {result.get('error')}")
                return False
            
            # Test 3: Test Python schema retriever
            print("   Test 3: Testing Python schema retriever...")
            try:
                from get_knowledgebase_schema import KnowledgeBaseSchemaRetriever
                
                retriever = KnowledgeBaseSchemaRetriever()
                schema_info = await retriever.get_schema()
                
                if schema_info and schema_info.get('tables'):
                    table_count = len(schema_info['tables'])
                    print(f"   ✅ Python retriever found {table_count} tables")
                else:
                    print("   ❌ Python retriever returned no data")
                    return False
                    
            except ImportError:
                print("   ⚠️ Could not import Python schema retriever (file may not be in path)")
            except Exception as e:
                print(f"   ❌ Python retriever error: {e}")
                return False
            
            print("✅ All tests passed!")
            return True
            
        except Exception as e:
            print(f"❌ Testing failed: {e}")
            return False
    
    async def show_current_schema(self):
        """Show current schema information."""
        
        print("\n📊 Current KnowledgeBase Schema:")
        
        try:
            result = await self.db_tool.execute_query("EXEC z_S_SYS_Admin_KnowledgeBaseSchema")
            
            if result.get('status') != 'success':
                print(f"❌ Failed to get schema: {result.get('error')}")
                return
            
            rows = result.get('rows', [])
            
            # Show tables
            tables = [r for r in rows if r.get('QueryType') == 'ALL_TABLES']
            print(f"\n📋 Tables ({len(tables)}):")
            
            for table in tables:
                print(f"   🔹 {table['TABLE_NAME']}")
                print(f"      Category: {table['TableCategory']}")
                print(f"      Columns: {table['ColumnCount']}")
            
            # Show relationships
            relationships = [r for r in rows if r.get('QueryType') == 'RELATIONSHIPS']
            if relationships:
                print(f"\n🔗 Relationships ({len(relationships)}):")
                for rel in relationships:
                    print(f"   {rel['FromTable']}.{rel['FromColumn']} → {rel['ToTable']}.{rel['ToColumn']}")
            
        except Exception as e:
            print(f"❌ Failed to show schema: {e}")


async def main():
    """Main function to handle command line usage."""
    
    installer = KnowledgeBaseInstaller()
    
    # Parse command line arguments
    install_only = '--install-only' in sys.argv
    test_only = '--test-only' in sys.argv
    
    print("🚀 KnowledgeBase DB Admin Setup")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    success = True
    
    # Install stored procedure (unless test-only)
    if not test_only:
        success = await installer.install_stored_procedure()
    
    # Test functionality (unless install-only)
    if success and not install_only:
        success = await installer.test_schema_retrieval()
    
    # Show current schema
    if success:
        await installer.show_current_schema()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Setup completed successfully!")
        print("\n📚 Usage:")
        print("   python get_knowledgebase_schema.py                    # Get all tables")
        print("   python get_knowledgebase_schema.py REF_Entities       # Get specific table")
        print("   python get_knowledgebase_schema.py --format markdown  # Markdown output")
    else:
        print("❌ Setup failed. Please check the errors above.")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
