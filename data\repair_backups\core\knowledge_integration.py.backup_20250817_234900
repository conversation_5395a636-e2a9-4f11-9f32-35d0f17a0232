"""
KnowledgeBase Integration Module for Self-Healer
Integrates Self-Healer data into the existing KnowledgeBase structure.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import json
import hashlib

from .generic_types import <PERSON>ric<PERSON><PERSON>r<PERSON><PERSON>il, <PERSON>rror<PERSON><PERSON><PERSON><PERSON>, <PERSON>rrorSeverity

# Type imports for annotations
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from .healer_manager import Healing<PERSON>ession, HealingStatus

# Import the MCP database tool
import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
from n8n_builder.mcp_database_tool import MCPDatabaseTool

logger = logging.getLogger(__name__)


@dataclass
class KnowledgeEntity:
    """Represents an entity in the KnowledgeBase."""
    entity_id: Optional[int]
    name: str
    entity_type: str  # 'error_type', 'solution', 'component', 'session'


@dataclass
class KnowledgeFact:
    """Represents a fact in the KnowledgeBase."""
    fact_id: Optional[int]
    name: str
    validity_rating: float
    data_source: str
    fact_type: str  # 'solution', 'pattern', 'diagnosis'


@dataclass
class KnowledgeEvidence:
    """Represents evidence supporting a fact."""
    evidence_id: Optional[int]
    name: str
    fact_id: int
    evidence_text: str
    data_source: str


class KnowledgeBaseIntegrator:
    """Integrates Self-Healer data with KnowledgeBase using validity ratings."""
    
    def __init__(self, connection_name: str = 'knowledgebase'):
        """Initialize the KnowledgeBase integrator."""
        self.db_tool = MCPDatabaseTool(connection_name)
        self.logger = logger
        
        # Cache for entity and category IDs
        self.entity_cache: Dict[str, int] = {}
        self.category_cache: Dict[str, int] = {}
        self.attribute_cache: Dict[str, int] = {}
        
        self.logger.info("KnowledgeBase Integrator initialized")
    
    async def initialize_selfhealer_schema(self):
        """Initialize Self-Healer specific categories, entities, and attributes."""
        try:
            # Create Self-Healer categories
            await self._create_categories()
            
            # Create core system entities
            await self._create_system_entities()
            
            # Create error type entities
            await self._create_error_entities()
            
            # Create attributes for error analysis
            await self._create_attributes()
            
            # Create entity values
            await self._create_entity_values()
            
            self.logger.info("Self-Healer schema initialized in KnowledgeBase")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Self-Healer schema: {e}")
            raise
    
    async def _create_categories(self):
        """Create Self-Healer categories."""
        categories = [
            'SelfHealer_NetworkErrors',
            'SelfHealer_ConfigurationErrors', 
            'SelfHealer_JSONParsingErrors',
            'SelfHealer_DatabaseErrors',
            'SelfHealer_WorkflowErrors',
            'SelfHealer_SystemErrors',
            'SelfHealer_SecurityErrors',
            'SelfHealer_PerformanceErrors',
            'SelfHealer_Components',
            'SelfHealer_Sessions',
            'SelfHealer_Solutions',
            'SelfHealer_Patterns'
        ]
        
        for category in categories:
            category_id = await self._get_or_create_category(category)
            self.category_cache[category] = category_id
    
    async def _create_system_entities(self):
        """Create system component entities."""
        components = [
            'N8N_Builder',
            'Self_Healer_Manager',
            'Error_Monitor',
            'Solution_Generator',
            'Context_Analyzer',
            'Learning_Engine',
            'Dashboard_UI'
        ]
        
        for component in components:
            entity_id = await self._get_or_create_entity(component)
            self.entity_cache[component] = entity_id
    
    async def _create_error_entities(self):
        """Create error type entities."""
        error_types = [
            'JSON_Parsing_Error',
            'Database_Connection_Error',
            'Network_Timeout_Error',
            'Configuration_Missing_Error',
            'Workflow_Execution_Error',
            'Authentication_Error',
            'File_Permission_Error',
            'SSL_Certificate_Error',
            'Module_Import_Error',
            'Memory_Error'
        ]
        
        for error_type in error_types:
            entity_id = await self._get_or_create_entity(error_type)
            self.entity_cache[error_type] = entity_id
    
    async def _create_attributes(self):
        """Create attributes for error analysis."""
        attributes = [
            'Error_Severity',
            'Error_Frequency', 
            'Solution_Success_Rate',
            'Implementation_Time',
            'Error_Source_File',
            'Error_Line_Number',
            'Session_Duration',
            'Fix_Complexity',
            'System_Impact',
            'Recovery_Time'
        ]
        
        for attribute in attributes:
            attr_id = await self._get_or_create_attribute(attribute)
            self.attribute_cache[attribute] = attr_id
    
    async def _create_entity_values(self):
        """Create entity values for specific data."""
        entity_values = [
            ('Critical', 'CRITICAL'),
            ('High', 'HIGH'),
            ('Medium', 'MEDIUM'),
            ('Low', 'LOW'),
            ('Info', 'INFO'),
            ('Success_Rate_90_Plus', '90-100%'),
            ('Success_Rate_70_89', '70-89%'),
            ('Success_Rate_50_69', '50-69%'),
            ('Success_Rate_30_49', '30-49%'),
            ('Success_Rate_Below_30', '0-29%')
        ]
        
        for name, value in entity_values:
            await self._get_or_create_entity_value(name, value)
    
    async def record_healing_session(self, session: "HealingSession") -> Dict[str, Any]:
        """Record a complete healing session in KnowledgeBase."""
        try:
            session_data = {
                'session_entity_id': None,
                'solution_facts': [],
                'evidence_records': [],
                'cross_correlations': []
            }
            
            # 1. Create session entity
            session_entity_name = f"Session_{session.session_id}"
            session_entity_id = await self._get_or_create_entity(session_entity_name)
            session_data['session_entity_id'] = session_entity_id
            
            # 2. Record error type and solution as facts
            if session.selected_solution:
                solution_fact = await self._record_solution_fact(session)
                session_data['solution_facts'].append(solution_fact)
                
                # 3. Record evidence for the solution
                evidence = await self._record_solution_evidence(session, solution_fact['fact_id'])
                session_data['evidence_records'].append(evidence)
            
            # 4. Update validity ratings based on success
            if session.success and session.selected_solution:
                await self._update_solution_validity(session)
            
            # 5. Record session attributes
            await self._record_session_attributes(session, session_entity_id)
            
            self.logger.info(f"Recorded healing session {session.session_id} in KnowledgeBase")
            return session_data
            
        except Exception as e:
            self.logger.error(f"Failed to record healing session {session.session_id}: {e}")
            raise
    
    async def _record_solution_fact(self, session: "HealingSession") -> Dict[str, Any]:
        """Record a solution as a fact with validity rating."""
        solution = session.selected_solution
        error_type = session.error_detail.category.value if session.error_detail else "Unknown"
        
        # Calculate initial validity rating based on success
        validity_rating = 75.0 if session.success else 25.0
        
        # Create fact name
        fact_name = f"{error_type}_Solution_{session.session_id[:8]}"
        
        # Insert fact
        query = """
        INSERT INTO REF_Fact (Name, ValidityRating, DataSource) 
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?)
        """
        
        result = await self.db_tool.execute_query(query, [
            fact_name,
            validity_rating,
            f"Self-Healer Session {session.session_id}"
        ])
        
        fact_id = result['rows'][0]['ID'] if result['rows'] else None
        
        return {
            'fact_id': fact_id,
            'name': fact_name,
            'validity_rating': validity_rating,
            'solution_data': solution
        }
    
    async def _record_solution_evidence(self, session: "HealingSession", fact_id: int) -> Dict[str, Any]:
        """Record evidence supporting a solution."""
        evidence_text = self._build_evidence_text(session)
        evidence_name = f"Evidence_{session.session_id[:8]}"
        
        query = """
        INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)
        OUTPUT INSERTED.ID
        VALUES (?, ?, ?, ?)
        """
        
        result = await self.db_tool.execute_query(query, [
            evidence_name,
            fact_id,
            evidence_text,
            f"Self-Healer Automated Analysis {datetime.now().isoformat()}"
        ])
        
        evidence_id = result['rows'][0]['ID'] if result['rows'] else None
        
        return {
            'evidence_id': evidence_id,
            'name': evidence_name,
            'fact_id': fact_id,
            'evidence_text': evidence_text
        }
    
    def _build_evidence_text(self, session: "HealingSession") -> str:
        """Build comprehensive evidence text for a session."""
        evidence_parts = []
        
        # Session outcome
        outcome = "SUCCESS" if session.success else "FAILURE"
        evidence_parts.append(f"Session Outcome: {outcome}")
        
        # Duration
        if session.end_time and session.start_time:
            duration = (session.end_time - session.start_time).total_seconds()
            evidence_parts.append(f"Resolution Time: {duration:.2f} seconds")
        
        # Error details
        if session.error_detail:
            evidence_parts.append(f"Error Type: {session.error_detail.category.value}")
            evidence_parts.append(f"Error Severity: {session.error_detail.severity.value}")
            evidence_parts.append(f"Error Message: {session.error_detail.message}")
        
        # Solution details
        if session.selected_solution:
            solution_type = session.selected_solution.get('type', 'Unknown')
            evidence_parts.append(f"Solution Type: {solution_type}")
            
            if 'confidence' in session.selected_solution:
                confidence = session.selected_solution['confidence']
                evidence_parts.append(f"Solution Confidence: {confidence:.2f}")
        
        # Implementation results
        if session.implementation_results:
            impl_success = session.implementation_results.get('success', False)
            evidence_parts.append(f"Implementation Success: {impl_success}")
        
        # Validation results
        if session.validation_results:
            val_success = session.validation_results.get('success', False)
            evidence_parts.append(f"Validation Success: {val_success}")
        
        return " | ".join(evidence_parts)

    async def _record_session_attributes(self, session: "HealingSession", session_entity_id: int):
        """Record session attributes as entity-attribute-value relationships."""
        try:
            # Duration attribute with numeric support
            if session.end_time and session.start_time:
                duration = (session.end_time - session.start_time).total_seconds()
                duration_value_id = await self._get_or_create_entity_value(
                    f"Duration_{duration:.1f}s",
                    f"{duration:.1f} seconds",
                    numeric_value=duration,
                    value_units="seconds"
                )
                await self._link_entity_attribute_value(
                    session_entity_id,
                    self.attribute_cache['Session_Duration'],
                    duration_value_id
                )

            # Success rate attribute with numeric support
            success_rate_text = "90-100%" if session.success else "0-29%"
            success_rate_numeric = 95.0 if session.success else 14.5  # Midpoint of range
            success_value_id = await self._get_or_create_entity_value(
                f"Success_{session.session_id[:8]}",
                success_rate_text,
                numeric_value=success_rate_numeric,
                value_units="percentage"
            )
            await self._link_entity_attribute_value(
                session_entity_id,
                self.attribute_cache['Solution_Success_Rate'],
                success_value_id
            )

        except Exception as e:
            self.logger.error(f"Failed to record session attributes: {e}")

    async def _update_solution_validity(self, session: "HealingSession"):
        """Update solution validity rating based on new evidence."""
        try:
            if not session.selected_solution:
                return

            solution_type = session.selected_solution.get('type', 'Unknown')

            # Find existing facts for this solution type
            query = """
            SELECT f.ID, f.ValidityRating, COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ?
            GROUP BY f.ID, f.ValidityRating
            """

            result = await self.db_tool.execute_query(query, [f"%{solution_type}%"])

            if result['rows']:
                # Update existing solution validity
                for row in result['rows']:
                    fact_id = row['ID']
                    current_rating = float(row['ValidityRating'])
                    evidence_count = row['EvidenceCount']

                    # Calculate new rating (weighted average)
                    new_evidence_rating = 85.0 if session.success else 15.0
                    updated_rating = ((current_rating * evidence_count) + new_evidence_rating) / (evidence_count + 1)

                    # Update the fact
                    update_query = "UPDATE REF_Fact SET ValidityRating = ? WHERE ID = ?"
                    await self.db_tool.execute_query(update_query, [updated_rating, fact_id])

                    self.logger.info(f"Updated solution validity from {current_rating:.1f}% to {updated_rating:.1f}%")

        except Exception as e:
            self.logger.error(f"Failed to update solution validity: {e}")

    async def get_session_knowledge(self, session_id: str) -> Dict[str, Any]:
        """Retrieve all knowledge related to a specific session."""
        try:
            session_entity_name = f"Session_{session_id}"

            # Get session entity
            entity_query = "SELECT ID FROM REF_Entities WHERE Name = ?"
            entity_result = await self.db_tool.execute_query(entity_query, [session_entity_name])

            if not entity_result['rows']:
                return {'error': f'Session {session_id} not found in KnowledgeBase'}

            entity_id = entity_result['rows'][0]['ID']

            # Get related facts
            facts_query = """
            SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            FROM REF_Fact f
            WHERE f.DataSource LIKE ?
            ORDER BY f.CreateDate DESC
            """
            facts_result = await self.db_tool.execute_query(facts_query, [f"%{session_id}%"])

            # Get evidence for each fact
            evidence_data = []
            for fact in facts_result['rows']:
                evidence_query = """
                SELECT e.ID, e.Name, e.Evidence, e.DataSource, e.CreateDate
                FROM REF_Evidence e
                WHERE e.FactID = ?
                """
                evidence_result = await self.db_tool.execute_query(evidence_query, [fact['ID']])
                evidence_data.extend(evidence_result['rows'])

            # Get session attributes
            attributes_query = """
            SELECT a.Name as AttributeName, ev.EntityValue, eav.CreateDate
            FROM XRF_EntityAttributeValue eav
            JOIN REF_Attributes a ON eav.AttributeID = a.ID
            JOIN REF_EntityValues ev ON eav.EntityValueID = ev.ID
            WHERE eav.EntityID = ?
            """
            attributes_result = await self.db_tool.execute_query(attributes_query, [entity_id])

            return {
                'session_id': session_id,
                'entity_id': entity_id,
                'facts': facts_result['rows'],
                'evidence': evidence_data,
                'attributes': attributes_result['rows'],
                'knowledge_summary': self._build_knowledge_summary(facts_result['rows'], evidence_data)
            }

        except Exception as e:
            self.logger.error(f"Failed to retrieve session knowledge for {session_id}: {e}")
            return {'error': str(e)}

    async def get_error_type_knowledge(self, error_type: str) -> Dict[str, Any]:
        """Retrieve all knowledge related to a specific error type."""
        try:
            # Get all facts related to this error type
            facts_query = """
            SELECT f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate,
                   COUNT(e.ID) as EvidenceCount
            FROM REF_Fact f
            LEFT JOIN REF_Evidence e ON f.ID = e.FactID
            WHERE f.Name LIKE ? OR f.DataSource LIKE ?
            GROUP BY f.ID, f.Name, f.ValidityRating, f.DataSource, f.CreateDate
            ORDER BY f.ValidityRating DESC, f.CreateDate DESC
            """
            facts_result = await self.db_tool.execute_query(facts_query, [f"%{error_type}%", f"%{error_type}%"])

            # Get related opinions
            opinions_query = """
            SELECT o.ID, o.Name, o.ValidityRating, o.Opinion, o.DataSource, o.CreateDate
            FROM REF_Opinion o
            WHERE o.Name LIKE ? OR o.Opinion LIKE ?
            ORDER BY o.ValidityRating DESC
            """
            opinions_result = await self.db_tool.execute_query(opinions_query, [f"%{error_type}%", f"%{error_type}%"])

            # Calculate effectiveness metrics
            effectiveness = self._calculate_error_type_effectiveness(facts_result['rows'])

            return {
                'error_type': error_type,
                'facts': facts_result['rows'],
                'opinions': opinions_result['rows'],
                'effectiveness_metrics': effectiveness,
                'recommendation': self._generate_error_type_recommendation(facts_result['rows'])
            }

        except Exception as e:
            self.logger.error(f"Failed to retrieve error type knowledge for {error_type}: {e}")
            return {'error': str(e)}

    def _build_knowledge_summary(self, facts: List[Dict], evidence: List[Dict]) -> Dict[str, Any]:
        """Build a summary of knowledge for a session."""
        if not facts:
            return {'summary': 'No knowledge recorded for this session'}

        avg_validity = sum(float(fact['ValidityRating']) for fact in facts) / len(facts)
        evidence_count = len(evidence)

        return {
            'total_facts': len(facts),
            'total_evidence': evidence_count,
            'average_validity': round(avg_validity, 2),
            'confidence_level': 'High' if avg_validity > 80 else 'Medium' if avg_validity > 60 else 'Low'
        }

    def _calculate_error_type_effectiveness(self, facts: List[Dict]) -> Dict[str, Any]:
        """Calculate effectiveness metrics for an error type."""
        if not facts:
            return {'effectiveness': 0, 'confidence': 'No Data'}

        total_validity = sum(float(fact['ValidityRating']) for fact in facts)
        avg_validity = total_validity / len(facts)
        evidence_count = sum(fact['EvidenceCount'] for fact in facts)

        return {
            'average_effectiveness': round(avg_validity, 2),
            'total_solutions': len(facts),
            'total_evidence': evidence_count,
            'confidence_level': 'High' if evidence_count > 10 else 'Medium' if evidence_count > 5 else 'Low'
        }

    def _generate_error_type_recommendation(self, facts: List[Dict]) -> str:
        """Generate a recommendation based on error type knowledge."""
        if not facts:
            return "No solutions available for this error type."

        best_solution = max(facts, key=lambda x: float(x['ValidityRating']))
        validity = float(best_solution['ValidityRating'])

        if validity > 85:
            return f"Highly recommended solution: {best_solution['Name']} (Validity: {validity:.1f}%)"
        elif validity > 70:
            return f"Recommended solution: {best_solution['Name']} (Validity: {validity:.1f}%)"
        elif validity > 50:
            return f"Possible solution: {best_solution['Name']} (Validity: {validity:.1f}%) - Use with caution"
        else:
            return f"Limited solution available: {best_solution['Name']} (Validity: {validity:.1f}%) - Requires validation"

    # Helper methods for database operations
    async def _get_or_create_category(self, name: str) -> int:
        """Get or create a category and return its ID."""
        # Check if exists
        query = "SELECT ID FROM REF_Category WHERE Name = ?"
        result = await self.db_tool.execute_query(query, [name])

        if result['rows']:
            return result['rows'][0]['ID']

        # Create new category
        insert_query = "INSERT INTO REF_Category (Name) OUTPUT INSERTED.ID VALUES (?)"
        result = await self.db_tool.execute_query(insert_query, [name])
        return result['rows'][0]['ID']

    async def _get_or_create_entity(self, name: str) -> int:
        """Get or create an entity and return its ID."""
        # Check if exists
        query = "SELECT ID FROM REF_Entities WHERE Name = ?"
        result = await self.db_tool.execute_query(query, [name])

        if result['rows']:
            return result['rows'][0]['ID']

        # Create new entity
        insert_query = "INSERT INTO REF_Entities (Name) OUTPUT INSERTED.ID VALUES (?)"
        result = await self.db_tool.execute_query(insert_query, [name])
        return result['rows'][0]['ID']

    async def _get_or_create_attribute(self, name: str) -> int:
        """Get or create an attribute and return its ID."""
        # Check if exists
        query = "SELECT ID FROM REF_Attributes WHERE Name = ?"
        result = await self.db_tool.execute_query(query, [name])

        if result['rows']:
            return result['rows'][0]['ID']

        # Create new attribute
        insert_query = "INSERT INTO REF_Attributes (Name) OUTPUT INSERTED.ID VALUES (?)"
        result = await self.db_tool.execute_query(insert_query, [name])
        return result['rows'][0]['ID']

    async def _get_or_create_entity_value(self, name: str, value: str, numeric_value: Optional[float] = None, value_units: Optional[str] = None) -> int:
        """Get or create an entity value and return its ID."""
        # Check if exists
        query = "SELECT ID FROM REF_EntityValues WHERE Name = ? AND EntityValue = ?"
        result = await self.db_tool.execute_query(query, [name, value])

        if result['rows']:
            return result['rows'][0]['ID']

        # Create new entity value with enhanced numeric support
        if numeric_value is not None or value_units is not None:
            insert_query = """
            INSERT INTO REF_EntityValues (Name, EntityValue, NumericValue, ValueUnits)
            OUTPUT INSERTED.ID
            VALUES (?, ?, ?, ?)
            """
            result = await self.db_tool.execute_query(insert_query, [name, value, numeric_value, value_units])
        else:
            # Fallback to original format for backward compatibility
            insert_query = "INSERT INTO REF_EntityValues (Name, EntityValue) OUTPUT INSERTED.ID VALUES (?, ?)"
            result = await self.db_tool.execute_query(insert_query, [name, value])

        return result['rows'][0]['ID']

    async def _link_entity_attribute_value(self, entity_id: int, attribute_id: int, value_id: int):
        """Link an entity to an attribute value."""
        name = f"Link_{entity_id}_{attribute_id}_{value_id}"

        # Check if link already exists
        query = """
        SELECT ID FROM XRF_EntityAttributeValue
        WHERE EntityID = ? AND AttributeID = ? AND EntityValueID = ?
        """
        result = await self.db_tool.execute_query(query, [entity_id, attribute_id, value_id])

        if result['rows']:
            return result['rows'][0]['ID']

        # Create new link
        insert_query = """
        INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)
        OUTPUT INSERTED.ID VALUES (?, ?, ?, ?)
        """
        result = await self.db_tool.execute_query(insert_query, [name, entity_id, attribute_id, value_id])
        return result['rows'][0]['ID']

    async def close(self):
        """Close the database connection."""
        if hasattr(self.db_tool, 'close'):
            self.db_tool.close()
        self.logger.info("KnowledgeBase Integrator closed")


# Global instance for easy access
knowledge_integrator = None

def get_knowledge_integrator(connection_name: str = 'knowledgebase') -> KnowledgeBaseIntegrator:
    """Get or create KnowledgeBase integrator instance."""
    global knowledge_integrator
    if knowledge_integrator is None:
        knowledge_integrator = KnowledgeBaseIntegrator(connection_name)
    return knowledge_integrator
