#!/usr/bin/env python3
"""
Simple Database Reference Fixer

Automatically fixes the most common database table name issues.
"""

import re
import sys
import shutil
from pathlib import Path
from datetime import datetime

def fix_database_references(dry_run=True):
    """Fix database reference issues in the codebase."""
    
    print(f"🔧 Simple Database Reference Fixer")
    print(f"{'=' * 40}")
    print(f"Mode: {'DRY RUN' if dry_run else 'LIVE REPAIR'}")
    
    # Define the fixes to apply
    fixes = {
        'REF_Entities': 'REF_Entity',
        'REF_Attributes': 'REF_Attribute', 
        'REF_EntityValues': 'REF_EntityValue',
        'XRF_Entity_Attribute_Value': 'XRF_EntityAttributeValue',
        'XRF_Entity_AttributeValue': 'XRF_EntityAttributeValue',
    }
    
    # Directories to scan
    directories = ['Self_Healer', 'Scripts']
    extensions = {'.py', '.sql'}
    
    project_root = Path(__file__).parent.parent
    total_fixes = 0
    files_modified = 0
    
    for directory in directories:
        dir_path = project_root / directory
        if not dir_path.exists():
            continue
            
        print(f"\n📁 Processing: {directory}")
        
        for file_path in dir_path.rglob('*'):
            if file_path.is_file() and file_path.suffix in extensions:
                
                try:
                    # Read the file
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    
                    original_content = content
                    file_fixes = 0
                    
                    # Apply each fix
                    for incorrect, correct in fixes.items():
                        if incorrect in content:
                            # Count occurrences before fixing
                            count = content.count(incorrect)
                            if count > 0:
                                content = content.replace(incorrect, correct)
                                file_fixes += count
                                print(f"   📄 {file_path.relative_to(project_root)}: {incorrect} → {correct} ({count} times)")
                    
                    # Write the file if changes were made
                    if file_fixes > 0:
                        files_modified += 1
                        total_fixes += file_fixes
                        
                        if not dry_run:
                            # Create backup
                            backup_dir = project_root / 'data' / 'repair_backups'
                            backup_dir.mkdir(parents=True, exist_ok=True)
                            
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                            backup_name = f"{file_path.name}.backup_{timestamp}"
                            backup_path = backup_dir / backup_name
                            
                            shutil.copy2(file_path, backup_path)
                            
                            # Write the fixed content
                            with open(file_path, 'w', encoding='utf-8') as f:
                                f.write(content)
                                
                            print(f"      ✅ Fixed and backed up to: {backup_name}")
                        
                except Exception as e:
                    print(f"   ❌ Error processing {file_path}: {e}")
    
    print(f"\n📊 SUMMARY:")
    print(f"   Files modified: {files_modified}")
    print(f"   Total fixes applied: {total_fixes}")
    
    if dry_run:
        print(f"\n🔍 DRY RUN - No files were actually modified")
        print(f"   Run with --live to apply the fixes")
    else:
        print(f"\n✅ FIXES APPLIED")
        print(f"   Backups saved to: data/repair_backups/")
    
    return total_fixes

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Fix database reference issues')
    parser.add_argument('--live', action='store_true', 
                       help='Apply fixes (default is dry-run)')
    
    args = parser.parse_args()
    
    total_fixes = fix_database_references(dry_run=not args.live)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
