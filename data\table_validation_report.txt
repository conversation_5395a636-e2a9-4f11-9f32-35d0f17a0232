DATABASE TABLE NAME VALIDATION REPORT
==================================================

Files scanned: 455
Issues found: 74

UNKNOWN TABLE NAMES:
--------------------
File: n8n_builder\mcp_database_server.py
Line: 280
Table: REF_FACT
Content: FROM REF_FACT

File: Self_Healer\api\knowledge_endpoints.py
Line: 114
Table: REF_Evidence
Content: LEFT JOIN REF_Evidence e ON f.ID = e.FactID

File: Self_Healer\api\knowledge_endpoints.py
Line: 195
Table: REF_Evidence
Content: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)

File: Self_Healer\api\knowledge_endpoints.py
Line: 218
Table: REF_Evidence
Content: count_query = "SELECT COUNT(*) as EvidenceCount FROM REF_Evidence WHERE FactID = ?"

File: Self_Healer\api\knowledge_endpoints.py
Line: 295
Table: REF_Evidence
Content: FROM REF_Evidence e

File: Self_Healer\core\knowledge_integration.py
Line: 268
Table: REF_Evidence
Content: INSERT INTO REF_Evidence (Name, FactID, Evidence, DataSource)

File: Self_Healer\core\knowledge_integration.py
Line: 377
Table: REF_Evidence
Content: LEFT JOIN REF_Evidence e ON f.ID = e.FactID

File: Self_Healer\core\knowledge_integration.py
Line: 432
Table: REF_Evidence
Content: FROM REF_Evidence e

File: Self_Healer\core\knowledge_integration.py
Line: 441
Table: XRF_EntityAttributeValue
Content: FROM XRF_EntityAttributeValue eav

File: Self_Healer\core\knowledge_integration.py
Line: 442
Table: REF_Attribute
Content: JOIN REF_Attribute a ON eav.AttributeID = a.ID

File: Self_Healer\core\knowledge_integration.py
Line: 443
Table: REF_EntityValue
Content: JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID

File: Self_Healer\core\knowledge_integration.py
Line: 469
Table: REF_Evidence
Content: LEFT JOIN REF_Evidence e ON f.ID = e.FactID

File: Self_Healer\core\knowledge_integration.py
Line: 580
Table: REF_Attribute
Content: query = "SELECT ID FROM REF_Attribute WHERE Name = ?"

File: Self_Healer\core\knowledge_integration.py
Line: 587
Table: REF_Attribute
Content: insert_query = "INSERT INTO REF_Attribute (Name) OUTPUT INSERTED.ID VALUES (?)"

File: Self_Healer\core\knowledge_integration.py
Line: 594
Table: REF_EntityValue
Content: query = "SELECT ID FROM REF_EntityValue WHERE Name = ? AND EntityValue = ?"

File: Self_Healer\core\knowledge_integration.py
Line: 603
Table: REF_EntityValue
Content: INSERT INTO REF_EntityValue (Name, EntityValue, NumericValue, ValueUnits)

File: Self_Healer\core\knowledge_integration.py
Line: 610
Table: REF_EntityValue
Content: insert_query = "INSERT INTO REF_EntityValue (Name, EntityValue) OUTPUT INSERTED.ID VALUES (?, ?)"

File: Self_Healer\core\knowledge_integration.py
Line: 621
Table: XRF_EntityAttributeValue
Content: SELECT ID FROM XRF_EntityAttributeValue

File: Self_Healer\core\knowledge_integration.py
Line: 631
Table: XRF_EntityAttributeValue
Content: INSERT INTO XRF_EntityAttributeValue (Name, EntityID, AttributeID, EntityValueID)

File: Self_Healer\dashboard\dashboard.py
Line: 880
Table: XRF_EntityAttributeValue
Content: FROM XRF_EntityAttributeValue eav

File: Self_Healer\dashboard\dashboard.py
Line: 881
Table: REF_Attribute
Content: JOIN REF_Attribute a ON eav.AttributeID = a.ID

File: Self_Healer\dashboard\dashboard.py
Line: 882
Table: REF_EntityValue
Content: JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID

File: Self_Healer\dashboard\dashboard.py
Line: 928
Table: REF_Evidence
Content: FROM REF_Evidence e

File: Self_Healer\dashboard\dashboard.py
Line: 1026
Table: XRF_EntityAttributeValue
Content: SELECT 1 FROM XRF_EntityAttributeValue eav

File: Self_Healer\dashboard\dashboard.py
Line: 1027
Table: REF_Attribute
Content: JOIN REF_Attribute a ON eav.AttributeID = a.ID

File: Self_Healer\dashboard\dashboard.py
Line: 1028
Table: REF_EntityValue
Content: JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID

File: Self_Healer\database\check_schema.py
Line: 33
Table: XRF_EntityAttributeValue
Content: xrf_query = "SELECT TOP 5 * FROM XRF_EntityAttributeValue"

File: Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql
Line: 48
Table: REF_Attribute
Content: SELECT * FROM REF_Attribute ORDER BY Name

File: Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql
Line: 63
Table: XRF_EntityAttributeValue
Content: FROM XRF_EntityAttributeValue eav

File: Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql
Line: 64
Table: REF_Attribute
Content: JOIN REF_Attribute a ON eav.AttributeID = a.ID

File: Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql
Line: 65
Table: REF_EntityValue
Content: JOIN REF_EntityValue ev ON eav.EntityValueID = ev.ID

File: Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql
Line: 235
Table: REF_Evidence
Content: LEFT JOIN REF_Evidence e ON f.ID = e.FactID

File: Self_Healer\Documentation\DB_Admin\create_selfhealer_procedures.sql
Line: 341
Table: REF_Evidence
Content: FROM REF_Evidence e

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 67
Table: XRF_EntityAttributeValue
Content: LEFT JOIN XRF_EntityAttributeValue eav_duration ON e.ID = eav_duration.EntityID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 68
Table: REF_Attribute
Content: LEFT JOIN REF_Attribute attr_duration ON eav_duration.AttributeID = attr_duration.ID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 70
Table: REF_EntityValue
Content: LEFT JOIN REF_EntityValue duration_val ON eav_duration.ValueID = duration_val.ID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 72
Table: XRF_EntityAttributeValue
Content: LEFT JOIN XRF_EntityAttributeValue eav_success ON e.ID = eav_success.EntityID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 73
Table: REF_Attribute
Content: LEFT JOIN REF_Attribute attr_success ON eav_success.AttributeID = attr_success.ID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 75
Table: REF_EntityValue
Content: LEFT JOIN REF_EntityValue success_val ON eav_success.ValueID = success_val.ID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 136
Table: XRF_EntityAttributeValue
Content: LEFT JOIN XRF_EntityAttributeValue eav ON e.ID = eav.EntityID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 137
Table: REF_Attribute
Content: LEFT JOIN REF_Attribute attr ON eav.AttributeID = attr.ID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 138
Table: REF_EntityValue
Content: LEFT JOIN REF_EntityValue val ON eav.EntityValueID = val.ID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 205
Table: XRF_EntityAttributeValue
Content: LEFT JOIN XRF_EntityAttributeValue eav_success ON e.ID = eav_success.EntityID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 206
Table: REF_Attribute
Content: LEFT JOIN REF_Attribute attr_success ON eav_success.AttributeID = attr_success.ID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 208
Table: REF_EntityValue
Content: LEFT JOIN REF_EntityValue success_val ON eav_success.EntityValueID = success_val.ID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 210
Table: XRF_EntityAttributeValue
Content: LEFT JOIN XRF_EntityAttributeValue eav_duration ON e.ID = eav_duration.EntityID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 211
Table: REF_Attribute
Content: LEFT JOIN REF_Attribute attr_duration ON eav_duration.AttributeID = attr_duration.ID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 213
Table: REF_EntityValue
Content: LEFT JOIN REF_EntityValue duration_val ON eav_duration.EntityValueID = duration_val.ID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 278
Table: XRF_EntityAttributeValue
Content: LEFT JOIN XRF_EntityAttributeValue eav_duration ON e.ID = eav_duration.EntityID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 279
Table: REF_Attribute
Content: LEFT JOIN REF_Attribute attr_duration ON eav_duration.AttributeID = attr_duration.ID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 281
Table: REF_EntityValue
Content: LEFT JOIN REF_EntityValue duration_val ON eav_duration.ValueID = duration_val.ID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 283
Table: XRF_EntityAttributeValue
Content: LEFT JOIN XRF_EntityAttributeValue eav_success ON e.ID = eav_success.EntityID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 284
Table: REF_Attribute
Content: LEFT JOIN REF_Attribute attr_success ON eav_success.AttributeID = attr_success.ID

File: Self_Healer\Documentation\DB_Admin\create_session_procedures.sql
Line: 286
Table: REF_EntityValue
Content: LEFT JOIN REF_EntityValue success_val ON eav_success.EntityValueID = success_val.ID

File: Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql
Line: 65
Table: REF_EntityValue
Content: UPDATE REF_EntityValue

File: Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql
Line: 82
Table: REF_EntityValue
Content: UPDATE REF_EntityValue

File: Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql
Line: 102
Table: REF_EntityValue
Content: UPDATE REF_EntityValue

File: Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql
Line: 120
Table: REF_EntityValue
Content: UPDATE REF_EntityValue

File: Self_Healer\Documentation\DB_Admin\schema_enhancement_numeric_values.sql
Line: 149
Table: REF_EntityValue
Content: FROM REF_EntityValue

File: Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql
Line: 62
Table: XRF_EntityAttributeValue
Content: LEFT JOIN XRF_EntityAttributeValue eav_success ON e.ID = eav_success.EntityID

File: Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql
Line: 63
Table: REF_Attribute
Content: LEFT JOIN REF_Attribute attr_success ON eav_success.AttributeID = attr_success.ID

File: Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql
Line: 65
Table: REF_EntityValue
Content: LEFT JOIN REF_EntityValue success_val ON eav_success.EntityValueID = success_val.ID

File: Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql
Line: 67
Table: XRF_EntityAttributeValue
Content: LEFT JOIN XRF_EntityAttributeValue eav_duration ON e.ID = eav_duration.EntityID

File: Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql
Line: 68
Table: REF_Attribute
Content: LEFT JOIN REF_Attribute attr_duration ON eav_duration.AttributeID = attr_duration.ID

File: Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql
Line: 70
Table: REF_EntityValue
Content: LEFT JOIN REF_EntityValue duration_val ON eav_duration.EntityValueID = duration_val.ID

File: Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql
Line: 126
Table: XRF_EntityAttributeValue
Content: LEFT JOIN XRF_EntityAttributeValue eav_duration ON e.ID = eav_duration.EntityID

File: Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql
Line: 127
Table: REF_Attribute
Content: LEFT JOIN REF_Attribute attr_duration ON eav_duration.AttributeID = attr_duration.ID

File: Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql
Line: 129
Table: REF_EntityValue
Content: LEFT JOIN REF_EntityValue duration_val ON eav_duration.EntityValueID = duration_val.ID

File: Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql
Line: 131
Table: XRF_EntityAttributeValue
Content: LEFT JOIN XRF_EntityAttributeValue eav_success ON e.ID = eav_success.EntityID

File: Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql
Line: 132
Table: REF_Attribute
Content: LEFT JOIN REF_Attribute attr_success ON eav_success.AttributeID = attr_success.ID

File: Self_Healer\Documentation\DB_Admin\update_procedures_numeric_values.sql
Line: 134
Table: REF_EntityValue
Content: LEFT JOIN REF_EntityValue success_val ON eav_success.EntityValueID = success_val.ID

File: tests\test_numeric_values_enhancement.py
Line: 53
Table: REF_EntityValues
Content: FROM REF_EntityValues

File: tests\test_numeric_values_enhancement.py
Line: 72
Table: REF_EntityValues
Content: FROM REF_EntityValues

File: tests\test_numeric_values_enhancement.py
Line: 151
Table: REF_EntityValues
Content: FROM REF_EntityValues

