{"name": "Local_IPConfig_Scan", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 60}]}}, "id": "7c79fcdb-04e1-4d19-9e01-e4fa25f31c61", "name": "Every 60 Minutes", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.1, "position": [-2300, -220]}, {"parameters": {"command": "stat -c '%Y' /home/<USER>/shared/dns_reports/setup/dns_cache_output.txt 2>/dev/null || echo 'FILE_NOT_FOUND'"}, "id": "d6ea2275-86a0-47ee-989f-4be573ac8cf8", "name": "Check File Age", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-2080, -220]}, {"parameters": {"jsCode": "// Validate DNS cache file freshness and stop if stale\nconst input = $input.first().json;\nconst fileCheckOutput = input.stdout?.trim();\n\nconsole.log('=== DNS CACHE FILE FRESHNESS CHECK ===');\nconsole.log('File check output:', fileCheckOutput);\n\nconst currentTime = Math.floor(Date.now() / 1000); // Unix timestamp\nconst maxAgeMinutes = 65; // File must be newer than 65 minutes (Windows task runs every 60 min)\nconst maxAgeSeconds = maxAgeMinutes * 60;\n\nlet fileStatus = {\n  exists: false,\n  ageMinutes: 0,\n  isValid: false,\n  error: null\n};\n\nif (fileCheckOutput === 'FILE_NOT_FOUND' || !fileCheckOutput || fileCheckOutput.includes('No such file')) {\n  fileStatus.error = 'DNS cache file does not exist';\n  console.log('❌ CRITICAL ERROR: DNS cache file not found');\n  console.log('   Expected: /home/<USER>/shared/dns_reports/setup/dns_cache_output.txt');\n  console.log('   This indicates Windows automation is not working');\n  \n} else {\n  try {\n    const fileTimestamp = parseInt(fileCheckOutput);\n    const fileAge = currentTime - fileTimestamp;\n    fileStatus.ageMinutes = Math.floor(fileAge / 60);\n    fileStatus.exists = true;\n    \n    console.log(`File timestamp: ${fileTimestamp} (${new Date(fileTimestamp * 1000).toISOString()})`);\n    console.log(`Current time: ${currentTime} (${new Date(currentTime * 1000).toISOString()})`);\n    console.log(`File age: ${fileStatus.ageMinutes} minutes`);\n    \n    if (fileAge <= maxAgeSeconds) {\n      fileStatus.isValid = true;\n      console.log('✅ DNS cache file is fresh and valid');\n    } else {\n      fileStatus.error = `DNS cache file is too old (${fileStatus.ageMinutes} minutes, max ${maxAgeMinutes})`;\n      console.log(`❌ CRITICAL ERROR: DNS cache file is stale`);\n      console.log(`   File age: ${fileStatus.ageMinutes} minutes (maximum allowed: ${maxAgeMinutes})`);\n      console.log(`   This indicates Windows automation has stopped working`);\n    }\n  } catch (error) {\n    fileStatus.error = `Error parsing file timestamp: ${error.message}`;\n    console.log('❌ CRITICAL ERROR: Cannot parse file timestamp');\n    console.log('   Raw output:', fileCheckOutput);\n  }\n}\n\nif (!fileStatus.isValid) {\n  console.log('\\n🚨 AUTOMATION FAILURE DETECTED 🚨');\n  console.log('The DNS Security Monitor automation is not working properly.');\n  console.log('\\nRequired Actions:');\n  console.log('1. Check Windows Task Scheduler: .\\\\Scripts\\\\setup_dns_automation.ps1 -Status');\n  console.log('2. Manually run: .\\\\Scripts\\\\get_dns_cache.ps1');\n  console.log('3. Verify Docker volume mount is working');\n  console.log('4. Check PowerShell execution policy');\n  console.log('\\nWorkflow will generate ERROR REPORT instead of security analysis.');\n  \n  // Return error status that will trigger error report generation\n  return [{\n    json: {\n      systemError: true,\n      errorType: 'DNS_CACHE_AUTOMATION_FAILURE',\n      errorMessage: fileStatus.error,\n      fileStatus: fileStatus,\n      troubleshooting: {\n        checkTaskScheduler: '.\\\\Scripts\\\\setup_dns_automation.ps1 -Status',\n        manualCollection: '.\\\\Scripts\\\\get_dns_cache.ps1',\n        setupAutomation: '.\\\\Scripts\\\\setup_dns_monitoring.ps1'\n      },\n      timestamp: new Date().toISOString()\n    }\n  }];\n}\n\n// File is valid, proceed with normal workflow\nconsole.log('DNS cache file validation passed - proceeding with analysis');\nreturn [{\n  json: {\n    systemError: false,\n    fileStatus: fileStatus,\n    validationPassed: true,\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "792a7616-9226-4764-a8fe-1fbfe36f7f5b", "name": "Validate DNS Cache", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1860, -220]}, {"parameters": {"filePath": "/home/<USER>/shared/dns_reports/setup/dns_cache_output.txt"}, "id": "a6e84b7a-2817-451c-aeaa-aa7c3a5c6426", "name": "Read DNS File", "type": "n8n-nodes-base.readBinaryFile", "typeVersion": 1, "position": [-2420, 120]}, {"parameters": {"jsCode": "// Process DNS cache data from file or provide fallback\nconst input = $input.first();\n\nconsole.log('=== PROCESSING DNS CACHE DATA ===');\n\nlet dnsOutput = '';\nlet source = 'unknown';\nlet status = 'success';\n\n// Check if we have binary data from the file read\nif (input.binary && input.binary.data) {\n  try {\n    // Convert binary data to text\n    const binaryData = input.binary.data;\n    dnsOutput = Buffer.from(binaryData.data, 'base64').toString('utf8');\n    source = 'windows_dns_cache_file';\n    console.log(`SUCCESS: DNS cache file read successfully`);\n    console.log(`   Content length: ${dnsOutput.length} characters`);\n    console.log(`   Lines: ${dnsOutput.split('\\n').length}`);\n    \n    // Show sample domains found\n    const domainMatches = dnsOutput.match(/Record Name[^:]*:[^\\n]*/g);\n    if (domainMatches && domainMatches.length > 0) {\n      console.log(`   Domains found: ${domainMatches.length}`);\n      console.log(`   Sample: ${domainMatches.slice(0, 3).map(m => m.split(':')[1]?.trim()).join(', ')}`);\n    }\n    \n  } catch (error) {\n    console.log('ERROR: Error processing DNS file data:', error.message);\n    dnsOutput = '';\n    status = 'file_read_error';\n  }\n}\n\n// If no valid DNS data, provide fallback with instructions\nif (!dnsOutput || dnsOutput.length < 100) {\n  console.log('WARNING: No valid DNS cache data found. Using fallback.');\n  console.log('   Batch file may have failed to collect DNS data');\n  console.log('   Check batch file execution logs above');\n  \n  source = 'fallback_mock_data';\n  status = 'using_fallback';\n  \n  dnsOutput = `DNS Cache Collection Failed - Using Mock Data\n\nBatch file execution may have failed.\nCheck N8N console logs for batch file output.\n\nMock DNS data for testing:\n\n    example.com\n    ----------------------------------------\n    Record Name . . . . . : example.com\n    Record Type . . . . . : 1\n    Time To Live  . . . . : 300\n    Data Length . . . . . : 4\n    Section . . . . . . . : Answer\n    A (Host) Record . . . : *************\n\n    google.com\n    ----------------------------------------\n    Record Name . . . . . : google.com\n    Record Type . . . . . : 1\n    Time To Live  . . . . : 300\n    Data Length . . . . . : 4\n    Section . . . . . . . : Answer\n    A (Host) Record . . . : **************\n\n    github.com\n    ----------------------------------------\n    Record Name . . . . . : github.com\n    Record Type . . . . . : 1\n    Time To Live  . . . . : 300\n    Data Length . . . . . : 4\n    Section . . . . . . . : Answer\n    A (Host) Record . . . : ************\n\n    suspicious-test-domain.com\n    ----------------------------------------\n    Record Name . . . . . : suspicious-test-domain.com\n    Record Type . . . . . : 1\n    Time To Live  . . . . : 300\n    Data Length . . . . . : 4\n    Section . . . . . . . : Answer\n    A (Host) Record . . . : *************`;\n}\n\nreturn [{\n  json: {\n    stdout: dnsOutput,\n    stderr: status === 'success' ? '' : 'Using fallback data - batch file may have failed',\n    exitCode: status === 'success' ? 0 : 1,\n    command: 'dns_cache_reader',\n    source: source,\n    status: status,\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "2b016c3b-7d0f-4f23-9079-fedae2349d91", "name": "Process DNS Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2240, 120]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.systemError }}", "value2": true}]}}, "id": "94503f43-50e9-4e6a-8335-0fbf7cc72b76", "name": "System Error?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-1660, -220]}, {"parameters": {"jsCode": "// Generate error report for system failures\nconst data = $input.first().json;\n\nconsole.log('=== GENERATING SYSTEM ERROR REPORT ===');\nconsole.log('Error Type:', data.errorType);\nconsole.log('Error Message:', data.errorMessage);\n\nconst errorDate = new Date().toLocaleDateString('en-CA'); // YYYY-MM-DD format in local timezone\nconst fileName = `dns_system_error_report_${errorDate}.html`;\n\n// Generate error report HTML\nconst errorReport = `<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>DNS Security Monitor - System Error</title>\n    <style>\n        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }\n        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n        .error-header { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 30px; }\n        .error-header h1 { margin: 0; font-size: 2.5em; }\n        .error-header p { margin: 10px 0 0 0; opacity: 0.9; }\n        .error-details { background: #fff5f5; border: 2px solid #dc3545; padding: 20px; border-radius: 10px; margin: 20px 0; }\n        .error-details h3 { color: #dc3545; margin-top: 0; }\n        .troubleshooting { background: #f8f9fa; border-left: 5px solid #007bff; padding: 20px; margin: 20px 0; }\n        .troubleshooting h3 { color: #007bff; margin-top: 0; }\n        .command { background: #f1f3f4; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }\n        .status-info { background: #e8f5e8; border: 1px solid #4caf50; padding: 15px; border-radius: 5px; margin: 20px 0; }\n        .footer { margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px; text-align: center; color: #666; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"error-header\">\n            <h1>🚨 DNS Security Monitor - System Error</h1>\n            <p>Automation Failure Detected: ${new Date().toLocaleString()}</p>\n        </div>\n        \n        <div class=\"error-details\">\n            <h3>❌ Critical System Error</h3>\n            <p><strong>Error Type:</strong> ${data.errorType}</p>\n            <p><strong>Error Message:</strong> ${data.errorMessage}</p>\n            <p><strong>Impact:</strong> DNS security monitoring is not functioning. No real DNS analysis can be performed.</p>\n        </div>\n        \n        <div class=\"troubleshooting\">\n            <h3>🔧 Required Actions</h3>\n            <p>The DNS cache automation system has failed. Please follow these steps to restore functionality:</p>\n            \n            <h4>Step 1: Check Task Scheduler Status</h4>\n            <div class=\"command\">${data.troubleshooting.checkTaskScheduler}</div>\n            \n            <h4>Step 2: Manual DNS Collection (Immediate Fix)</h4>\n            <div class=\"command\">${data.troubleshooting.manualCollection}</div>\n            \n            <h4>Step 3: Restart Automation (If Needed)</h4>\n            <div class=\"command\">${data.troubleshooting.setupAutomation}</div>\n            \n            <h4>Step 4: Verify File Creation</h4>\n            <p>After running the manual collection, verify that this file exists and is recent:</p>\n            <div class=\"command\">data\\\\dns_reports\\\\setup\\\\dns_cache_output.txt</div>\n        </div>\n        \n        ${data.fileStatus ? `\n        <div class=\"status-info\">\n            <h3>📊 File Status Details</h3>\n            <p><strong>File Exists:</strong> ${data.fileStatus.exists ? 'Yes' : 'No'}</p>\n            ${data.fileStatus.ageMinutes ? `<p><strong>File Age:</strong> ${data.fileStatus.ageMinutes} minutes</p>` : ''}\n            <p><strong>Maximum Allowed Age:</strong> 65 minutes</p>\n            <p><strong>Expected Update Frequency:</strong> Every 55 minutes via Windows Task Scheduler</p>\n        </div>\n        ` : ''}\n        \n        <div class=\"footer\">\n            <p><strong>DNS Security Monitor - System Error Report</strong></p>\n            <p>Generated: ${new Date().toLocaleString()}</p>\n            <p>This report will be replaced with normal security analysis once the automation is restored.</p>\n        </div>\n    </div>\n</body>\n</html>`;\n\nconsole.log(`Error report generated: ${fileName}`);\nconsole.log(`Report size: ${errorReport.length} characters`);\n\nreturn [{\n  json: {\n    htmlReport: errorReport,\n    htmlContent: errorReport,\n    fileName: fileName,\n    isErrorReport: true,\n    errorType: data.errorType,\n    errorMessage: data.errorMessage,\n    timestamp: new Date().toISOString()\n  }\n}];"}, "id": "738cf34b-2068-4b1a-acc4-d5edf1156bb8", "name": "Generate Error Report", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1420, -220]}, {"parameters": {"jsCode": "// Parse DNS cache output to extract domain names\nconst dnsOutput = $input.first().json.stdout || '';\nconst domains = new Set();\n\nconsole.log('=== PARSING DNS DOMAINS ===');\nconsole.log('DNS output length:', dnsOutput.length);\n\n// Split output into lines and process\nconst lines = dnsOutput.split('\\n');\nfor (const line of lines) {\n  // Look for \"Record Name\" lines which contain domain names\n  if (line.includes('Record Name')) {\n    const parts = line.split(':');\n    if (parts.length > 1) {\n      const domain = parts[1].trim();\n      // Filter out localhost, empty, and IP addresses\n      if (domain && \n          !domain.includes('localhost') && \n          !domain.includes('127.0.0.1') &&\n          !domain.match(/^\\d+\\.\\d+\\.\\d+\\.\\d+$/) &&\n          domain.includes('.')) {\n        domains.add(domain.toLowerCase());\n      }\n    }\n  }\n}\n\n// Convert to array and return as separate items\nconst domainList = Array.from(domains);\nconsole.log(`Found ${domainList.length} unique domains:`, domainList);\n\nreturn domainList.map(domain => ({ \n  domain,\n  timestamp: new Date().toISOString()\n}));"}, "id": "077fdb99-78db-4293-a48b-af51651781a2", "name": "Parse DNS Domains", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2060, 120]}, {"parameters": {"jsCode": "// Update domain history tracking in CSV file\nconst domains = $input.all();\nconst currentTime = new Date().toISOString();\n\nconsole.log('=== UPDATING DOMAIN HISTORY ===');\nconsole.log(`Processing ${domains.length} domains for history tracking`);\n\n// Note: This is a placeholder for domain history tracking\n// In a future version, this would:\n// 1. Read existing domain history CSV file\n// 2. Update last_seen dates for existing domains\n// 3. Add new domains with first_seen dates\n// 4. Increment occurrence counts\n// 5. Write updated CSV back to file\n\nconst historyFilePath = '/home/<USER>/shared/dns_reports/setup/dns_domain_history.csv';\nconsole.log('Domain history file:', historyFilePath);\n\n// Process each domain for history tracking preparation\nconst historyUpdates = [];\n\nfor (const domainItem of domains) {\n  const domain = domainItem.json.domain.toLowerCase();\n  const timestamp = domainItem.json.timestamp;\n  \n  // Prepare history record structure (for future implementation)\n  const historyRecord = {\n    domain_name: domain,\n    ip_address: '', // Would be extracted from DNS data\n    first_seen: timestamp,\n    last_seen: timestamp,\n    occurrence_count: 1,\n    current_risk_level: 'LOW', // Will be updated after security analysis\n    highest_risk_level: 'LOW',\n    threat_categories: '',\n    dns_record_type: 'A',\n    created_date: timestamp,\n    modified_date: timestamp\n  };\n  \n  historyUpdates.push(historyRecord);\n  console.log(`Prepared history tracking for: ${domain}`);\n}\n\nconsole.log(`Domain history tracking prepared for ${historyUpdates.length} domains`);\nconsole.log('Note: CSV file updates will be implemented via PowerShell script integration');\n\n// Pass through all domains for continued processing\nreturn domains.map(item => ({\n  json: {\n    ...item.json,\n    historyTracked: true,\n    historyTimestamp: currentTime\n  }\n}));"}, "id": "0e887671-24a1-4b45-a4b5-48e45d3770ae", "name": "Update Domain History", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1880, 120]}, {"parameters": {"jsCode": "// Pattern-based security analysis for domains\nconst domain = $input.first().json.domain;\nconsole.log(`Analyzing domain: ${domain}`);\n\n// Simple domain analysis based on patterns and characteristics\nlet riskLevel = 'LOW';\nlet threats = [];\nlet details = 'Domain analysis completed';\nlet suspiciousScore = 0;\n\n// Check for suspicious domain patterns\nconst highRiskPatterns = [\n  /suspicious/i, /malware/i, /phish/i, /hack/i, /virus/i, \n  /spam/i, /scam/i, /fake/i, /fraud/i, /trojan/i\n];\n\nconst mediumRiskPatterns = [\n  /test/i, /temp/i, /demo/i, /sample/i, /example/i,\n  /\\d{1,3}-\\d{1,3}-\\d{1,3}-\\d{1,3}/i, // IP-like patterns\n  /[0-9]{8,}/i // Long number sequences\n];\n\n// Check domain length and structure\nconst domainParts = domain.split('.');\nconst domainName = domainParts[0];\n\n// High-risk pattern checks\nfor (const pattern of highRiskPatterns) {\n  if (pattern.test(domain)) {\n    riskLevel = 'HIGH';\n    threats.push('Suspicious domain name pattern detected');\n    suspiciousScore += 3;\n    break;\n  }\n}\n\n// Medium-risk pattern checks (only if not already high risk)\nif (riskLevel !== 'HIGH') {\n  for (const pattern of mediumRiskPatterns) {\n    if (pattern.test(domain)) {\n      riskLevel = 'MEDIUM';\n      threats.push('Potentially suspicious domain characteristics');\n      suspiciousScore += 1;\n      break;\n    }\n  }\n}\n\n// Check for very long domain names (potential DGA)\nif (domainName.length > 20) {\n  if (riskLevel === 'LOW') riskLevel = 'MEDIUM';\n  threats.push('Unusually long domain name');\n  suspiciousScore += 1;\n}\n\n// Check for excessive subdomains\nif (domainParts.length > 4) {\n  if (riskLevel === 'LOW') riskLevel = 'MEDIUM';\n  threats.push('Multiple subdomains detected');\n  suspiciousScore += 1;\n}\n\n// Check for known good domains (whitelist)\nconst knownGoodDomains = [\n  'google.com', 'microsoft.com', 'github.com', 'stackoverflow.com',\n  'amazon.com', 'facebook.com', 'twitter.com', 'linkedin.com',\n  'youtube.com', 'wikipedia.org', 'apple.com', 'adobe.com'\n];\n\nconst isKnownGood = knownGoodDomains.some(goodDomain => \n  domain.toLowerCase().includes(goodDomain.toLowerCase())\n);\n\nif (isKnownGood && riskLevel !== 'HIGH') {\n  riskLevel = 'LOW';\n  threats = []; // Clear any medium-risk flags for known good domains\n  details = 'Known legitimate domain';\n  suspiciousScore = 0;\n}\n\n// Set final details\nif (riskLevel === 'HIGH') {\n  details = `High-risk domain detected (score: ${suspiciousScore})`;\n} else if (riskLevel === 'MEDIUM') {\n  details = `Potentially suspicious domain (score: ${suspiciousScore})`;\n} else {\n  details = 'Domain appears legitimate';\n}\n\n// Add some realistic-looking metadata\nconst analysisResult = {\n  domain: domain,\n  riskLevel: riskLevel,\n  threats: threats,\n  details: details,\n  suspiciousScore: suspiciousScore,\n  analysisMethod: 'pattern_based',\n  timestamp: new Date().toISOString(),\n  // Simulate security engine results\n  positives: suspiciousScore,\n  total: 10, // Simulated total engines\n  engines_flagged: threats.length > 0 ? ['Pattern Analysis', 'Domain Structure Check'] : [],\n  clean_engines: threats.length === 0 ? ['Pattern Analysis', 'Domain Structure Check', 'Whitelist Check'] : ['Whitelist Check']\n};\n\nconsole.log(`Domain analysis result:`, JSON.stringify(analysisResult, null, 2));\n\nreturn [{\n  json: {\n    ...analysisResult,\n    // Keep original domain for next node\n    originalDomain: domain\n  }\n}];"}, "id": "f85c4c5f-1278-47f2-a1b5-b76096a850d4", "name": "Check Domain Security", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1680, 120]}, {"parameters": {"jsCode": "// Analyze pattern-based security data and assess risk\nconst items = $input.all();\nconst analysis = [];\nconst highRiskDomains = [];\n\nconsole.log(`Processing ${items.length} domain security analyses`);\n\nfor (const item of items) {\n  // The security check already did the analysis, just extract the results\n  const securityResult = item.json;\n  \n  const domainAnalysis = {\n    domain: securityResult.domain || securityResult.originalDomain,\n    riskLevel: securityResult.riskLevel,\n    threats: securityResult.threats || [],\n    details: securityResult.details,\n    positives: securityResult.positives || securityResult.suspiciousScore || 0,\n    total: securityResult.total || 10,\n    timestamp: new Date().toISOString(),\n    source: 'Pattern Analysis',\n    analysisMethod: securityResult.analysisMethod || 'pattern_based',\n    engines_flagged: securityResult.engines_flagged || [],\n    clean_engines: securityResult.clean_engines || []\n  };\n  \n  analysis.push(domainAnalysis);\n  \n  if (domainAnalysis.riskLevel === 'HIGH') {\n    highRiskDomains.push(domainAnalysis);\n  }\n  \n  console.log(`Domain: ${domainAnalysis.domain} - Risk: ${domainAnalysis.riskLevel} - Threats: ${domainAnalysis.threats.length}`);\n}\n\nconsole.log(`Analysis complete: ${analysis.length} domains processed, ${highRiskDomains.length} high-risk domains found`);\n\n// Log high-risk domains for debugging\nif (highRiskDomains.length > 0) {\n  console.log('HIGH RISK DOMAINS DETECTED:');\n  highRiskDomains.forEach(domain => {\n    console.log(`  - ${domain.domain}: ${domain.details}`);\n    console.log(`    Threats: ${domain.threats.join(', ')}`);\n  });\n}\n\nreturn [{\n  analysis,\n  highRiskDomains,\n  totalDomains: analysis.length,\n  highRiskCount: highRiskDomains.length,\n  hasHighRisk: highRiskDomains.length > 0,\n  reportTimestamp: new Date().toISOString()\n}];"}, "id": "62b1fd46-a616-4ff2-bff4-1dce558f1eb3", "name": "Analyze Security Risk", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1460, 120]}, {"parameters": {"jsCode": "// Generate comprehensive HTML security report\nconst data = $input.first().json;\nconst { analysis, highRiskDomains, totalDomains, highRiskCount, reportTimestamp } = data;\n\nconsole.log('=== GENERATING HTML REPORT ===');\nconsole.log(`Total domains: ${totalDomains}`);\nconsole.log(`High risk domains: ${highRiskCount}`);\n\n// Generate report filename\nconst reportDate = new Date().toLocaleDateString('en-CA'); // YYYY-MM-DD format in local timezone\nconst fileName = `dns_security_report_${reportDate}.html`;\n\n// Count domains by risk level\nconst riskCounts = {\n  HIGH: analysis.filter(d => d.riskLevel === 'HIGH').length,\n  MEDIUM: analysis.filter(d => d.riskLevel === 'MEDIUM').length,\n  LOW: analysis.filter(d => d.riskLevel === 'LOW').length\n};\n\n// Generate HTML report\nconst htmlReport = `<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>DNS Security Report - ${new Date().toLocaleDateString()}</title>\n    <style>\n        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }\n        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 30px; }\n        .header h1 { margin: 0; font-size: 2.5em; }\n        .header p { margin: 10px 0 0 0; opacity: 0.9; }\n        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }\n        .summary-card { background: #f8f9fa; border-left: 5px solid #28a745; padding: 20px; border-radius: 5px; }\n        .summary-card.high-risk { border-left-color: #dc3545; background: #fff5f5; }\n        .summary-card.medium-risk { border-left-color: #ffc107; background: #fffbf0; }\n        .summary-card h3 { margin: 0 0 10px 0; color: #333; }\n        .summary-card .number { font-size: 2em; font-weight: bold; color: #666; }\n        .high-risk { color: #d32f2f; font-weight: bold; }\n        .medium-risk { color: #f57c00; font-weight: bold; }\n        .low-risk { color: #388e3c; font-weight: bold; }\n        .alert { background: #ffebee; border: 1px solid #f44336; padding: 15px; margin: 20px 0; border-radius: 5px; }\n        table { border-collapse: collapse; width: 100%; margin-top: 20px; background: white; }\n        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }\n        th { background-color: #f2f2f2; font-weight: bold; }\n        tr:nth-child(even) { background-color: #f9f9f9; }\n        .footer { margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px; text-align: center; color: #666; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>🛡️ DNS Security Monitoring Report</h1>\n            <p>Generated: ${new Date().toLocaleString()}</p>\n        </div>\n        \n        <div class=\"summary\">\n            <div class=\"summary-card ${highRiskCount > 0 ? 'high-risk' : ''}\">\n                <h3>High Risk Domains</h3>\n                <div class=\"number\">${riskCounts.HIGH}</div>\n            </div>\n            <div class=\"summary-card ${riskCounts.MEDIUM > 0 ? 'medium-risk' : ''}\">\n                <h3>Medium Risk Domains</h3>\n                <div class=\"number\">${riskCounts.MEDIUM}</div>\n            </div>\n            <div class=\"summary-card\">\n                <h3>Low Risk Domains</h3>\n                <div class=\"number\">${riskCounts.LOW}</div>\n            </div>\n            <div class=\"summary-card\">\n                <h3>Total Domains Analyzed</h3>\n                <div class=\"number\">${totalDomains}</div>\n            </div>\n        </div>\n        \n        ${highRiskCount > 0 ? `\n        <div class=\"alert\">\n            <h3>⚠️ Security Alert</h3>\n            <p><strong>${highRiskCount} high-risk domain(s) detected!</strong> Immediate review recommended.</p>\n        </div>\n        ` : `\n        <div style=\"background: #e8f5e8; border: 1px solid #4caf50; padding: 15px; margin: 20px 0; border-radius: 5px;\">\n            <h3>✅ All Clear</h3>\n            <p>No high-risk domains detected in this scan.</p>\n        </div>\n        `}\n        \n        <h2>📊 Detailed Analysis</h2>\n        <table>\n            <thead>\n                <tr>\n                    <th>Domain</th>\n                    <th>Risk Level</th>\n                    <th>Security Score</th>\n                    <th>Details</th>\n                    <th>Threats</th>\n                </tr>\n            </thead>\n            <tbody>\n                ${analysis.map(domain => `\n                <tr>\n                    <td>${domain.domain}</td>\n                    <td><span class=\"${domain.riskLevel.toLowerCase()}-risk\">${domain.riskLevel}</span></td>\n                    <td>${domain.positives}/${domain.total}</td>\n                    <td>${domain.details}</td>\n                    <td>${domain.threats.join(', ') || 'None detected'}</td>\n                </tr>\n                `).join('')}\n            </tbody>\n        </table>\n        \n        <div class=\"footer\">\n            <p><strong>Report generated by N8N DNS Security Monitor</strong></p>\n            <p>Next scan scheduled in 60 minutes</p>\n            <p>Report saved: ${fileName}</p>\n        </div>\n    </div>\n</body>\n</html>`;\n\nconsole.log(`HTML report generated: ${fileName}`);\nconsole.log(`Report size: ${htmlReport.length} characters`);\n\nreturn [{\n  htmlReport,\n  htmlContent: htmlReport,\n  fileName,\n  hasHighRisk: highRiskCount > 0,\n  highRiskCount,\n  totalDomains,\n  reportData: data\n}];"}, "id": "82b6af0a-c215-41ae-82d0-2d590cbcc87f", "name": "Generate HTML Report", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1240, 120]}, {"parameters": {"jsCode": "// Prepare HTML report data for N8N Write Binary File node\nconst data = $input.first().json;\nconst fileName = data.fileName;\nconst htmlContent = data.htmlContent || data.htmlReport;\n\nconsole.log('=== PREPARING HTML REPORT FOR FILE SAVE ===');\nconsole.log(`File name: ${fileName}`);\nconsole.log(`Content length: ${htmlContent ? htmlContent.length : 'N/A'} characters`);\nconsole.log(`High risk domains: ${data.highRiskCount}`);\nconsole.log(`Total domains: ${data.totalDomains}`);\n\n// Convert HTML string to binary data for N8N Write Binary File node\nconst binaryData = {\n  data: Buffer.from(htmlContent, 'utf8').toString('base64'),\n  mimeType: 'text/html',\n  fileName: fileName,\n  fileExtension: 'html'\n};\n\nconsole.log('✅ HTML content prepared for file writing');\nconsole.log(`   Binary data size: ${binaryData.data.length} bytes (base64)`);\nconsole.log(`   MIME type: ${binaryData.mimeType}`);\nconsole.log(`   Target: /home/<USER>/shared/dns_reports/${fileName}`);\nconsole.log(`   Host path: ./data/dns_reports/${fileName}`);\n\nreturn [{\n  json: {\n    ...data,\n    fileName: fileName,\n    binaryData: binaryData\n  },\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "13101f6d-a437-4628-b01d-77be2f09a2c2", "name": "Prepare Report Save", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1040, -220]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/dns_reports/' + $json.fileName }}", "options": {}}, "id": "1dae643b-9ab8-445a-86ea-924bea0d7886", "name": "Write Report File", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [-820, -220]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.hasHighRisk }}", "value2": true}]}}, "id": "838a8392-22f7-4934-8c1b-11c6f5b6040d", "name": "High Risk Detected?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-540, -220]}, {"parameters": {"jsCode": "// Prepare detailed alert content for high-risk domains\nconst data = $input.first().json;\nconst { reportData } = data;\nconst { highRiskDomains, totalDomains, reportTimestamp } = reportData;\n\nconsole.log('=== PREPARING SECURITY ALERT ===');\nconsole.log(`🚨 HIGH RISK DOMAINS DETECTED: ${highRiskDomains.length}`);\n\n// Generate alert filename with timestamp\nconst alertTimestamp = Date.now();\nconst alertDate = new Date().toLocaleDateString('en-CA'); // YYYY-MM-DD format in local timezone\nconst alertFileName = `DNS_SECURITY_ALERT_${alertDate}_${alertTimestamp}.txt`;\n\n// Create detailed alert content\nconst alertContent = `🚨 DNS SECURITY ALERT 🚨\nTime: ${new Date().toLocaleString()}\nHigh-Risk Domains Found: ${highRiskDomains.length}\nTotal Domains Scanned: ${totalDomains}\n\nIMMEDIATE ACTION REQUIRED:\n\n${highRiskDomains.map(domain => `⚠️  DOMAIN: ${domain.domain}\n   RISK: ${domain.riskLevel}\n   DETAILS: ${domain.details}\n   THREATS: ${domain.threats.join(', ')}\n   SCORE: ${domain.positives}/${domain.total}`).join('\\n\\n')}\n\nFull HTML Report: ${data.fileName}\nNext scan in 60 minutes.\n\n--- DNS Security Monitor ---`;\n\nconsole.log('Alert content prepared');\nconsole.log(`Alert file: ${alertFileName}`);\nconsole.log(`Content length: ${alertContent.length} characters`);\n\n// Create notification text for Windows notification\nconst notificationText = `${highRiskDomains.length} high-risk domains detected: ${highRiskDomains.map(d => d.domain).join(', ')}. Check alert file: ${alertFileName}`;\n\nreturn [{\n  json: {\n    ...data,\n    alertFileName,\n    alertContent,\n    notificationText,\n    timestamp: new Date().toLocaleString(),\n    highRiskCount: highRiskDomains.length,\n    totalDomains\n  }\n}];"}, "id": "3834bde0-3899-4b1f-b37e-675ff42bf977", "name": "Prepare Alert Content", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-320, -320]}, {"parameters": {"jsCode": "// Prepare alert file data for N8N Write Binary File node\nconst data = $input.first().json;\nconst alertFileName = data.alertFileName;\nconst alertContent = data.alertContent;\n\nconsole.log('=== PREPARING ALERT FILE FOR SAVE ===');\nconsole.log(`🚨 HIGH RISK DOMAINS DETECTED! 🚨`);\nconsole.log(`Alert file: ${alertFileName}`);\nconsole.log(`Content length: ${alertContent ? alertContent.length : 'N/A'}`);\n\n// Convert alert content to binary data for N8N Write Binary File node\nconst binaryData = {\n  data: Buffer.from(alertContent, 'utf8').toString('base64'),\n  mimeType: 'text/plain',\n  fileName: alertFileName,\n  fileExtension: 'txt'\n};\n\nconsole.log('✅ Alert content prepared for file writing');\nconsole.log(`   Binary data size: ${binaryData.data.length} bytes (base64)`);\nconsole.log(`   Target: /home/<USER>/shared/dns_reports/${alertFileName}`);\nconsole.log(`   Host path: ./data/dns_reports/${alertFileName}`);\n\nconsole.log('\\n📄 ALERT CONTENT:');\nconsole.log('=' * 50);\nconsole.log(alertContent);\nconsole.log('=' * 50);\n\nreturn [{\n  json: {\n    ...data,\n    alertFileName: alertFileName,\n    binaryData: binaryData\n  },\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "e14cc47b-ce3d-4df8-afe7-bac19a5f3588", "name": "Prepare <PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-100, -120]}, {"parameters": {"fileName": "={{ '/home/<USER>/shared/dns_reports/' + $json.alertFileName }}", "options": {}}, "id": "c74fe2c0-4c6b-410f-968b-65cf0b372b61", "name": "Write Alert File", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [-100, 120]}, {"parameters": {"jsCode": "// Windows notification simulation (child_process not available in N8N Code node)\nconst data = $input.first().json;\n\n// Since we can't execute PowerShell from N8N Code node, we'll log the notification details\nconsole.log('=== WINDOWS NOTIFICATION SIMULATION ===');\nconsole.log(`🚨 DNS SECURITY ALERT 🚨`);\nconsole.log(`High-risk domains detected: ${data.highRiskCount}`);\nconsole.log(`Alert file created: ${data.alertFileName}`);\nconsole.log(`Notification text: ${data.notificationText}`);\nconsole.log(`Timestamp: ${data.timestamp}`);\nconsole.log('========================================');\n\n// Create a notification record for the logs\nconst notificationRecord = {\n  type: 'windows_notification_simulation',\n  title: '🚨 DNS Security Alert',\n  message: data.notificationText,\n  alertFile: data.alertFileName,\n  highRiskCount: data.highRiskCount,\n  timestamp: data.timestamp,\n  status: 'simulated_in_n8n_logs'\n};\n\nconsole.log('Notification record:', JSON.stringify(notificationRecord, null, 2));\n\nreturn [{\n  ...data,\n  notificationRecord,\n  notificationStatus: 'logged_to_console'\n}];"}, "id": "613f059f-84ed-46e2-9a98-a7f877981845", "name": "Send Windows Notification", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-100, -320]}], "pinData": {}, "connections": {"Every 60 Minutes": {"main": [[{"node": "Check File Age", "type": "main", "index": 0}]]}, "Check File Age": {"main": [[{"node": "Validate DNS Cache", "type": "main", "index": 0}]]}, "Validate DNS Cache": {"main": [[{"node": "System Error?", "type": "main", "index": 0}]]}, "System Error?": {"main": [[{"node": "Generate Error Report", "type": "main", "index": 0}], [{"node": "Read DNS File", "type": "main", "index": 0}]]}, "Read DNS File": {"main": [[{"node": "Process DNS Data", "type": "main", "index": 0}]]}, "Process DNS Data": {"main": [[{"node": "Parse DNS Domains", "type": "main", "index": 0}]]}, "Parse DNS Domains": {"main": [[{"node": "Update Domain History", "type": "main", "index": 0}]]}, "Update Domain History": {"main": [[{"node": "Check Domain Security", "type": "main", "index": 0}]]}, "Check Domain Security": {"main": [[{"node": "Analyze Security Risk", "type": "main", "index": 0}]]}, "Analyze Security Risk": {"main": [[{"node": "Generate HTML Report", "type": "main", "index": 0}]]}, "Generate HTML Report": {"main": [[{"node": "Prepare Report Save", "type": "main", "index": 0}]]}, "Prepare Report Save": {"main": [[{"node": "Write Report File", "type": "main", "index": 0}]]}, "Write Report File": {"main": [[{"node": "High Risk Detected?", "type": "main", "index": 0}]]}, "High Risk Detected?": {"main": [[{"node": "Prepare Alert Content", "type": "main", "index": 0}]]}, "Prepare Alert Content": {"main": [[{"node": "Prepare <PERSON>", "type": "main", "index": 0}, {"node": "Send Windows Notification", "type": "main", "index": 0}]]}, "Prepare Alert Save": {"main": [[{"node": "Write Alert File", "type": "main", "index": 0}]]}, "Generate Error Report": {"main": [[{"node": "Prepare Report Save", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "a13daedc-a640-43b6-b32e-32c2da696a46", "meta": {"instanceId": "a9e00de748ec35ee88db078f832d6e48181d32e4fa741d36554310dd025f8599"}, "id": "DnEcPDQrqzFuBcTB", "tags": []}