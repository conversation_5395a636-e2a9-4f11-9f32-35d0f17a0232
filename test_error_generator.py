#!/usr/bin/env python3
"""
Deliberate Error Generator for Self-Healer Testing
This script generates a logged error that Self-Healer should detect and attempt to heal.
"""

import logging
import sys
from pathlib import Path

# Set up logging to match N8N Builder's logging configuration
log_formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s'
)

# Create logs directory if it doesn't exist
logs_dir = Path("logs")
logs_dir.mkdir(exist_ok=True)

# Configure error logging to the same file Self-Healer monitors
error_file_handler = logging.FileHandler(logs_dir / "errors.log")
error_file_handler.setFormatter(log_formatter)
error_file_handler.setLevel(logging.ERROR)

# Set up logger
logger = logging.getLogger("test_module")
logger.setLevel(logging.ERROR)
logger.addHandler(error_file_handler)

def generate_test_error():
    """Generate a deliberate error for Self-Healer testing."""
    try:
        # This will cause a deliberate error that should be detectable
        raise ValueError("Test error for Self-Healer validation - workflow failed validation")
    except Exception as e:
        # Log the error in the format Self-Healer expects
        logger.error(f"Test error for Self-Healer validation - {str(e)}")
        print(f"[SUCCESS] Test error logged: {str(e)}")
        return True

if __name__ == "__main__":
    generate_test_error()
